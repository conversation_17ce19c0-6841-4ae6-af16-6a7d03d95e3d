#!/usr/bin/env python3
"""
DC Tokens Training Script for MaIL Project
SoftREPA-style contrastive learning with DC tokens for robot action diffusion.
Based on lerobot-add_transformer's implementation.
"""

import os
import sys
import time
import logging
import random
from datetime import datetime
from pathlib import Path
import multiprocessing as mp
import pdb
import hydra
import numpy as np
import torch
import wandb
from omegaconf import DictConfig, OmegaConf
from tqdm import tqdm
import math
import shutil
from hydra.core.hydra_config import HydraConfig

# Add current project root to path (prioritize over MaIL)
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)
# Add MaIL project root to path (lower priority)
sys.path.append('/home/<USER>/work/MaIL')

from agents.utils import sim_framework_path

# Setup logging
logging.basicConfig(level=logging.INFO)
log = logging.getLogger(__name__)

# Training completed, no automatic evaluation (user can run manually)

# Register OmegaConf resolver
OmegaConf.register_new_resolver("add", lambda *numbers: sum(numbers))

def setup_logging(cfg):
    """Setup comprehensive logging system for DC training."""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_dir = Path(f"logs/dc_training/{cfg.task_suite}_{timestamp}")
    log_dir.mkdir(parents=True, exist_ok=True)
    
    # Configure loggers
    main_logger = logging.getLogger("dc_main")
    training_logger = logging.getLogger("dc_training")
    
    # Clear existing handlers
    for logger in [main_logger, training_logger]:
        logger.handlers.clear()
        logger.setLevel(logging.INFO)
    
    # Create formatters
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # File handlers
    main_file_handler = logging.FileHandler(log_dir / "dc_main.log")
    main_file_handler.setFormatter(formatter)
    training_file_handler = logging.FileHandler(log_dir / "dc_training.log")
    training_file_handler.setFormatter(formatter)
    
    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
    
    # Add handlers
    main_logger.addHandler(main_file_handler)
    main_logger.addHandler(console_handler)
    training_logger.addHandler(training_file_handler)
    
    main_logger.info("=" * 60)
    main_logger.info("🚀 DC Tokens Training - Logging System Initialized")
    main_logger.info(f"📁 Log directory: {log_dir}")
    main_logger.info("=" * 60)
    
    return main_logger, training_logger, log_dir

def set_seed_everywhere(seed):
    """Set random seeds for reproducibility."""
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed_all(seed)
    np.random.seed(seed)
    random.seed(seed)
    log.info(f"🎲 Random seed set to: {seed}")

def log_system_info(logger):
    """Log system information."""
    logger.info("🖥️  System Information:")
    logger.info(f"   CUDA Available: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        logger.info(f"   CUDA Device: {torch.cuda.get_device_name()}")
        logger.info(f"   CUDA Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
    logger.info(f"   Python Version: {sys.version}")
    logger.info(f"   PyTorch Version: {torch.__version__}")
    logger.info(f"   Available CPUs: {mp.cpu_count()}")

def log_dc_configuration(cfg, logger):
    """Log DC training configuration."""
    logger.info("📝 DC Training Configuration:")
    logger.info(f"   Agent: {cfg.agent_name}")
    logger.info(f"   Task Suite: {cfg.task_suite}")
    logger.info(f"   DC Tokens: {cfg.n_dc_tokens}")
    logger.info(f"   Encoder DC Layers: {cfg.encoder_n_dc_layer}")
    logger.info(f"   Decoder DC Layers: {cfg.decoder_n_dc_layer}")
    logger.info(f"   Training Steps: {cfg.dc_training.total_steps}")
    logger.info(f"   Batch Size: {cfg.dc_training.batch_size}")
    logger.info(f"   Learning Rate: {cfg.dc_training.learning_rate}")
    logger.info(f"   Temperature: {cfg.dc_training.contrastive.temperature}")
    logger.info(f"   Scale: {cfg.dc_training.contrastive.scale}")
    logger.info(f"   Freeze Base Model: {cfg.dc_training.freeze_base_model}")
    logger.info(f"   Device: {cfg.device}")
    logger.info(f"   Seed: {cfg.seed}")

    # Log DTW contrastive learning configuration
    if hasattr(cfg, 'dtw_contrastive') and cfg.dtw_contrastive.enabled:
        logger.info("🔗 DTW Contrastive Learning Configuration:")
        logger.info(f"   DTW Enabled: {cfg.dtw_contrastive.enabled}")
        logger.info(f"   DTW File Path: {cfg.dtw_contrastive.dtw_file_path}")
        logger.info(f"   Contrastive Weight: {cfg.dtw_contrastive.contrastive_weight}")
        logger.info(f"   Positive Threshold: {cfg.dtw_contrastive.positive_threshold}")
        logger.info(f"   Distance Quantile: {cfg.dtw_contrastive.dist_quantile}")
        logger.info(f"   Use Sparse DTW: {cfg.dtw_contrastive.use_sparse_dtw}")
    else:
        logger.info("🔗 DTW Contrastive Learning: Disabled")

def setup_wandb(cfg, logger):
    """Setup Wandb logging."""
    try:
        wandb_config = OmegaConf.to_container(cfg, resolve=True, throw_on_missing=True)
        
        timestamp = datetime.now().strftime("%m%d_%H%M")
        run_name = f"dc_{cfg.agent_name}_{cfg.task_suite}_{timestamp}"
        
        init_kwargs = {
            "project": cfg.wandb.project,
            "name": run_name,
            "mode": "online",
            "config": wandb_config,
            "tags": ["dc_training", cfg.agent_name, cfg.task_suite]
        }
        
        if cfg.wandb.entity:
            init_kwargs["entity"] = cfg.wandb.entity
            
        run = wandb.init(**init_kwargs)
        logger.info(f"✅ Wandb initialized: {run_name}")
        logger.info(f"📊 Wandb URL: {run.get_url()}")
        return True
        
    except Exception as e:
        logger.warning(f"⚠️  Wandb initialization failed: {e}")
        logger.info("📊 Continuing without wandb logging")
        try:
            wandb.init(mode="disabled", project="dummy")
        except:
            pass
        return False

class DCTrainingManager:
    """Manager class for DC tokens training with comprehensive logging and monitoring."""
    
    def __init__(self, agent, cfg, main_logger, training_logger):
        self.agent = agent
        self.cfg = cfg
        self.main_logger = main_logger
        self.training_logger = training_logger
        self.start_time = None
        self.device = cfg.device  # 添加device属性，解决评估时的device访问错误
        
        # DC training parameters from config
        self.total_steps = cfg.dc_training.total_steps
        self.batch_size = cfg.dc_training.batch_size
        self.eval_freq = cfg.dc_training.eval_frequency
        self.save_freq = cfg.dc_training.save_frequency
        self.learning_rate = cfg.dc_training.learning_rate
        
        # Contrastive loss parameters
        self.temperature = cfg.dc_training.contrastive.temperature
        self.scale = cfg.dc_training.contrastive.scale
        self.diagonal_weight = cfg.dc_training.contrastive.diagonal_weight
        self.window_before = cfg.dc_training.contrastive.window_before
        self.window_after = cfg.dc_training.contrastive.window_after
        self.hard_negative_weight = cfg.dc_training.contrastive.hard_negative_weight
        
        # Setup checkpoint directory
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.checkpoint_dir = Path(f"checkpoints/dc_training/{cfg.task_suite}_{timestamp}")
        self.checkpoint_dir.mkdir(parents=True, exist_ok=True)
        
        self.main_logger.info(f"📁 Checkpoint directory: {self.checkpoint_dir}")
        
        # 确保评估时能从checkpoint目录向上找到.hydra
        try:
            run_dir = Path(HydraConfig.get().run.dir)
            hydra_src = run_dir / ".hydra"
            hydra_dst = self.checkpoint_dir / ".hydra"
            if hydra_src.exists():
                if hydra_dst.exists():
                    self.main_logger.info(f"📝 已存在 .hydra 配置于: {hydra_dst}")
                else:
                    shutil.copytree(hydra_src, hydra_dst, dirs_exist_ok=True)
                    self.main_logger.info(f"📝 已复制 Hydra 配置到 checkpoint 目录: {hydra_dst}")
            else:
                self.main_logger.warning(f"⚠️  未在运行目录找到 .hydra: {hydra_src}")
        except Exception as e:
            self.main_logger.warning(f"⚠️  复制 .hydra 配置到 checkpoint 目录失败: {e}")
    
    def setup_agent_for_dc_training(self):
        """Setup agent for DC training."""
        self.main_logger.info("🔧 Setting up agent for DC training...")
        
        # Load pretrained weights if specified
        if self.cfg.dc_training.load_pretrained_weights and self.cfg.dc_training.pretrained_model_path:
            self.main_logger.info(f"🔄 Loading pretrained weights from: {self.cfg.dc_training.pretrained_model_path}")
            # Check if the path is a file or directory
            import os
            if os.path.isfile(self.cfg.dc_training.pretrained_model_path):
                # Direct file path
                self.agent.load_pretrained_model(
                    self.cfg.dc_training.pretrained_model_path,
                    sv_name=None
                )
            else:
                # Directory path, use default filename
                self.agent.load_pretrained_model(
                    self.cfg.dc_training.pretrained_model_path,
                    sv_name="last_ddpm.pth"
                )
        
        # Initialize DC tokens
        if hasattr(self.agent.model, 'initialize_dc_tokens'):
            self.agent.model.initialize_dc_tokens()
            self.main_logger.info("✅ DC tokens initialized")
        
        # Freeze base model if specified
        if self.cfg.dc_training.freeze_base_model:
            if hasattr(self.agent.model, 'freeze_base_model'):
                self.agent.model.freeze_base_model()
                self.main_logger.info("🔒 Base model frozen for DC training")
        
        # Print parameter statistics
        if hasattr(self.agent.model, 'print_parameter_stats'):
            self.agent.model.print_parameter_stats()

        self.agent.model.window_before = self.window_before
        self.agent.model.window_after = self.window_after
        self.agent.model.hard_negative_weight = self.hard_negative_weight
        
        # 🔥 Configure Dispersive Loss
        if hasattr(self.agent, 'configure_dispersive_loss'):
            self.agent.configure_dispersive_loss(self.cfg)
            self.main_logger.info("✅ Dispersive Loss configured")
        
        # Display Dispersive Loss status
        if hasattr(self.agent, '_display_dispersive_loss_status'):
            self.agent._display_dispersive_loss_status()
        
        self.main_logger.info("✅ Agent setup completed")
    
    def create_dc_optimizer(self):
        """Create optimizer for DC parameters only."""
        if hasattr(self.agent.model, 'get_dc_parameters'):
            dc_params = self.agent.model.get_dc_parameters()
            if not dc_params:
                raise ValueError("No DC parameters found for training!")
            
            optimizer = torch.optim.AdamW(
                dc_params,
                lr=self.learning_rate,
                weight_decay=self.cfg.dc_training.weight_decay
            )
            
            self.main_logger.info(f"✅ DC optimizer created with {len(dc_params)} parameter groups")
            return optimizer
        else:
            # Fallback to all parameters
            self.main_logger.warning("⚠️  Using all parameters for DC training")
            return torch.optim.AdamW(
                self.agent.model.parameters(),
                lr=self.learning_rate,
                weight_decay=self.cfg.dc_training.weight_decay
            )

    def train_dc_tokens(self):
        """Main DC tokens training loop."""
        self.main_logger.info("🚀 Starting DC tokens training...")
        self.start_time = time.time()

        # Setup agent and optimizer
        self.setup_agent_for_dc_training()
        optimizer = self.create_dc_optimizer()

        # Get data loaders
        train_loader = self.agent.get_train_dataloader()
        val_loader = self.agent.get_val_dataloader()

        self.main_logger.info(f"📊 Training data: {len(train_loader)} batches")
        self.main_logger.info(f"📊 Validation data: {len(val_loader)} batches")

        # Training loop
        self.agent.model.train()
        step = 0
        best_val_loss = float('inf')
        last_loss = 0.0  # Initialize for final checkpoint

        with tqdm(total=self.total_steps, desc="DC Training Progress") as pbar:
            while step < self.total_steps:
                for raw_batch in train_loader:
                    if step >= self.total_steps:
                        break

                    try:
                        # Convert batch format and move to device
                        batch = self.agent._convert_batch_format(raw_batch)

                        batch = self.agent._move_batch_to_device(batch)

                        # Create extended batch info for episode-aware contrastive learning
                        extended_batch_info = self.agent._create_extended_batch_info(batch)

                        # Compute contrastive error matrix with components for detailed monitoring
                        error_matrix, raw_diffusion_losses, diagonal_diffusion_losses, features = self.agent.model.compute_contrastive_error_with_components(batch, use_dc=True)

                        # Get the actual dispersive loss value by computing it directly from the diagonal samples
                        # The dispersive loss should be computed from the diagonal elements (same sample pairs)
                        actual_disperse_loss = 0.0
                        if hasattr(self.agent.model.model, 'disp_loss') and features is not None:
                            try:
                                # Use features from diagonal elements (same samples)
                                batch_size = batch["action"].shape[0]
                                if isinstance(features, (list, tuple)) and len(features) > 0:
                                    selected_features = features[-1]
                                elif isinstance(features, torch.Tensor):
                                    selected_features = features
                                else:
                                    selected_features = None

                                if selected_features is not None and selected_features.shape[0] >= batch_size:
                                    # Take the diagonal elements (first B samples correspond to same-sample pairs)
                                    diagonal_features = selected_features[:batch_size]
                                    actual_disperse_loss = self.agent.model.model.disp_loss(diagonal_features).item()
                            except Exception as e:
                                actual_disperse_loss = 0.0

                        # Check if using DTW-guided contrastive learning
                        if hasattr(self.agent, 'use_dtw_contrastive') and self.agent.use_dtw_contrastive:
                            # Use the dispersive loss value that was just computed
                            # Add disperse_loss to extended_batch_info
                            extended_batch_info['disperse_loss'] = torch.tensor(actual_disperse_loss, device=self.agent.device)

                            # DTW-guided contrastive loss
                            loss, loss_components = self.agent._compute_dtw_guided_loss(
                                error_matrix, batch, extended_batch_info
                            )

                            # Extract DTW loss components (matching new naming)
                            contrastive_loss = loss_components.get('contrastive_loss', loss)
                            diffusion_regularization = loss_components.get('diagonal_loss', torch.tensor(0.0))
                            disperse_loss = loss_components.get('disperse_loss', torch.tensor(0.0))
                            mask = None  # DTW loss doesn't use mask
                        else:
                            # Standard episode-aware contrastive loss
                            loss_components = self.agent.contrastive_loss.forward_with_components(error_matrix, extended_batch_info)

                            # Extract separate loss components
                            loss = loss_components['total_loss']
                            contrastive_loss = loss_components['contrastive_loss']
                            diffusion_regularization = loss_components['diffusion_regularization']
                            mask = loss_components['mask']
                   
                        last_loss = loss.item()  # Store for final checkpoint

                        # Backward pass
                        optimizer.zero_grad()
                        loss.backward()

                        # Gradient clipping
                        if self.cfg.dc_training.gradient_clip_norm > 0:
                            torch.nn.utils.clip_grad_norm_(
                                self.agent.model.parameters(),
                                self.cfg.dc_training.gradient_clip_norm
                            )

                        optimizer.step()

                        # Update EMA if enabled
                        if hasattr(self.agent, 'ema_helper') and step % self.agent.update_ema_every_n_steps == 0:
                            self.agent.ema_helper.update(self.agent.model.parameters())

                        # Logging (reduced frequency)
                        if step % 10 == 0:
                            # 统计样本分布信息
                            episode_indices = batch.get('episode_indices', None)

                            # Check if using DTW-guided contrastive learning
                            if hasattr(self.agent, 'use_dtw_contrastive') and self.agent.use_dtw_contrastive:
                                # DTW-guided loss statistics
                                positive_pairs = loss_components.get('dtw_positive_pairs', 0)
                                if isinstance(positive_pairs, torch.Tensor):
                                    positive_pairs = positive_pairs.item()

                                negative_pairs = loss_components.get('dtw_negative_pairs', 0)
                                if isinstance(negative_pairs, torch.Tensor):
                                    negative_pairs = negative_pairs.item()

                                total_pairs = error_matrix.numel()
                                positive_ratio = positive_pairs / total_pairs if total_pairs > 0 else 0
                                hard_negative_pairs = negative_pairs
                                easy_negative_pairs = total_pairs - positive_pairs - negative_pairs
                            else:
                                # Standard contrastive loss statistics
                                positive_pairs = mask.sum().item() if mask is not None else 0
                                total_pairs = mask.numel() if mask is not None else 0
                                positive_ratio = positive_pairs / total_pairs if total_pairs > 0 else 0

                                # 统计困难负样本信息
                                hard_negative_pairs = 0
                                easy_negative_pairs = 0
                                if episode_indices is not None and mask is not None:
                                    batch_size = len(episode_indices)
                                    # 计算同episode样本对
                                    same_episode_pairs = 0
                                    for i in range(batch_size):
                                        for j in range(batch_size):
                                            if episode_indices[i] == episode_indices[j]:
                                                same_episode_pairs += 1
                                                if mask[i, j] == 0:  # 同episode但非正样本 = 困难负样本
                                                    hard_negative_pairs += 1

                                    easy_negative_pairs = total_pairs - positive_pairs - hard_negative_pairs
                            
                            # 计算平均扩散损失统计
                            avg_diagonal_diffusion_loss = diagonal_diffusion_losses.mean().item()
                            avg_all_diffusion_loss = raw_diffusion_losses.mean().item()
                            
                            # 🔥 检查是否有 Dispersive Loss 相关信息
                            dispersive_info = {}
                            # 优先读取模型上的真实开关（configure_dispersive_loss 会将其下发到 model.use_dispersive_in_diffusion）
                            model_disp_enabled = getattr(self.agent.model, 'use_dispersive_in_diffusion', False)
                            agent_cfg_enabled = bool(getattr(self.agent, 'use_dispersive_loss', False) and getattr(self.agent, 'dispersive_in_diffusion', False))
                            dispersive_enabled = bool(model_disp_enabled or agent_cfg_enabled)

                            if dispersive_enabled:
                                dispersive_info['dispersive_enabled'] = True
                                # 权重优先读取 agent 上的设置，否则回退到模型
                                dispersive_info['dispersive_weight'] = getattr(self.agent, 'dispersive_weight', getattr(self.agent.model, 'dispersive_weight', 0.25))

                                # 尝试从特征中计算 dispersive loss（如果可用）
                                if features is not None and hasattr(self.agent.model.model, 'disp_loss'):
                                    try:
                                        f = features
                                        # 若是扩展批(B*B)，仅取前B个样本以避免重复计算
                                        if isinstance(f, (list, tuple)) and len(f) > 0 and isinstance(f[-1], torch.Tensor):
                                            f = f[-1]
                                        if isinstance(f, torch.Tensor):
                                            N = f.shape[0]
                                            ori_B = int(math.isqrt(N))
                                            if ori_B * ori_B == N and ori_B > 1:
                                                f = f[:ori_B]
                                        current_dispersive_loss = self.agent.model.model.disp_loss(f).item()
                                        dispersive_info['dispersive_loss_value'] = float(current_dispersive_loss)
                                    except Exception as e:
                                        dispersive_info['dispersive_loss_value'] = 0.0
                                else:
                                    dispersive_info['dispersive_loss_value'] = 0.0
                            else:
                                dispersive_info['dispersive_enabled'] = False
                                dispersive_info['dispersive_weight'] = 0.0
                                dispersive_info['dispersive_loss_value'] = 0.0
 
                            # Update progress bar display
                            if hasattr(self.agent, 'use_dtw_contrastive') and self.agent.use_dtw_contrastive:
                                # DTW-guided loss display (matching original format)
                                pbar.set_postfix({
                                    'Total': f'{loss.item():.4f}',
                                    'Contrastive': f'{contrastive_loss.item():.4f}',
                                    'Diagonal': f'{diffusion_regularization.item():.4f}',
                                    'Disperse': f'{disperse_loss.item():.4f}',
                                    'DTW_Pos': f'{positive_pairs}',
                                    'DTW_Neg': f'{hard_negative_pairs}',
                                    'Step': step
                                })
                            else:
                                # Standard loss display
                                pbar.set_postfix({
                                    'Total': f'{loss.item():.4f}',
                                    'Contrast': f'{contrastive_loss.item():.4f}',
                                    'Diffusion': f'{diffusion_regularization.item():.4f}',
                                    'AvgDiff': f'{avg_diagonal_diffusion_loss:.4f}',
                                    'DispLoss': f'{dispersive_info["dispersive_loss_value"]:.4f}' if dispersive_info['dispersive_enabled'] else 'Off',
                                    'Step': step
                                })

                            # 🔥 增强的日志信息，包含 DTW 和 Dispersive Loss
                            if hasattr(self.agent, 'use_dtw_contrastive') and self.agent.use_dtw_contrastive:
                                # DTW-guided loss logging (matching original format)
                                log_message = (
                                    f"Step {step}: Total Loss = {loss.item():.6f}, "
                                    f"Contrastive Loss = {contrastive_loss.item():.6f}, "
                                    f"Diagonal Loss = {diffusion_regularization.item():.6f}, "
                                    f"Disperse Loss = {disperse_loss.item():.6f}, "
                                    f"DTW Pos = {positive_pairs}, DTW Neg = {hard_negative_pairs}"
                                )

                                # Add DTW similarity statistics if available
                                if 'dtw_similarity_mean' in loss_components:
                                    dtw_sim_mean = loss_components['dtw_similarity_mean']
                                    if isinstance(dtw_sim_mean, torch.Tensor):
                                        dtw_sim_mean = dtw_sim_mean.item()
                                    log_message += f", DTW Sim Mean = {dtw_sim_mean:.4f}"
                            else:
                                # Standard loss logging
                                log_message = (
                                    f"Step {step}: Total Loss = {loss.item():.6f}, "
                                    f"Contrastive Loss = {contrastive_loss.item():.6f}, "
                                    f"Diffusion Regularization = {diffusion_regularization.item():.6f}, "
                                    f"Avg Diagonal Diffusion = {avg_diagonal_diffusion_loss:.6f}, "
                                    f"Pos = {positive_pairs}, Hard Neg = {hard_negative_pairs}, Easy Neg = {easy_negative_pairs}"
                                )

                                if dispersive_info['dispersive_enabled']:
                                    log_message += (
                                        f", Dispersive Loss = {dispersive_info['dispersive_loss_value']:.6f} "
                                        f"(weight={dispersive_info['dispersive_weight']:.3f})"
                                    )

                            self.training_logger.info(log_message)

                            # 🔥 增强的 Wandb 日志，包含 DTW 和 Dispersive Loss 信息
                            wandb_log_dict = {
                                # 总损失和分离损失 (matching original format)
                                'dc_train/total_loss': loss.item(),
                                'dc_train/contrastive_loss': contrastive_loss.item(),
                                'dc_train/diagonal_loss': diffusion_regularization.item(),
                                'dc_train/disperse_loss': disperse_loss.item(),

                                # 扩散损失统计
                                'dc_train/avg_diagonal_diffusion_loss': avg_diagonal_diffusion_loss,
                                'dc_train/avg_all_diffusion_loss': avg_all_diffusion_loss,
                                'dc_train/diffusion_loss_std': raw_diffusion_losses.std().item(),

                                # 样本分布信息
                                'dc_train/positive_pairs': positive_pairs,
                                'dc_train/hard_negative_pairs': hard_negative_pairs,
                                'dc_train/easy_negative_pairs': easy_negative_pairs,
                                'dc_train/positive_ratio': positive_ratio,

                                # 🔥 Dispersive Loss 信息
                                'dc_train/dispersive_enabled': bool(dispersive_info['dispersive_enabled']),
                                'dc_train/dispersive_weight': float(dispersive_info['dispersive_weight']),
                                'dc_train/dispersive_loss_value': float(dispersive_info['dispersive_loss_value']),

                                # 训练信息
                                'dc_train/step': step,
                                'dc_train/learning_rate': optimizer.param_groups[0]['lr']
                            }

                            # Add DTW-specific metrics if using DTW contrastive learning
                            if hasattr(self.agent, 'use_dtw_contrastive') and self.agent.use_dtw_contrastive:
                                wandb_log_dict.update({
                                    'dc_train/dtw_enabled': True,
                                    'dc_train/dtw_positive_pairs': positive_pairs,
                                    'dc_train/dtw_negative_pairs': hard_negative_pairs,
                                    'dc_train/dtw_similarity_mean': loss_components.get('dtw_similarity_mean', 0.0),
                                    'dc_train/error_matrix_mean': loss_components.get('error_matrix_mean', 0.0)
                                })

                                # Add all DTW loss components
                                for key, value in loss_components.items():
                                    if isinstance(value, torch.Tensor) and value.numel() == 1:
                                        wandb_log_dict[f'dc_train/dtw_{key}'] = value.item()
                            else:
                                wandb_log_dict['dc_train/dtw_enabled'] = False
                            
                            wandb.log(wandb_log_dict)

                        # Evaluation
                        if step % self.eval_freq == 0 and step > 0:
                            val_loss = self.evaluate_dc_model(val_loader, step)

                            # Save best model
                            if val_loss < best_val_loss:
                                best_val_loss = val_loss
                                self.save_checkpoint(optimizer, step, val_loss, is_best=True)
                                self.main_logger.info(f"🏆 New best validation loss: {val_loss:.6f}")

                        # Regular checkpoint saving
                        if step % self.save_freq == 0 and step > 0:
                            self.save_checkpoint(optimizer, step, loss.item(), is_best=False)

                        step += 1
                        pbar.update(1)

                    except Exception as e:
                        self.training_logger.error(f"Error in training step {step}: {e}")
                        # 添加完整的错误追踪信息
                        import traceback
                        self.training_logger.error(f"Full traceback:\n{traceback.format_exc()}")
                        step += 1
                        pbar.update(1)
                        continue

        # Final checkpoint
        self.save_checkpoint(optimizer, step, last_loss, is_best=False)

        total_time = time.time() - self.start_time
        self.main_logger.info(f"✅ DC training completed! Total time: {total_time:.1f}s ({total_time/60:.1f} minutes)")

        return best_val_loss

    def evaluate_dc_model(self, val_loader, step):
        """Evaluate DC model on validation set."""
        self.main_logger.info(f"📊 Evaluating DC model at step {step}")

        self.agent.model.eval()
        total_loss = 0.0
        num_batches = 0

        with torch.no_grad():
            try:
                for raw_batch in val_loader:
                    try:
                        # Use simpler validation approach similar to train_diffusion_transformer.py
                        bp_imgs, inhand_imgs, action, goal_imgs, episode_indices,slice_indices = raw_batch

                        # Move to device (simpler approach)
                        bp_imgs = bp_imgs.to(self.agent.device)
                        inhand_imgs = inhand_imgs.to(self.agent.device)
                        goal_imgs = goal_imgs.to(self.agent.device)

                        # Scale action data
                        action = self.agent.scaler.scale_output(action)
                        action = action[:, self.agent.obs_seq_len - 1:, :].contiguous()
                        bp_imgs = bp_imgs[:, :self.agent.obs_seq_len].contiguous()
                        inhand_imgs = inhand_imgs[:, :self.agent.obs_seq_len].contiguous()

                        state = (bp_imgs, inhand_imgs, goal_imgs)

                        # # Use model's loss function directly (simpler and more stable)
                        # if hasattr(self.agent.model, 'loss'):
                        #     loss = self.agent.model.loss(action, state, goal=None)
                        #     mean_loss = loss.mean().item()
                        # else:
                        #     # Fallback: compute prediction and MSE manually
                        pred_action = self.agent.model(state, goal=None)
                        mean_loss = torch.nn.functional.mse_loss(pred_action, action).item()

                        total_loss += mean_loss
                        num_batches += 1

                        # Limit evaluation batches
                        if num_batches >= self.cfg.evaluation.num_batches:
                            break

                    except Exception as e:
                        self.training_logger.error(f"Error in validation batch: {e}")
                        # Add traceback for debugging
                        import traceback
                        self.training_logger.error(f"Validation batch error traceback:\n{traceback.format_exc()}")
                        continue

            except Exception as e:
                self.training_logger.error(f"Critical error in validation loop: {e}")
                # If DataLoader fails completely, return a default loss
                if num_batches == 0:
                    self.training_logger.warning("No validation batches processed, using default loss")
                    return float('inf')

        avg_loss = total_loss / num_batches if num_batches > 0 else float('inf')

        self.main_logger.info(f"📈 Validation loss: {avg_loss:.6f}")
        self.training_logger.info(f"Validation at step {step}: Loss = {avg_loss:.6f}, Batches = {num_batches}")

        wandb.log({
            'dc_val/loss': avg_loss,
            'dc_val/step': step,
            'dc_val/num_batches': num_batches
        })

        self.agent.model.train()
        return avg_loss

    def save_checkpoint(self, optimizer, step, loss, is_best=False):
        """Save training checkpoint."""
        checkpoint = {
            'step': step,
            'model_state_dict': self.agent.model.state_dict(),
            'optimizer_state_dict': optimizer.state_dict(),
            'loss': loss,
            'config': OmegaConf.to_container(self.cfg, resolve=True),
            'timestamp': datetime.now().isoformat()
        }

        # Save EMA state if available
        if hasattr(self.agent, 'ema_helper'):
            checkpoint['ema_state_dict'] = self.agent.ema_helper.state_dict()

        # Save checkpoint
        if is_best:
            checkpoint_path = self.checkpoint_dir / "best_dc_model.pth"
            self.main_logger.info(f"💾 Saving best checkpoint: {checkpoint_path}")
        else:
            checkpoint_path = self.checkpoint_dir / f"checkpoint_step_{step}.pth"
            self.main_logger.info(f"💾 Saving checkpoint: {checkpoint_path}")

        torch.save(checkpoint, checkpoint_path)

        # Also save latest checkpoint
        latest_path = self.checkpoint_dir / "latest_dc_model.pth"
        torch.save(checkpoint, latest_path)

        # Clean up old checkpoints (keep only last N)
        if self.cfg.checkpointing.keep_last_n > 0:
            self.cleanup_old_checkpoints()

    def cleanup_old_checkpoints(self):
        """Clean up old checkpoint files."""
        checkpoint_files = list(self.checkpoint_dir.glob("checkpoint_step_*.pth"))
        if len(checkpoint_files) > self.cfg.checkpointing.keep_last_n:
            # Sort by step number
            checkpoint_files.sort(key=lambda x: int(x.stem.split('_')[-1]))
            # Remove oldest files
            for old_file in checkpoint_files[:-self.cfg.checkpointing.keep_last_n]:
                old_file.unlink()
                self.training_logger.info(f"🗑️  Removed old checkpoint: {old_file}")

@hydra.main(version_base=None, config_path="config", config_name="dc_training")
def main(cfg: DictConfig) -> None:
    """Main training function."""

    # Setup logging
    main_logger, training_logger, log_dir = setup_logging(cfg)

    try:
        # Set random seed
        set_seed_everywhere(cfg.seed)

        # Log system information
        log_system_info(main_logger)

        # Log DC configuration
        log_dc_configuration(cfg, main_logger)

        # Setup wandb
        wandb_enabled = setup_wandb(cfg, main_logger)

        # Instantiate agent
        main_logger.info("🔧 Instantiating DC agent...")
        agent = hydra.utils.instantiate(cfg.agents)
        main_logger.info(f"✅ Agent created: {type(agent).__name__}")

        # Verify agent has required methods
        required_methods = ['get_train_dataloader', 'get_val_dataloader', '_convert_batch_format',
                          '_move_batch_to_device', 'contrastive_loss']
        for method in required_methods:
            if not hasattr(agent, method):
                raise AttributeError(f"Agent missing required method: {method}")

        main_logger.info("✅ Agent validation passed")

        # Create training manager
        trainer = DCTrainingManager(agent, cfg, main_logger, training_logger)

        # Start DC training
        best_val_loss = trainer.train_dc_tokens()

        # Final logging
        main_logger.info("=" * 60)
        main_logger.info("🎉 DC Training Completed Successfully!")
        main_logger.info(f"🏆 Best validation loss: {best_val_loss:.6f}")
        main_logger.info(f"📁 Checkpoints saved to: {trainer.checkpoint_dir}")
        main_logger.info(f"📁 Logs saved to: {log_dir}")
        main_logger.info("=" * 60)

        # 训练完成后，立即进行单进程评估
        main_logger.info("\n" + "=" * 60)
        main_logger.info("🚀 开始DC模型自动评估阶段...")
        main_logger.info("=" * 60)
        
        try:
            # 评估两个模型: best 和 latest
            models_to_evaluate = [
                ("best_dc_model.pth", "最佳模型"),
                ("latest_dc_model.pth", "最新模型")
            ]
            
            evaluation_results = {}
            
            for model_file, model_desc in models_to_evaluate:
                model_path = trainer.checkpoint_dir / model_file
                
                if not model_path.exists():
                    main_logger.warning(f"⚠️  模型文件不存在，跳过评估: {model_path}")
                    continue
                    
                main_logger.info(f"\n📊 开始评估 {model_desc}: {model_file}")
                
                try:
                    # 使用load_and_simulate.py进行评估
                    from datetime import datetime
                    timestamp = datetime.now().strftime("%m%d_%H%M")
                    output_file = f"dc_evaluation_{model_file.replace('.pth', '')}_{timestamp}.json"
                    
                    # 创建评估配置
                    eval_config = {
                        'complete_dc_model': str(model_path),
                        'task_suite': cfg.task_suite,
                        'num_episodes': 20,
                        'max_steps': 600,
                        'max_videos_per_task': 3,
                        'verbose': True,
                        'output': output_file
                    }
                    
                    main_logger.info(f"📝 评估配置: {eval_config}")
                    
                    # 导入并使用load_and_simulate模块
                    main_logger.info(f"🔄 导入评估模块...")
                    from load_and_simulate import PretrainedModelSimulator
                    
                    # 创建simulator实例
                    simulator = PretrainedModelSimulator(
                        config_path="dummy",  # 临时值，将被DC配置覆盖
                        model_weights_path="dummy",  # 临时值，将被DC配置覆盖
                        task_suite=cfg.task_suite,
                        num_episodes=20,
                        max_steps_per_episode=600,
                        seed=cfg.seed,
                        device=trainer.device,
                        save_video=True,
                        max_videos_per_task=3,
                        verbose=True
                    )
                    
                    # 加载完整DC模型
                    main_logger.info(f"📥 加载完整DC模型: {model_path}")
                    simulator.load_complete_dc_model(str(model_path))
                    
                    # 运行评估（单进程）
                    main_logger.info(f"🎮 开始环境评估...")
                    results = simulator.evaluate_all_tasks()
                    
                    # 保存评估结果
                    import json
                    with open(output_file, 'w') as f:
                        json.dump(results, f, indent=2)
                    
                    # 从evaluate_all_tasks的返回值中提取成功率信息
                    success_rate = results.get('overall_success_rate', 0.0)
                    total_episodes = results.get('total_episodes', 0)
                    successful_episodes = results.get('total_success_count', 0)
                    avg_task_success_rate = results.get('avg_task_success_rate', 0.0)
                    
                    evaluation_results[model_file] = {
                        'success_rate': success_rate,
                        'total_episodes': total_episodes,
                        'successful_episodes': successful_episodes,
                        'avg_task_success_rate': avg_task_success_rate,
                        'output_file': output_file
                    }
                    
                    main_logger.info(f"🎯 {model_desc}评估完成:")
                    main_logger.info(f"  - 整体成功率: {success_rate:.3f} ({successful_episodes}/{total_episodes})")
                    main_logger.info(f"  - 平均任务成功率: {avg_task_success_rate:.3f}")
                    main_logger.info(f"  - 结果文件: {output_file}")
                    
                    # 清理GPU内存
                    del simulator
                    torch.cuda.empty_cache()
                    
                except Exception as e:
                    main_logger.error(f"❌ {model_desc}评估失败: {e}")
                    main_logger.error(f"错误类型: {type(e).__name__}")
                    import traceback
                    main_logger.error(f"错误详情:\n{traceback.format_exc()}")
                    continue
            
            # 总结评估结果
            main_logger.info("\n" + "=" * 60)
            main_logger.info("📊 DC模型评估结果总结:")
            for model_file, result in evaluation_results.items():
                if isinstance(result, dict) and 'success_rate' in result:
                    main_logger.info(f"  {model_file}:")
                    main_logger.info(f"    整体成功率: {result['success_rate']:.3f} "
                                   f"({result['successful_episodes']}/{result['total_episodes']})")
                    main_logger.info(f"    平均任务成功率: {result['avg_task_success_rate']:.3f}")
                    main_logger.info(f"    结果文件: {result['output_file']}")
                else:
                    main_logger.info(f"  {model_file}: {result}")
            main_logger.info("=" * 60)
            
            # 如果启用了wandb，记录评估结果
            if wandb_enabled:
                for model_file, result in evaluation_results.items():
                    if isinstance(result, dict) and 'success_rate' in result:
                        model_key = model_file.replace('.pth', '')
                        wandb.log({
                            f"evaluation/{model_key}_overall_success_rate": result['success_rate'],
                            f"evaluation/{model_key}_avg_task_success_rate": result['avg_task_success_rate'],
                            f"evaluation/{model_key}_total_episodes": result['total_episodes'],
                            f"evaluation/{model_key}_successful_episodes": result['successful_episodes']
                        })
                        
        except Exception as e:
            main_logger.error(f"❌ 评估阶段失败: {e}")
            main_logger.error(f"错误类型: {type(e).__name__}")
            import traceback
            main_logger.error(f"错误详情:\n{traceback.format_exc()}")
            main_logger.info("🔄 跳过评估，训练已完成")
            
            # 提供手动评估命令作为备选
            main_logger.info("\n📋 手动评估命令（备选）:")
            models_generated = [
                ("best_dc_model.pth", "最佳模型"),
                ("latest_dc_model.pth", "最新模型")
            ]
            
            for model_file, model_desc in models_generated:
                model_path = trainer.checkpoint_dir / model_file
                if model_path.exists():
                    eval_cmd = f'CUDA_VISIBLE_DEVICES=2 python load_and_simulate.py --complete_dc_model "{model_path}" --task_suite {cfg.task_suite} --num_episodes 20 --max_steps 600 --max_videos_per_task 3 --verbose --output "dc_evaluation_{model_file.replace(".pth", "")}_$(date +%m%d_%H%M).json"'
                    main_logger.info(f"# 评估{model_desc}")
                    main_logger.info(f"cd /home/<USER>/work/MaIL && conda activate mail && {eval_cmd}")
            main_logger.info("=" * 60)

        # Close wandb
        if wandb_enabled:
            wandb.finish()

    except Exception as e:
        main_logger.error(f"❌ Training failed with error: {e}")
        main_logger.error(f"Error type: {type(e).__name__}")
        import traceback
        main_logger.error(f"Traceback:\n{traceback.format_exc()}")

        # Close wandb on error
        try:
            wandb.finish()
        except:
            pass

        raise e

if __name__ == "__main__":
    # Set multiprocessing start method
    mp.set_start_method('spawn', force=True)

    # Run main function
    main()
