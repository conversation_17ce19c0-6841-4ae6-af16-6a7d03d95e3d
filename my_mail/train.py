import os
import logging
import random

import hydra
import numpy as np
import multiprocessing as mp
import wandb
from omegaconf import DictConfig, OmegaConf
import torch

from agents.utils import sim_framework_path


log = logging.getLogger(__name__)

print(torch.cuda.is_available())

OmegaConf.register_new_resolver(
     "add", lambda *numbers: sum(numbers)
)
torch.cuda.empty_cache()


def set_seed_everywhere(seed):
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed_all(seed)
    np.random.seed(seed)
    random.seed(seed)

def prepare_agent_for_multiprocess(agent):
    """
    准备agent以支持多进程测试，避免序列化错误
    """
    print("🔧 准备agent以支持多进程...")
    
    # 1. 设置agent为评估模式
    if hasattr(agent, 'eval'):
        agent.eval()
    
    # 2. 递归处理agent中的所有PyTorch模块
    def process_module(module, module_path=""):
        if isinstance(module, torch.nn.Module):
            # 设置为评估模式
            module.eval()
            
            # 禁用所有参数的梯度计算
            for param_name, param in module.named_parameters():
                param.requires_grad_(False)
                if param.grad is not None:
                    param.grad = None  # 清除梯度
            
            # 处理缓冲区中的问题张量
            buffers_to_fix = []
            for buffer_name, buffer in module.named_buffers():
                if buffer.requires_grad and not buffer.is_leaf:
                    buffers_to_fix.append((buffer_name, buffer.detach()))
                    print(f"   修复缓冲区: {module_path}.{buffer_name}")
            
            # 重新注册修复的缓冲区
            for buffer_name, fixed_buffer in buffers_to_fix:
                module.register_buffer(buffer_name, fixed_buffer, persistent=True)
    
    # 3. 处理agent中可能包含模型的常见属性
    common_model_attrs = ['policy', 'model', 'network', 'actor', 'critic', 'encoder', 'decoder']
    
    for attr_name in common_model_attrs:
        if hasattr(agent, attr_name):
            attr = getattr(agent, attr_name)
            if attr is not None:
                if isinstance(attr, torch.nn.Module):
                    process_module(attr, f"agent.{attr_name}")
                elif hasattr(attr, '__dict__'):
                    # 递归处理嵌套对象
                    for sub_attr_name in dir(attr):
                        if not sub_attr_name.startswith('_'):
                            try:
                                sub_attr = getattr(attr, sub_attr_name)
                                if isinstance(sub_attr, torch.nn.Module):
                                    process_module(sub_attr, f"agent.{attr_name}.{sub_attr_name}")
                            except:
                                continue
    
    # 4. 直接处理agent本身（如果它是一个nn.Module）
    if isinstance(agent, torch.nn.Module):
        process_module(agent, "agent")
    
    # 5. 清理CUDA缓存
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
    
    print("✅ agent多进程准备完成")
    return agent



@hydra.main(config_path="config", config_name="benchmark_libero_goal.yaml", version_base="1.1")
def main(cfg: DictConfig) -> None:

    set_seed_everywhere(cfg.seed)

    # init wandb logger and config from hydra path
    wandb.config = OmegaConf.to_container(cfg, resolve=True, throw_on_missing=True)

    run = wandb.init(
        project=cfg.wandb.project,
        entity=cfg.wandb.entity,
        group=cfg.group,
        mode="offline",
        config=wandb.config
    )

    agent = hydra.utils.instantiate(cfg.agents)
    # train the agents
    agent.train_vision_agent()

    # load the model performs best on the evaluation set
    # agent.load_pretrained_model('/home/<USER>/student/wang/OCIL/OCIL/libero_10/agent', sv_name="last_ddpm.pth")

    num_cpu = mp.cpu_count()
    cpu_set = list(range(num_cpu))
    print("there are cpus: ", num_cpu)

    assign_cpus = cpu_set[cfg.seed * cfg.n_cores:cfg.seed * cfg.n_cores + cfg.n_cores]

    env_sim = hydra.utils.instantiate(cfg.simulation)
    agent = prepare_agent_for_multiprocess(agent)
    env_sim.test_agent(agent, assign_cpus, epoch=agent.epoch)

    log.info("done")

    wandb.finish()


if __name__ == "__main__":
    main()