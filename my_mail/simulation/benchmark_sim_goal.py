# import logging
# import os
# import cv2
# import random
# import numpy as np
# import torch
# import wandb
# import robosuite
# import multiprocessing as mp
# from .base_sim import BaseSim
# from libero.libero.envs import *
# from libero.libero import benchmark
# from libero.libero.envs import OffScreenRenderEnv
# import imgaug.parameters as iap
# from imgaug import augmenters as iaa
# import sys
# sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
# from utils.video_utils import VideoRecorder


# log = logging.getLogger(__name__)


# def assign_process_to_cpu(pid, cpus):
#     os.sched_setaffinity(pid, cpus)


# def process_image_input(img_tensor):
#     # return (img_tensor / 255. - 0.5) * 2.
#     return img_tensor / 255.


# # aug = iaa.arithmetic.ReplaceElementwise(iap.FromLowerResolution(iap.Binomial(0.02), size_px=8),
# #                                         [255])


# class MultiTaskSim(BaseSim):
#     def __init__(self,
#                  num_episode,
#                  max_step_per_episode,
#                  task_suite: str,
#                  use_eye_in_hand: bool,
#                  seed,
#                  device,
#                  render,
#                  n_cores,
#                  camera_shape: tuple,
#                  data_aug: bool = False,
#                  aug_factor: float = 0.02,
#                  task_id: int = 0,
#                  nms: float = 0.1,
#                  save_video: bool = True,
#                  max_videos_per_task: int = 5):
#         super().__init__(seed, device, render, n_cores)

#         # data augmentation
#         self.data_aug = data_aug
#         self.aug_factor = aug_factor
#         self.aug = iaa.arithmetic.ReplaceElementwise(iap.FromLowerResolution(iap.Binomial(self.aug_factor), size_px=8),
#                                                      [255])

#         # according to the task_id, load the corresponding bddl file
#         self.task_suite = task_suite

#         self.use_eye_in_hand = use_eye_in_hand
#         self.render = render
#         self.task_id = task_id

#         self.num_episode = num_episode
#         self.max_step_per_episode = max_step_per_episode

#         # video recording settings
#         self.save_video = save_video
#         self.max_videos_per_task = max_videos_per_task
#         self.video_counter = {}  # Track videos saved per task

#         self.success_rate = 0

#     def eval_agent(self, agent, contexts, context_ind, success, pid, cpu_set, epoch=None):
#         print(os.getpid(), cpu_set)
#         assign_process_to_cpu(os.getpid(), cpu_set)

#         # env_ids = []

#         print(contexts)

#         for i, context in enumerate(contexts):

#             # if context not in env_ids:
#             # env_ids.append(context)

#             task_suite = benchmark.get_benchmark_dict()[self.task_suite]()

#             task_bddl_file = task_suite.get_task_bddl_file_path(context)

#             file_name = os.path.basename(task_bddl_file).split('.')[0]

#             task_emb = self.task_embs[file_name]

#             # goal_images = self.goal_dicts[file_name]
#             # goal_image = random.choice(goal_images)

#             init_states = task_suite.get_task_init_states(context)

#             env_args = {
#                 "bddl_file_name": task_bddl_file,
#                 "camera_heights": 128,
#                 "camera_widths": 128
#             }

#             env = OffScreenRenderEnv(**env_args)

#             agent.reset()
#             env.seed(self.seed)
#             env.reset()
#             obs = env.set_init_state(init_state=init_states[context_ind[i]])

#             # Initialize video recorder
#             should_record_video = (
#                 self.save_video and
#                 context not in self.video_counter and
#                 len([k for k in self.video_counter.keys() if str(context) in str(k)]) < self.max_videos_per_task
#             )
#             video_recorder = VideoRecorder(save_video=should_record_video)

#             # Get task description for video filename
#             task_description = f"task_{context}"
#             try:
#                 # Try to get actual task description if available
#                 task_suite_obj = benchmark.get_benchmark_dict()[self.task_suite]()
#                 task_obj = task_suite_obj.get_task(context)
#                 if hasattr(task_obj, 'language'):
#                     task_description = task_obj.language
#                 elif hasattr(task_obj, 'name'):
#                     task_description = task_obj.name
#             except:
#                 pass

#             # dummy actions all zeros for initial physics simulation
#             dummy = np.zeros(7)
#             dummy[-1] = -1.0  # set the last action to -1 to open the gripper
#             for _ in range(5):
#                 obs, _, _, _ = env.step(dummy)
#                 # Record initial frames
#                 if should_record_video:
#                     video_recorder.record_frame(obs, "agentview_image")

#             # multiprocessing simulation
#             episode_success = False
#             for j in range(self.max_step_per_episode):
#                 agentview_rgb = obs["agentview_image"]

#                 # save_path = os.path.join("/home/<USER>/student/wang/OCIL/OCIL", f"{self.task_suite}", "images")
#                 # img = env.sim.render(camera_name="frontview", width=1280, height=800)[..., ::-1]
#                 # img = np.flip(img, axis=0)
#                 # cv2.imwrite(os.path.join(save_path, f"agentview_{context}_{context_ind[i]}_{j}.png"), img)

#                 if self.data_aug:
#                     agentview_rgb = self.aug(image=agentview_rgb)

#                 if self.use_eye_in_hand:
#                     eye_in_hand_rgb = obs["robot0_eye_in_hand_image"]
#                     state = (agentview_rgb, eye_in_hand_rgb, task_emb)
#                 else:
#                     state = agentview_rgb

#                 action = agent.predict(state)[0]
#                 obs, r, done, _ = env.step(action)

#                 # Record frame for video
#                 if should_record_video:
#                     video_recorder.record_frame(obs, "agentview_image")

#                 # if self.render:
#                 # env.render()

#                 if r == 1:
#                     success[context, context_ind[i]] = r
#                     episode_success = True
#                     # env.close()
#                     break

#             # Save video if recording
#             if should_record_video and video_recorder.get_num_frames() > 0:
#                 try:
#                     episode_idx = f"{context}_{context_ind[i]}"
#                     current_epoch = epoch if epoch is not None else "unknown"
#                     video_path = video_recorder.save_recorded_video(
#                         task_suite_name=self.task_suite,
#                         epoch=current_epoch,
#                         episode_idx=episode_idx,
#                         success=episode_success,
#                         task_description=task_description
#                     )
#                     if video_path:
#                         # Mark this task as having a video recorded
#                         self.video_counter[f"{context}_{context_ind[i]}"] = True
#                         print(f"📹 Video saved for task {context}, episode {context_ind[i]}: {episode_success}")
#                 except Exception as e:
#                     print(f"⚠️  Failed to save video for task {context}: {e}")

#             env.close()

#     def test_agent(self, agent, cpu_set=None, epoch=None):
#         logging.info("Start testing agent")

#         self.task_embs = agent.trainset.tasks

#         if cpu_set is None:
#             num_cpu = mp.cpu_count()
#             cpu_set = [i for i in range(num_cpu)]
#         else:
#             num_cpu = len(cpu_set)

#         print("there is {} cpus".format(num_cpu))

#         if self.task_suite == "libero_90":
#             num_tasks = 90
#         else:
#             num_tasks = 10

#         success = torch.zeros([num_tasks, self.num_episode]).share_memory_()
#         all_runs = num_tasks * self.num_episode
#         ###################################################################
#         # distribute every runs on cpu
#         ###################################################################
#         contexts = np.arange(num_tasks)
#         contexts = np.repeat(contexts, self.num_episode)

#         context_ind = np.arange(self.num_episode)
#         context_ind = np.tile(context_ind, num_tasks)

#         repeat_num = all_runs // num_cpu
#         repeat_res = all_runs % num_cpu

#         workload_array = np.ones([num_cpu], dtype=int)
#         workload_array[:repeat_res] += repeat_num
#         workload_array[repeat_res:] = repeat_num

#         assert np.sum(workload_array) == all_runs

#         ind_workload = np.cumsum(workload_array)
#         ind_workload = np.concatenate([[0], ind_workload])
#         ###################################################################
#         ctx = mp.get_context('spawn')
#         processes_list = []

#         for i in range(self.n_cores):
#             p = ctx.Process(target=self.eval_agent,
#                             kwargs={
#                                 "agent": agent,
#                                 "contexts": contexts[ind_workload[i]:ind_workload[i + 1]],
#                                 "context_ind": context_ind[ind_workload[i]:ind_workload[i + 1]],
#                                 "success": success,
#                                 "pid": i,
#                                 "cpu_set": set(cpu_set[i:i + 1]),
#                                 "epoch": epoch
#                             },
#                             )
#             p.start()
#             processes_list.append(p)

#         [p.join() for p in processes_list]

#         success_rate = torch.mean(success, dim=-1)
#         average_success = torch.mean(success_rate).item()

#         print(f'success array {success.detach()}')

#         custom_step = f"{epoch}_custom_step"
#         wandb.define_metric(custom_step)
#         wandb.define_metric(f"{epoch}_tasks_success", step_metric=custom_step)

#         for num in range(num_tasks):
#             log.info(f"Task {num}: {success_rate[num].item()}")

#             wandb.log({custom_step: num,
#                        f"{epoch}_tasks_success": success_rate[num].item()
#                        })

#         wandb.log({f"epoch{epoch}_average_success": average_success})
#         log.info(f"Average success rate: {average_success}")


import logging
import os
import cv2
import random
import numpy as np
import torch
import wandb
import robosuite
import multiprocessing as mp
from .base_sim import BaseSim
from libero.libero.envs import *
from libero.libero import benchmark
from libero.libero.envs import OffScreenRenderEnv
import imgaug.parameters as iap
from imgaug import augmenters as iaa
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from utils.video_utils import VideoRecorder


log = logging.getLogger(__name__)


def assign_process_to_cpu(pid, cpus):
    os.sched_setaffinity(pid, cpus)


def process_image_input(img_tensor):
    # return (img_tensor / 255. - 0.5) * 2.
    return img_tensor / 255.


# def prepare_agent_for_multiprocess(agent):
#     """
#     准备agent以支持多进程，修复PyTorch序列化问题
#     """
#     print("🔧 正在为多进程准备agent...")
    
#     import copy
#     import torch
    
#     # 创建agent的深拷贝，避免修改原始agent
#     try:
#         safe_agent = copy.deepcopy(agent)
#     except:
#         # 如果深拷贝失败，直接修改原agent
#         safe_agent = agent
    
#     def clean_module(module, module_name=""):
#         """递归清理PyTorch模块"""
#         if isinstance(module, torch.nn.Module):
#             # 设置为评估模式
#             module.eval()
            
#             # 禁用所有参数的梯度
#             for param_name, param in module.named_parameters():
#                 param.requires_grad_(False)
#                 if param.grad is not None:
#                     param.grad = None
            
#             # 修复有问题的缓冲区
#             buffers_to_fix = []
#             for buffer_name, buffer in module.named_buffers():
#                 if buffer.requires_grad and not buffer.is_leaf:
#                     buffers_to_fix.append((buffer_name, buffer.detach()))
#                     print(f"   修复缓冲区: {module_name}.{buffer_name}")
            
#             # 重新注册修复的缓冲区
#             for buffer_name, fixed_buffer in buffers_to_fix:
#                 module.register_buffer(buffer_name, fixed_buffer, persistent=True)
    
#     def clean_object_recursively(obj, obj_path="agent"):
#         """递归清理对象中的所有PyTorch相关内容"""
#         # 直接处理PyTorch模块
#         if isinstance(obj, torch.nn.Module):
#             clean_module(obj, obj_path)
#             return
        
#         # 处理对象的属性
#         if hasattr(obj, '__dict__'):
#             for attr_name in list(obj.__dict__.keys()):
#                 if attr_name.startswith('_'):
#                     continue
                
#                 try:
#                     attr_value = getattr(obj, attr_name)
#                     attr_path = f"{obj_path}.{attr_name}"
                    
#                     if isinstance(attr_value, torch.nn.Module):
#                         clean_module(attr_value, attr_path)
#                     elif isinstance(attr_value, torch.Tensor):
#                         # 处理单独的张量
#                         if attr_value.requires_grad and not attr_value.is_leaf:
#                             setattr(obj, attr_name, attr_value.detach())
#                             print(f"   修复张量: {attr_path}")
#                     elif hasattr(attr_value, '__dict__') and not isinstance(attr_value, (str, int, float, bool)):
#                         # 递归处理嵌套对象
#                         clean_object_recursively(attr_value, attr_path)
#                 except Exception as e:
#                     # 跳过无法处理的属性
#                     continue
    
#     # 开始清理
#     clean_object_recursively(safe_agent)
    
#     # 特别处理常见的agent结构
#     common_attrs = ['policy', 'model', 'network', 'actor', 'critic', 'encoder', 'decoder', 'trainset']
#     for attr_name in common_attrs:
#         if hasattr(safe_agent, attr_name):
#             attr = getattr(safe_agent, attr_name)
#             if attr is not None:
#                 clean_object_recursively(attr, f"agent.{attr_name}")
    
#     # 如果agent本身是nn.Module
#     if isinstance(safe_agent, torch.nn.Module):
#         clean_module(safe_agent, "agent")
    
#     # 特别处理DiffusionEncDecWithFeatures的collected_features
#     def clear_collected_features(obj, obj_path=""):
#         """清理DiffusionEncDecWithFeatures中的collected_features"""
#         if hasattr(obj, 'clear_features_for_multiprocess'):
#             obj.clear_features_for_multiprocess()
#             print(f"   清理特征缓存: {obj_path}")
        
#         # 递归检查所有属性
#         if hasattr(obj, '__dict__'):
#             for attr_name, attr_value in obj.__dict__.items():
#                 if not attr_name.startswith('_') and attr_value is not None:
#                     try:
#                         if hasattr(attr_value, '__dict__'):
#                             clear_collected_features(attr_value, f"{obj_path}.{attr_name}")
#                     except:
#                         continue
    
#     clear_collected_features(safe_agent, "agent")
    
#     # 清理CUDA缓存
#     if torch.cuda.is_available():
#         torch.cuda.empty_cache()
    
#     print("✅ agent多进程准备完成")
#     return safe_agent


class MultiTaskSim(BaseSim):
    def __init__(self,
                 num_episode,
                 max_step_per_episode,
                 task_suite: str,
                 use_eye_in_hand: bool,
                 seed,
                 device,
                 render,
                 n_cores,
                 camera_shape: tuple,
                 data_aug: bool = False,
                 aug_factor: float = 0.02,
                 task_id: int = 0,
                 nms: float = 0.1,
                 save_video: bool = True,
                 max_videos_per_task: int = 5):
        super().__init__(seed, device, render, n_cores)

        # data augmentation
        self.data_aug = data_aug
        self.aug_factor = aug_factor
        self.aug = iaa.arithmetic.ReplaceElementwise(iap.FromLowerResolution(iap.Binomial(self.aug_factor), size_px=8),
                                                     [255])

        # according to the task_id, load the corresponding bddl file
        self.task_suite = task_suite

        self.use_eye_in_hand = use_eye_in_hand
        self.render = render
        self.task_id = task_id

        self.num_episode = num_episode
        self.max_step_per_episode = max_step_per_episode

        # video recording settings
        self.save_video = save_video
        self.max_videos_per_task = max_videos_per_task
        self.video_counter = {}  # Track videos saved per task

        self.success_rate = 0

    def eval_agent(self, agent, contexts, context_ind, success, pid, cpu_set, epoch=None):
        print(os.getpid(), cpu_set)
        assign_process_to_cpu(os.getpid(), cpu_set)

        # env_ids = []

        print(contexts)

        for i, context in enumerate(contexts):

            # if context not in env_ids:
            # env_ids.append(context)

            task_suite = benchmark.get_benchmark_dict()[self.task_suite]()

            task_bddl_file = task_suite.get_task_bddl_file_path(context)

            file_name = os.path.basename(task_bddl_file).split('.')[0]

            task_emb = self.task_embs[file_name]

            # goal_images = self.goal_dicts[file_name]
            # goal_image = random.choice(goal_images)

            init_states = task_suite.get_task_init_states(context)

            env_args = {
                "bddl_file_name": task_bddl_file,
                "camera_heights": 128,
                "camera_widths": 128
            }

            env = OffScreenRenderEnv(**env_args)

            agent.reset()
            env.seed(self.seed)
            env.reset()
            obs = env.set_init_state(init_state=init_states[context_ind[i]])

            # Initialize video recorder
            should_record_video = (
                self.save_video and
                context not in self.video_counter and
                len([k for k in self.video_counter.keys() if str(context) in str(k)]) < self.max_videos_per_task
            )
            video_recorder = VideoRecorder(save_video=should_record_video)

            # Get task description for video filename
            task_description = f"task_{context}"
            try:
                # Try to get actual task description if available
                task_suite_obj = benchmark.get_benchmark_dict()[self.task_suite]()
                task_obj = task_suite_obj.get_task(context)
                if hasattr(task_obj, 'language'):
                    task_description = task_obj.language
                elif hasattr(task_obj, 'name'):
                    task_description = task_obj.name
            except:
                pass

            # dummy actions all zeros for initial physics simulation
            dummy = np.zeros(7)
            dummy[-1] = -1.0  # set the last action to -1 to open the gripper
            for _ in range(5):
                obs, _, _, _ = env.step(dummy)
                # Record initial frames
                if should_record_video:
                    video_recorder.record_frame(obs, "agentview_image")

            # multiprocessing simulation
            episode_success = False
            for j in range(self.max_step_per_episode):
                agentview_rgb = obs["agentview_image"]

                # save_path = os.path.join("/home/<USER>/student/wang/OCIL/OCIL", f"{self.task_suite}", "images")
                # img = env.sim.render(camera_name="frontview", width=1280, height=800)[..., ::-1]
                # img = np.flip(img, axis=0)
                # cv2.imwrite(os.path.join(save_path, f"agentview_{context}_{context_ind[i]}_{j}.png"), img)

                if self.data_aug:
                    agentview_rgb = self.aug(image=agentview_rgb)

                if self.use_eye_in_hand:
                    eye_in_hand_rgb = obs["robot0_eye_in_hand_image"]
                    state = (agentview_rgb, eye_in_hand_rgb, task_emb)
                else:
                    state = agentview_rgb

                action = agent.predict(state)[0]
                obs, r, done, _ = env.step(action)

                # Record frame for video
                if should_record_video:
                    video_recorder.record_frame(obs, "agentview_image")

                # if self.render:
                # env.render()

                if r == 1:
                    success[context, context_ind[i]] = r
                    episode_success = True
                    # env.close()
                    break

            # Save video if recording
            if should_record_video and video_recorder.get_num_frames() > 0:
                try:
                    episode_idx = f"{context}_{context_ind[i]}"
                    current_epoch = epoch if epoch is not None else "unknown"
                    video_path = video_recorder.save_recorded_video(
                        task_suite_name=self.task_suite,
                        epoch=current_epoch,
                        episode_idx=episode_idx,
                        success=episode_success,
                        task_description=task_description
                    )
                    if video_path:
                        # Mark this task as having a video recorded
                        self.video_counter[f"{context}_{context_ind[i]}"] = True
                        print(f"📹 Video saved for task {context}, episode {context_ind[i]}: {episode_success}")
                except Exception as e:
                    print(f"⚠️  Failed to save video for task {context}: {e}")

            env.close()

    def test_agent(self, agent, cpu_set=None, epoch=None):
        logging.info("Start testing agent")

        # 🔧 关键修复：在多进程之前准备agent
        

        self.task_embs = agent.trainset.tasks

        if cpu_set is None:
            num_cpu = mp.cpu_count()
            cpu_set = [i for i in range(num_cpu)]
        else:
            num_cpu = len(cpu_set)

        print("there is {} cpus".format(num_cpu))

        if self.task_suite == "libero_90":
            num_tasks = 90
        else:
            num_tasks = 10

        success = torch.zeros([num_tasks, self.num_episode]).share_memory_()
        all_runs = num_tasks * self.num_episode
        ###################################################################
        # distribute every runs on cpu
        ###################################################################
        contexts = np.arange(num_tasks)
        contexts = np.repeat(contexts, self.num_episode)

        context_ind = np.arange(self.num_episode)
        context_ind = np.tile(context_ind, num_tasks)

        repeat_num = all_runs // num_cpu
        repeat_res = all_runs % num_cpu

        workload_array = np.ones([num_cpu], dtype=int)
        workload_array[:repeat_res] += repeat_num
        workload_array[repeat_res:] = repeat_num

        assert np.sum(workload_array) == all_runs

        ind_workload = np.cumsum(workload_array)
        ind_workload = np.concatenate([[0], ind_workload])
        ###################################################################
        ctx = mp.get_context('spawn')
        processes_list = []

        # 🔧 在创建进程前再次确保agent是安全的
        print("🔧 最终检查agent状态...")
        
        # 强制设置no_grad模式
        torch.set_grad_enabled(False)

        for i in range(self.n_cores):
            p = ctx.Process(target=self.eval_agent,
                            kwargs={
                                "agent": agent,  # 现在agent已经被清理过了
                                "contexts": contexts[ind_workload[i]:ind_workload[i + 1]],
                                "context_ind": context_ind[ind_workload[i]:ind_workload[i + 1]],
                                "success": success,
                                "pid": i,
                                "cpu_set": set(cpu_set[i:i + 1]),
                                "epoch": epoch
                            },
                            )
            print(f"🚀 启动进程 {i}...")
            p.start()  # 这是原来出错的第260行
            processes_list.append(p)

        # 恢复梯度计算（虽然在这个上下文中可能不需要）
        torch.set_grad_enabled(True)

        [p.join() for p in processes_list]

        success_rate = torch.mean(success, dim=-1)
        average_success = torch.mean(success_rate).item()

        print(f'success array {success.detach()}')

        custom_step = f"{epoch}_custom_step"
        wandb.define_metric(custom_step)
        wandb.define_metric(f"{epoch}_tasks_success", step_metric=custom_step)

        for num in range(num_tasks):
            log.info(f"Task {num}: {success_rate[num].item()}")

            wandb.log({custom_step: num,
                       f"{epoch}_tasks_success": success_rate[num].item()
                       })

        wandb.log({f"epoch{epoch}_average_success": average_success})
        log.info(f"Average success rate: {average_success}")