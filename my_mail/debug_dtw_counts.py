#!/usr/bin/env python3
"""
Debug script to understand DTW positive/negative pair counts.
Simulates the exact counting logic used in training.
"""

import torch
import numpy as np

def simulate_dtw_counting(batch_size=8, positive_threshold=0.1, negative_threshold=0.05):
    """Simulate DTW pair counting logic"""
    
    print(f"🧪 Simulating DTW counting for batch_size={batch_size}")
    print(f"  - Positive threshold: {positive_threshold}")
    print(f"  - Negative threshold: {negative_threshold}")
    
    # Create a mock DTW similarity matrix for this batch
    # Simulate the actual distribution you might see
    dtw_similarity = torch.zeros(batch_size, batch_size)
    
    # Add some positive pairs (high similarity)
    dtw_similarity[0, 1] = dtw_similarity[1, 0] = 0.15  # Positive pair
    dtw_similarity[2, 3] = dtw_similarity[3, 2] = 0.12  # Positive pair
    
    # Most pairs have very low similarity (negative pairs)
    for i in range(batch_size):
        for j in range(batch_size):
            if i != j and dtw_similarity[i, j] == 0:
                dtw_similarity[i, j] = np.random.uniform(0.0, 0.04)  # Random low similarity
    
    # Make sure diagonal is 0
    dtw_similarity.fill_diagonal_(0)
    
    print(f"\n📊 DTW Similarity Matrix:")
    print(dtw_similarity.numpy())
    
    # Apply the same logic as in training
    positive_mask = dtw_similarity > positive_threshold
    negative_mask = dtw_similarity < negative_threshold
    
    # Remove diagonal elements
    diagonal_mask = torch.eye(batch_size, dtype=torch.bool)
    positive_mask = positive_mask & ~diagonal_mask
    negative_mask = negative_mask & ~diagonal_mask
    
    # Count pairs
    positive_pairs = positive_mask.sum().item()
    negative_pairs = negative_mask.sum().item()
    total_pairs = batch_size * batch_size - batch_size  # Exclude diagonal
    neutral_pairs = total_pairs - positive_pairs - negative_pairs
    
    print(f"\n📈 Pair Counts:")
    print(f"  - Total pairs (excluding diagonal): {total_pairs}")
    print(f"  - Positive pairs (DTW > {positive_threshold}): {positive_pairs}")
    print(f"  - Negative pairs (DTW < {negative_threshold}): {negative_pairs}")
    print(f"  - Neutral pairs ({negative_threshold} ≤ DTW ≤ {positive_threshold}): {neutral_pairs}")
    
    print(f"\n🔍 Verification:")
    print(f"  - Positive + Negative + Neutral = {positive_pairs + negative_pairs + neutral_pairs}")
    print(f"  - Should equal total pairs: {total_pairs}")
    print(f"  - Match: {'✅' if positive_pairs + negative_pairs + neutral_pairs == total_pairs else '❌'}")
    
    # Show which pairs are positive/negative
    print(f"\n📋 Detailed Breakdown:")
    positive_indices = torch.where(positive_mask)
    negative_indices = torch.where(negative_mask)
    
    print(f"  Positive pairs:")
    for i, j in zip(positive_indices[0], positive_indices[1]):
        print(f"    - ({i.item()}, {j.item()}): similarity = {dtw_similarity[i, j]:.3f}")
    
    print(f"  First few negative pairs:")
    for idx, (i, j) in enumerate(zip(negative_indices[0][:10], negative_indices[1][:10])):
        print(f"    - ({i.item()}, {j.item()}): similarity = {dtw_similarity[i, j]:.3f}")
    if len(negative_indices[0]) > 10:
        print(f"    - ... and {len(negative_indices[0]) - 10} more negative pairs")
    
    return {
        'batch_size': batch_size,
        'total_pairs': total_pairs,
        'positive_pairs': positive_pairs,
        'negative_pairs': negative_pairs,
        'neutral_pairs': neutral_pairs,
        'dtw_similarity': dtw_similarity
    }

def analyze_real_training_scenario():
    """Analyze the specific scenario from training logs"""
    print(f"\n🎯 Analyzing Real Training Scenario:")
    print(f"  - DTW Pos = 2")
    print(f"  - DTW Neg = 62")
    
    # Reverse engineer the batch size
    total_reported = 2 + 62  # 64
    
    # If total_pairs = batch_size² - batch_size = 64
    # Then batch_size² - batch_size - 64 = 0
    # Solving: batch_size = (1 + sqrt(1 + 256)) / 2 ≈ 8.5
    
    # Let's try batch_size = 8: 8² - 8 = 56 (too small)
    # Let's try batch_size = 9: 9² - 9 = 72 (close, but 2+62=64 < 72)
    
    possible_batch_sizes = [8, 9, 10, 16]
    
    for bs in possible_batch_sizes:
        total_pairs = bs * bs - bs
        remaining = total_pairs - 2 - 62
        print(f"  - Batch size {bs}: total_pairs={total_pairs}, remaining={remaining}")
        
        if remaining >= 0:
            print(f"    ✅ Possible: {2} pos + {62} neg + {remaining} neutral = {total_pairs}")
        else:
            print(f"    ❌ Impossible: {2} pos + {62} neg > {total_pairs}")

def test_different_batch_sizes():
    """Test different batch sizes to understand the pattern"""
    print(f"\n🔬 Testing Different Batch Sizes:")
    
    batch_sizes = [4, 8, 16, 32]
    
    for bs in batch_sizes:
        print(f"\n--- Batch Size {bs} ---")
        result = simulate_dtw_counting(bs)
        
        # Calculate ratios
        pos_ratio = result['positive_pairs'] / result['total_pairs'] * 100
        neg_ratio = result['negative_pairs'] / result['total_pairs'] * 100
        
        print(f"  Ratios: {pos_ratio:.1f}% positive, {neg_ratio:.1f}% negative")

def main():
    print("🚀 DTW Pair Counting Debug Analysis")
    print("=" * 50)
    
    # Test 1: Simulate typical scenario
    simulate_dtw_counting(batch_size=8)
    
    # Test 2: Analyze real training scenario
    analyze_real_training_scenario()
    
    # Test 3: Test different batch sizes
    test_different_batch_sizes()
    
    print(f"\n💡 Key Insights:")
    print(f"  1. DTW Pos/Neg counts exclude diagonal elements (self-pairs)")
    print(f"  2. Most pairs are negative due to sparse DTW similarity")
    print(f"  3. Only ~2.5% of pairs have non-zero DTW similarity (by design)")
    print(f"  4. Positive pairs are rare and valuable for contrastive learning")
    print(f"  5. The counts depend on both batch composition and thresholds")

if __name__ == "__main__":
    main()
