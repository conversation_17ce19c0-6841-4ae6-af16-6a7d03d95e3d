#!/usr/bin/env python3
"""
Simple DC rollout script that directly follows DC training approach.
Load pretrained model first, then perform rollout evaluation.
"""

import os
import sys
import torch
import logging
import argparse
from pathlib import Path

# Add MaIL to path
sys.path.insert(0, '/home/<USER>/work/MaIL')

import hydra
from omegaconf import DictConfig, OmegaConf
from agents.utils import sim_framework_path

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
log = logging.getLogger(__name__)

def load_pretrained_model(agent, model_path, device="cuda"):
    """Load pretrained model weights"""
    log.info(f"🔄 Loading pretrained model from: {model_path}")
    
    if not os.path.exists(model_path):
        raise FileNotFoundError(f"Model not found: {model_path}")
    
    # Load checkpoint
    checkpoint = torch.load(model_path, map_location=device)
    
    # Load model state dict
    if 'model_state_dict' in checkpoint:
        agent.model.load_state_dict(checkpoint['model_state_dict'])
        log.info("✅ Loaded model_state_dict from checkpoint")
    elif 'state_dict' in checkpoint:
        agent.model.load_state_dict(checkpoint['state_dict'])
        log.info("✅ Loaded state_dict from checkpoint")
    else:
        agent.model.load_state_dict(checkpoint)
        log.info("✅ Loaded checkpoint as state_dict")
    
    # Set to evaluation mode
    agent.model.eval()
    
    # Log model info
    total_params = sum(p.numel() for p in agent.model.parameters())
    log.info(f"📊 Total model parameters: {total_params:,}")
    
    return agent

@hydra.main(config_path="config", config_name="dc_training.yaml", version_base="1.1")
def main(cfg: DictConfig):
    log.info("🚀 Starting Simple DC Rollout")
    log.info(f"Device: {cfg.device}")
    
    # Set device
    device = torch.device(cfg.device if torch.cuda.is_available() else "cpu")
    log.info(f"Using device: {device}")
    
    try:
        # Create agent using DC training configuration
        log.info("🔧 Creating DC agent...")
        agent = hydra.utils.instantiate(cfg.agents)
        log.info(f"✅ Agent created: {type(agent).__name__}")
        
        # Load pretrained model
        pretrained_path = "/home/<USER>/work/MaIL/MaIL/logs/libero_goal/multi_task_diffusion_transformer/sweeps/goal_ddpm_transformer_encdec/2025-07-22/21-53-31/eval_best_ddpm.pth"
        agent = load_pretrained_model(agent, pretrained_path, cfg.device)
        
        # Setup environment simulation
        log.info("🌍 Setting up environment simulation...")
        sys.path.append(sim_framework_path)
        from simulation.benchmark_sim_goal import MultiTaskSim
        
        # Create environment
        env_sim = MultiTaskSim(
            task_suite_name=cfg.task_suite,
            obs_keys=cfg.obs_keys,
            dataset_keys=cfg.dataset_keys,
            dataset_path=cfg.dataset_path,
            observation=cfg.observation,
            device=cfg.device
        )
        log.info("✅ Environment created successfully")
        
        # Run rollout evaluation
        log.info("🎮 Starting rollout evaluation...")
        
        # Assign CPUs for parallel evaluation
        import multiprocessing as mp
        available_cpus = list(range(mp.cpu_count()))
        n_cores = min(2, len(available_cpus))  # Use 2 cores
        assign_cpus = available_cpus[:n_cores]
        
        log.info(f"💻 Using {n_cores} CPU cores: {assign_cpus}")
        
        # Run evaluation with 10 episodes per task
        episodes_per_task = 10
        log.info(f"📋 Episodes per task: {episodes_per_task}")
        
        success_array = env_sim.test_agent(agent, assign_cpus, epoch="simple_dc_rollout")
        
        # Log results
        log.info("📊 Rollout Results:")
        for i, success_rate in enumerate(success_array.mean(axis=1)):
            log.info(f"Task {i}: {success_rate:.3f}")
        
        overall_success = success_array.mean()
        log.info(f"Overall Success Rate: {overall_success:.3f}")
        
        log.info("🎉 Simple DC rollout completed successfully!")
        
    except Exception as e:
        log.error(f"❌ Error during rollout: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    # Parse command line arguments
    parser = argparse.ArgumentParser()
    parser.add_argument("--episodes", type=int, default=10, help="Episodes per task")
    parser.add_argument("--n_cores", type=int, default=2, help="Number of CPU cores")
    args = parser.parse_args()
    
    # Run main function
    exit_code = main()
    sys.exit(exit_code)
