#!/usr/bin/env python3
"""
使用训练时配置运行完整评估 (非交互式)
评估所有10个libero_goal任务，每个任务20轮
严格按照 benchmark_libero_goal_diffusion_transformer.yaml 配置
"""

import os
import sys
from pathlib import Path

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from load_and_simulate import PretrainedModelSimulator


def main():
    """使用训练配置运行完整评估"""
    print("🚀 使用训练配置运行完整评估 (所有10个libero_goal任务)")
    print("📋 配置严格按照: benchmark_libero_goal_diffusion_transformer.yaml")
    
    # 配置路径
    config_path = "config/benchmark_libero_goal_diffusion_transformer.yaml"
    model_weights_path = "MaIL/logs/libero_goal/multi_task_diffusion_transformer/sweeps/goal_ddpm_transformer_encdec/2025-07-22/21-53-31/eval_best_ddpm.pth"
    
    # 验证路径
    if not Path(config_path).exists():
        print(f"❌ 配置文件不存在: {config_path}")
        return
    if not Path(model_weights_path).exists():
        print(f"❌ 模型权重文件不存在: {model_weights_path}")
        return
    
    print("✅ 文件路径验证通过")
    
    # 使用训练时的完全相同配置
    simulator = PretrainedModelSimulator(
        config_path=config_path,
        model_weights_path=model_weights_path,
        task_suite="libero_goal",              # 来自配置文件
        num_episodes=20,                       # num_episode: 20
        max_steps_per_episode=600,             # max_step_per_episode: 600
        seed=4,                               # seed: 4
        device="cuda",                        # device: 'cuda'
        save_video=False,                      # save_video: True
        max_videos_per_task=3,                # max_videos_per_task: 3
        data_aug=False,                       # data_aug: False
        aug_factor=0.1,                       # aug_factor: 0.1
        verbose=True
    )
    
    print("\n📊 使用的配置参数:")
    print("   - 任务套件: libero_goal (10个任务)")
    print("   - 每任务episodes: 20 (vs 之前的3)")
    print("   - 最大步数: 600")
    print("   - 随机种子: 4 (vs 之前的42)")
    print("   - 数据增强: False")
    print("   - 增强强度: 0.1 (vs 之前的0.02)")
    print("   - 总episodes: 200 (10任务 × 20轮)")
    
    try:
        # 加载模型
        print("\n📂 加载模型...")
        simulator.load_agent()
        print("✅ 模型加载成功!")
        
        # 运行完整评估
        print("\n🎯 开始完整评估所有10个任务 (每个任务20轮)...")
        print("⏱️  预计需要时间: 约30-60分钟 (取决于任务复杂度)")
        results = simulator.evaluate_all_tasks()
        
        # 保存结果
        simulator.results = results
        output_file = simulator.save_results("training_config_full_evaluation.json")
        
        # 打印结果
        print("\n" + "="*80)
        print("🎉 训练配置完整评估完成!")
        print("="*80)
        print(f"📊 整体成功率: {results['overall_success_rate']:.2%}")
        print(f"📈 平均任务成功率: {results['avg_task_success_rate']:.2%}")
        print(f"📁 结果文件: {output_file}")
        print("="*80)
        
        # 详细结果
        print("\n📋 各任务详细结果:")
        for task_id, task_result in results['task_results'].items():
            success_rate = task_result['success_rate']
            success_count = task_result['success_count']
            total_episodes = task_result['total_episodes']
            avg_steps = task_result['avg_steps']
            print(f"  任务 {task_id}: {success_rate:.1%} ({success_count}/{total_episodes}) - 平均步数: {avg_steps:.1f}")
        
        # 对比分析和详细统计
        print(f"\n🔍 完整评估统计:")
        print(f"   📊 总episodes: {results['total_episodes']}")
        print(f"   ✅ 成功episodes: {results['total_success_count']}")
        print(f"   📈 整体成功率: {results['overall_success_rate']:.1%}")
        print(f"   📊 任务成功率标准差: {results.get('std_task_success_rate', 0):.3f}")

        # 找出最佳和最差任务
        task_rates = [(tid, tres['success_rate']) for tid, tres in results['task_results'].items()]
        task_rates.sort(key=lambda x: x[1], reverse=True)

        print(f"\n🏆 表现最佳的3个任务:")
        for i, (task_id, rate) in enumerate(task_rates[:3]):
            task_name = results['task_results'][task_id].get('task_description', f'任务{task_id}')
            print(f"   {i+1}. 任务{task_id}: {rate:.1%} - {task_name}")

        print(f"\n📉 表现最差的3个任务:")
        for i, (task_id, rate) in enumerate(task_rates[-3:]):
            task_name = results['task_results'][task_id].get('task_description', f'任务{task_id}')
            print(f"   {i+1}. 任务{task_id}: {rate:.1%} - {task_name}")

        print(f"\n💡 与之前快速测试对比:")
        print(f"   - 之前 (3任务×3轮, seed=42): 11.1% 成功率")
        print(f"   - 现在 (10任务×20轮, seed=4): {results['overall_success_rate']:.1%} 成功率")
        print(f"   - 评估规模: 9 episodes → {results['total_episodes']} episodes")
        
    except Exception as e:
        print(f"❌ 运行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
