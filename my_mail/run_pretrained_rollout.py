#!/usr/bin/env python3
"""
运行预训练模型的LIBERO Rollout评估脚本
使用指定的预训练模型进行rollout测试
"""

import os
import sys
import logging
from pathlib import Path

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

from standalone_rollout import LiberoRolloutEvaluator, set_seed_everywhere


def main():
    # 设置随机种子
    seed = 42
    set_seed_everywhere(seed)
    
    # 配置路径
    config_path = "/home/<USER>/work/MaIL/config/benchmark_libero_goal_diffusion_transformer.yaml"
    model_weights_path = "/home/<USER>/work/MaIL/MaIL/logs/libero_goal/multi_task_diffusion_transformer/sweeps/goal_ddpm_transformer_encdec/2025-07-24/15-04-28/checkpoints/goal_ddpm_transformer_encdec_20250724_150436/best_checkpoint.pth"
    
    # 验证文件存在
    if not os.path.exists(config_path):
        print(f"❌ 配置文件不存在: {config_path}")
        return 1
    
    if not os.path.exists(model_weights_path):
        print(f"❌ 模型权重文件不存在: {model_weights_path}")
        return 1
    
    print(f"✅ 配置文件: {config_path}")
    print(f"✅ 模型权重: {model_weights_path}")
    
    # 创建rollout评估器
    evaluator = LiberoRolloutEvaluator(
        task_suite="libero_goal",  # 使用libero_goal任务套件
        num_episodes=5,            # 每个任务5轮测试
        max_steps_per_episode=600, # 每轮最多600步
        seed=seed,
        device="cuda",
        save_video=True,           # 保存视频
        data_aug=False             # 关闭数据增强
    )
    
    try:
        # 加载预训练模型
        print("\n🔄 正在加载预训练模型...")
        evaluator.load_agent(config_path, model_weights_path)
        print("✅ 模型加载成功!")
        
        # 运行评估 - 测试前3个任务作为示例
        print("\n🚀 开始rollout评估...")
        task_ids = [0, 1, 2]  # 可以修改这里来测试不同的任务
        results = evaluator.evaluate_tasks(task_ids=task_ids, episodes_per_task=3)
        
        # 打印结果
        print(f"\n🎉 评估完成!")
        print(f"📊 整体成功率: {results['overall_success_rate']:.2%}")
        print(f"📈 总成功次数: {results['total_successes']}/{results['total_episodes']}")
        
        # 打印每个任务的成功率
        print(f"\n📋 各任务成功率:")
        for task_id, success_rate in results['task_success_rates'].items():
            print(f"   任务 {task_id}: {success_rate:.2%}")
        
        return 0
        
    except Exception as e:
        print(f"❌ 评估失败: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit(main()) 