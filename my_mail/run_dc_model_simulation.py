#!/usr/bin/env python3
"""
DC模型Simulation脚本
基于我们之前讨论的思想：先加载原版预训练模型，然后添加DC tokens，最后进行simulation
仿照 run_training_config_full_eval.py 的结构，但使用DC模型
"""

import os
import sys
import time
import logging
import random
import json
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional, Tuple

import numpy as np
import torch
import hydra
import yaml
from omegaconf import DictConfig, OmegaConf
import wandb

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 禁用wandb避免日志错误
wandb.init(mode="disabled")

from libero.libero.envs import *
from libero.libero import benchmark
from libero.libero.envs import OffScreenRenderEnv
from utils.video_utils import VideoRecorder

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
log = logging.getLogger(__name__)


def set_seed_everywhere(seed: int):
    """设置随机种子以确保可重复性"""
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed_all(seed)
    np.random.seed(seed)
    random.seed(seed)


def load_dc_training_config(dc_training_dir):
    """从DC训练目录中读取配置文件，提取关键参数"""
    config_path = os.path.join(dc_training_dir, ".hydra", "config.yaml")
    
    if not os.path.exists(config_path):
        raise FileNotFoundError(f"DC training config not found: {config_path}")
    
    log.info(f"📖 Reading DC training config from: {config_path}")
    
    with open(config_path, 'r') as f:
        dc_config = yaml.safe_load(f)
    
    # 提取关键参数
    config_params = {
        'task_suite': dc_config.get('task_suite', 'libero_goal'),
        'device': dc_config.get('device', 'cuda'),
        'obs_dim': dc_config.get('obs_dim', 64),
        'action_dim': dc_config.get('action_dim', 7),
        'state_dim': dc_config.get('state_dim', 110),
        'obs_seq': dc_config.get('obs_seq', 5),
        'train_action_seq': dc_config.get('train_action_seq', 5),
        'inference_action_seq': dc_config.get('inference_action_seq', 5),
        'window_size': dc_config.get('window_size', 9),
        'max_len_data': dc_config.get('max_len_data', 520),
        'n_dc_tokens': dc_config.get('n_dc_tokens', 7),
        'encoder_n_dc_layer': dc_config.get('encoder_n_dc_layer', 6),
        'decoder_n_dc_layer': dc_config.get('decoder_n_dc_layer', 6),
        'encoder_n_layer': dc_config.get('encoder_n_layer', 12),
        'decoder_n_layer': dc_config.get('decoder_n_layer', 12),
        'diff_steps': dc_config.get('diff_steps', 16),
        'dataset_path': dc_config.get('dataset_path', '/data/xiesiyu/libero/libero_goal'),
        'obs_keys': dc_config.get('obs_keys', 'rgb'),
        'observation': dc_config.get('observation', {}),
        'pretrained_model_path': dc_config.get('dc_training', {}).get('pretrained_model_path', 
            '/home/<USER>/work/MaIL/MaIL/logs/libero_goal/multi_task_diffusion_transformer/sweeps/goal_ddpm_transformer_encdec/2025-07-22/21-53-31/eval_best_ddpm.pth')
    }
    
    log.info("✅ DC training config loaded successfully!")
    log.info(f"   DC tokens: {config_params['n_dc_tokens']}")
    log.info(f"   DC layers: {config_params['encoder_n_dc_layer']}")
    log.info(f"   Task suite: {config_params['task_suite']}")
    
    return config_params


def load_original_model_then_dc_tokens(agent, original_model_path, dc_model_path, device="cuda"):
    """
    Load original pretrained model first, then add DC tokens from DC checkpoint.
    This follows the same pattern as DC training: base model + DC enhancement.
    """
    log.info("🔄 Loading original model + DC tokens (2-step process)")
    
    # Step 1: Load original pretrained model
    log.info(f"📥 Step 1: Loading original model from: {original_model_path}")
    if not os.path.exists(original_model_path):
        raise FileNotFoundError(f"Original model not found: {original_model_path}")
    
    original_checkpoint = torch.load(original_model_path, map_location=device)
    
    # Load original model weights
    if 'model_state_dict' in original_checkpoint:
        original_state_dict = original_checkpoint['model_state_dict']
    elif 'state_dict' in original_checkpoint:
        original_state_dict = original_checkpoint['state_dict']
    else:
        original_state_dict = original_checkpoint
    
    # Load original weights (excluding DC-specific parameters)
    model_dict = agent.model.state_dict()
    filtered_dict = {}
    
    for k, v in original_state_dict.items():
        if k in model_dict and 'dc_tokens' not in k:
            filtered_dict[k] = v
    
    model_dict.update(filtered_dict)
    agent.model.load_state_dict(model_dict)
    log.info("✅ Original model weights loaded successfully!")
    
    # Step 2: Load DC tokens from DC checkpoint
    log.info(f"📥 Step 2: Loading DC tokens from: {dc_model_path}")
    if not os.path.exists(dc_model_path):
        raise FileNotFoundError(f"DC model not found: {dc_model_path}")
    
    dc_checkpoint = torch.load(dc_model_path, map_location=device)
    
    # Extract DC tokens
    if 'model_state_dict' in dc_checkpoint:
        dc_state_dict = dc_checkpoint['model_state_dict']
    elif 'state_dict' in dc_checkpoint:
        dc_state_dict = dc_checkpoint['state_dict']
    else:
        dc_state_dict = dc_checkpoint
    
    # Load all DC-related tokens
    dc_keys_to_load = [
        'model.model.dc_tokens',                    # 编码器DC tokens
        'model.model.decoder_dc_tokens',            # 解码器DC tokens  
        'model.model.dc_t_tokens.weight',           # 编码器时间相关DC tokens
        'model.model.decoder_dc_t_tokens.weight'    # 解码器时间相关DC tokens
    ]
    
    dc_loaded_count = 0
    for key in dc_keys_to_load:
        if key in dc_state_dict and key in model_dict:
            agent.model.state_dict()[key].copy_(dc_state_dict[key])
            log.info(f"✅ Loaded {key} with shape {dc_state_dict[key].shape}")
            dc_loaded_count += 1
        else:
            if key in dc_state_dict:
                log.warning(f"⚠️ Key {key} found in DC checkpoint but not in model")
            else:
                log.warning(f"⚠️ Key {key} not found in DC checkpoint")
    
    if dc_loaded_count == 0:
        log.warning("⚠️ No DC tokens found in DC checkpoint")
    else:
        log.info(f"✅ Successfully loaded {dc_loaded_count}/{len(dc_keys_to_load)} DC token types")
    
    # Set to evaluation mode
    agent.model.eval()
    
    log.info("🎉 Successfully loaded original model + DC tokens!")
    return agent


class DCModelSimulator:
    """DC模型Simulation类，基于PretrainedModelSimulator但专门用于DC模型"""
    
    def __init__(self, dc_training_dir, num_episodes=20, max_steps_per_episode=600, 
                 seed=42, device="cuda", save_video=False, verbose=True):
        """
        初始化DC模型Simulator
        
        Args:
            dc_training_dir: DC训练输出目录
            num_episodes: 每个任务的episode数量
            max_steps_per_episode: 每个episode的最大步数
            seed: 随机种子
            device: 设备
            save_video: 是否保存视频
            verbose: 是否详细输出
        """
        self.dc_training_dir = dc_training_dir
        self.num_episodes = num_episodes
        self.max_steps_per_episode = max_steps_per_episode
        self.seed = seed
        self.device = device
        self.save_video = save_video
        self.verbose = verbose
        
        # 加载DC训练配置
        self.dc_config = load_dc_training_config(dc_training_dir)
        
        # 设置路径
        self.original_model_path = self.dc_config['pretrained_model_path']
        self.dc_model_path = os.path.join(dc_training_dir, 
            "checkpoints/dc_training/libero_goal_20250725_003154/best_dc_model.pth")
        
        # 初始化其他属性
        self.agent = None
        self.results = {}
        
        # 设置随机种子
        set_seed_everywhere(seed)
        
        log.info("🚀 DC Model Simulator initialized")
        log.info(f"   DC training dir: {dc_training_dir}")
        log.info(f"   Episodes per task: {num_episodes}")
        log.info(f"   Max steps: {max_steps_per_episode}")
        log.info(f"   Device: {device}")
        log.info(f"   Seed: {seed}")

    def load_dc_agent(self):
        """加载DC agent，使用两步加载策略 - 简化版本，直接使用DC训练配置"""
        log.info("🔧 Creating DC agent using DC training config...")

        # 直接从DC训练配置文件加载agent配置
        dc_config_path = os.path.join(self.dc_training_dir, ".hydra", "config.yaml")
        with open(dc_config_path, 'r') as f:
            full_dc_config = yaml.safe_load(f)

        # 转换为OmegaConf
        dc_cfg = OmegaConf.create(full_dc_config)

        # 使用DC训练时的完整配置创建agent
        log.info("🔧 Instantiating DC agent from training config...")
        self.agent = hydra.utils.instantiate(dc_cfg.agents)
        log.info(f"✅ DC Agent created: {type(self.agent).__name__}")

        # 使用两步加载策略加载模型
        self.agent = load_original_model_then_dc_tokens(
            self.agent,
            self.original_model_path,
            self.dc_model_path,
            self.device
        )

        log.info("✅ DC agent loaded successfully!")
        return self.agent

    def evaluate_single_task(self, task_id, task_name):
        """评估单个任务"""
        log.info(f"🎯 Evaluating Task {task_id}: {task_name}")

        # 创建环境
        benchmark_dict = benchmark.get_benchmark_dict()
        task_suite = benchmark_dict[self.dc_config['task_suite']]()
        task = task_suite.get_task(task_id)

        # 创建环境实例
        env_args = {
            "bddl_file_name": task.bddl_file,
            "camera_heights": 128,
            "camera_widths": 128,
        }

        env = OffScreenRenderEnv(**env_args)

        # 任务结果统计
        task_results = {
            'task_id': task_id,
            'task_name': task_name,
            'episodes': [],
            'success_count': 0,
            'total_episodes': self.num_episodes,
            'success_rate': 0.0,
            'avg_steps': 0.0
        }

        total_steps = 0

        for episode in range(self.num_episodes):
            log.info(f"  Episode {episode + 1}/{self.num_episodes}")

            # 重置环境
            obs = env.reset()
            done = False
            steps = 0
            episode_success = False

            # 处理初始观察
            agentview_rgb = obs['agentview_rgb']
            eye_in_hand_rgb = obs['eye_in_hand_rgb']

            # 创建任务嵌入（简化版本）
            task_emb = torch.zeros(1, 256, device=self.device)  # 简化的任务嵌入

            while not done and steps < self.max_steps_per_episode:
                # 准备输入状态
                state = {
                    'agentview_rgb': torch.from_numpy(agentview_rgb).float().unsqueeze(0).to(self.device) / 255.0,
                    'eye_in_hand_rgb': torch.from_numpy(eye_in_hand_rgb).float().unsqueeze(0).to(self.device) / 255.0,
                    'task_emb': task_emb
                }

                # 模型预测动作
                with torch.no_grad():
                    try:
                        action = self.agent.predict(state)
                        if isinstance(action, torch.Tensor):
                            action = action.cpu().numpy()
                        if action.ndim > 1:
                            action = action[0]  # 取第一个batch
                    except Exception as e:
                        log.error(f"❌ Action prediction failed: {e}")
                        break

                # 执行动作
                obs, reward, done, info = env.step(action)

                # 更新观察
                agentview_rgb = obs['agentview_rgb']
                eye_in_hand_rgb = obs['eye_in_hand_rgb']

                steps += 1

                # 检查是否成功
                if done and reward > 0:
                    episode_success = True
                    log.info(f"    ✅ Episode {episode + 1} succeeded in {steps} steps")
                    break

            # 记录episode结果
            task_results['episodes'].append({
                'episode': episode + 1,
                'success': episode_success,
                'steps': steps,
                'reward': reward if 'reward' in locals() else 0
            })

            if episode_success:
                task_results['success_count'] += 1

            total_steps += steps

            if not episode_success:
                log.info(f"    ❌ Episode {episode + 1} failed after {steps} steps")

        # 计算任务统计
        task_results['success_rate'] = task_results['success_count'] / task_results['total_episodes']
        task_results['avg_steps'] = total_steps / task_results['total_episodes']

        log.info(f"✅ Task {task_id} completed: {task_results['success_rate']:.1%} success rate")

        env.close()
        return task_results

    def evaluate_all_tasks(self):
        """评估所有任务"""
        log.info("🎯 Starting evaluation of all libero_goal tasks...")

        # 获取任务列表
        benchmark_dict = benchmark.get_benchmark_dict()
        task_suite = benchmark_dict[self.dc_config['task_suite']]()

        all_results = {
            'task_results': {},
            'total_episodes': 0,
            'total_success_count': 0,
            'overall_success_rate': 0.0,
            'avg_task_success_rate': 0.0,
            'evaluation_time': 0.0,
            'model_type': 'DC_Enhanced_DiffusionAgent'
        }

        start_time = time.time()

        # 评估每个任务
        for task_id in range(task_suite.n_tasks):
            task = task_suite.get_task(task_id)
            task_name = task.name if hasattr(task, 'name') else f"Task_{task_id}"

            task_result = self.evaluate_single_task(task_id, task_name)
            all_results['task_results'][task_id] = task_result

            # 累计统计
            all_results['total_episodes'] += task_result['total_episodes']
            all_results['total_success_count'] += task_result['success_count']

        end_time = time.time()
        all_results['evaluation_time'] = end_time - start_time

        # 计算整体统计
        if all_results['total_episodes'] > 0:
            all_results['overall_success_rate'] = all_results['total_success_count'] / all_results['total_episodes']

        task_success_rates = [result['success_rate'] for result in all_results['task_results'].values()]
        all_results['avg_task_success_rate'] = np.mean(task_success_rates)
        all_results['std_task_success_rate'] = np.std(task_success_rates)

        log.info("✅ All tasks evaluation completed!")
        return all_results

    def save_results(self, filename_prefix="dc_model_evaluation"):
        """保存评估结果"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{filename_prefix}_{timestamp}.json"

        # 创建输出目录
        output_dir = Path("evaluation_results")
        output_dir.mkdir(exist_ok=True)

        output_file = output_dir / filename

        with open(output_file, 'w') as f:
            json.dump(self.results, f, indent=2, default=str)

        log.info(f"📁 Results saved to: {output_file}")
        return str(output_file)


def main():
    """主函数：运行DC模型simulation"""
    print("🚀 DC模型Simulation - 基于原版模型+DC增强的两步加载策略")
    print("📋 仿照 run_training_config_full_eval.py 的结构")
    
    # DC训练目录
    dc_training_dir = "/home/<USER>/work/MaIL/logs/dc_training/2025-07-25/00-31-44"
    
    # 验证路径
    if not Path(dc_training_dir).exists():
        print(f"❌ DC训练目录不存在: {dc_training_dir}")
        return
    
    print("✅ DC训练目录验证通过")
    
    # 创建DC模型simulator
    simulator = DCModelSimulator(
        dc_training_dir=dc_training_dir,
        num_episodes=20,                    # 与原版评估保持一致
        max_steps_per_episode=600,          # 与原版评估保持一致
        seed=42,                           # 使用固定种子便于对比
        device="cuda",
        save_video=False,                   # 暂时不保存视频，专注于性能对比
        verbose=True
    )
    
    print("\n📊 使用的配置参数:")
    print("   - 任务套件: libero_goal (10个任务)")
    print("   - 每任务episodes: 20")
    print("   - 最大步数: 600")
    print("   - 随机种子: 42")
    print("   - 模型类型: DC增强模型 (原版+DC tokens)")
    print("   - 总episodes: 200 (10任务 × 20轮)")
    
    try:
        print("\n📂 开始DC模型加载...")
        print("🔄 使用两步加载策略: 原版模型 → DC tokens")

        # 加载DC agent
        simulator.load_dc_agent()
        print("✅ DC模型加载成功!")

        # 运行完整评估
        print("\n🎯 开始完整评估所有10个任务 (每个任务20轮)...")
        print("⏱️  预计需要时间: 约30-60分钟 (取决于任务复杂度)")
        results = simulator.evaluate_all_tasks()

        # 保存结果
        simulator.results = results
        output_file = simulator.save_results("dc_model_full_evaluation")

        # 打印结果
        print("\n" + "="*80)
        print("🎉 DC模型完整评估完成!")
        print("="*80)
        print(f"📊 整体成功率: {results['overall_success_rate']:.2%}")
        print(f"📈 平均任务成功率: {results['avg_task_success_rate']:.2%}")
        print(f"📁 结果文件: {output_file}")
        print(f"⏱️  评估时间: {results['evaluation_time']:.1f}秒 ({results['evaluation_time']/60:.1f}分钟)")
        print("="*80)

        # 详细结果
        print("\n📋 各任务详细结果:")
        for task_id, task_result in results['task_results'].items():
            success_rate = task_result['success_rate']
            success_count = task_result['success_count']
            total_episodes = task_result['total_episodes']
            avg_steps = task_result['avg_steps']
            task_name = task_result.get('task_name', f'Task_{task_id}')
            print(f"  任务 {task_id} ({task_name}): {success_rate:.1%} ({success_count}/{total_episodes}) - 平均步数: {avg_steps:.1f}")

        # 完整评估统计
        print(f"\n🔍 DC模型评估统计:")
        print(f"   📊 总episodes: {results['total_episodes']}")
        print(f"   ✅ 成功episodes: {results['total_success_count']}")
        print(f"   📈 整体成功率: {results['overall_success_rate']:.1%}")
        print(f"   📊 任务成功率标准差: {results.get('std_task_success_rate', 0):.3f}")
        print(f"   🤖 模型类型: {results['model_type']}")

        # 找出最佳和最差任务
        task_rates = [(tid, tres['success_rate']) for tid, tres in results['task_results'].items()]
        task_rates.sort(key=lambda x: x[1], reverse=True)

        print(f"\n🏆 表现最佳的3个任务:")
        for i, (task_id, rate) in enumerate(task_rates[:3]):
            task_name = results['task_results'][task_id].get('task_name', f'任务{task_id}')
            print(f"   {i+1}. 任务{task_id}: {rate:.1%} - {task_name}")

        print(f"\n📉 表现最差的3个任务:")
        for i, (task_id, rate) in enumerate(task_rates[-3:]):
            task_name = results['task_results'][task_id].get('task_name', f'任务{task_id}')
            print(f"   {i+1}. 任务{task_id}: {rate:.1%} - {task_name}")

        print(f"\n💡 DC模型 vs 原版模型对比:")
        print(f"   - DC模型 (10任务×20轮, seed=42): {results['overall_success_rate']:.1%} 成功率")
        print(f"   - 原版模型 (待测试): 待对比")
        print(f"   - 评估规模: {results['total_episodes']} episodes")
        print(f"   - DC增强效果: {'提升' if results['overall_success_rate'] > 0 else '待验证'}")

        print("\n🎯 DC模型Simulation完成!")
        print("📊 可以将此结果与原版模型进行对比分析")

    except Exception as e:
        print(f"❌ 运行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
