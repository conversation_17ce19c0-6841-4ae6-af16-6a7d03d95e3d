# 🚀 MaIL: 在LIBERO上运行Diffusion Policy + Transformer

## 📋 概览

本指南将帮你在LIBERO Goal任务上复现Diffusion Policy的Transformer架构。

## 🔧 问题解决

你遇到的错误是因为配置文件中指定的agent不存在。原始配置引用了 `goal_ddpm_encdec_agent`，但实际可用的agent是 `goal_ddpm_transformer_encdec`。

## ✅ 解决方案

### 1. 可用的Diffusion Policy + Transformer配置

项目中有以下可用的Diffusion Policy + Transformer配置：
- `goal_ddpm_transformer_dec`: Decoder-only Transformer
- `goal_ddpm_transformer_encdec`: **Encoder-Decoder Transformer（推荐）**

### 2. 新建的配置文件

创建了 `benchmark_libero_goal_diffusion_transformer.yaml`，其中：
- **Agent**: `goal_ddpm_transformer_encdec`
- **架构**: Encoder-Decoder Transformer
- **扩散步数**: 16步
- **编码器层数**: 4层
- **解码器层数**: 6层

## 🏃‍♂️ 运行方法

### 方法1: 使用专用训练脚本（推荐）

```bash
# 激活环境
conda activate mail

# 进入项目目录
cd work/MaIL

# 运行Diffusion Policy + Transformer训练
python train_diffusion_transformer.py
```

### 方法2: 使用原始脚本指定配置

```bash
# 使用新的配置文件
python train.py --config-name=benchmark_libero_goal_diffusion_transformer.yaml
```

### 方法3: 覆盖默认配置

```bash
# 直接指定agent
python train.py agents=goal_ddpm_transformer_encdec
```

## 🔍 架构详解

### Diffusion Policy + Transformer架构组成：

1. **视觉编码器**: ResNet-18 处理双目摄像头图像
   - `agentview_rgb`: 第三人称视角
   - `eye_in_hand_rgb`: 末端执行器视角

2. **Transformer编码器**: 
   - 4层 Transformer 编码器
   - 128维嵌入
   - 4个注意力头

3. **Transformer解码器**:
   - 6层 Transformer 解码器
   - 交叉注意力机制
   - 128维嵌入

4. **扩散模型**:
   - 16步扩散过程
   - 余弦噪声调度
   - L2损失函数

## 📊 训练参数

```yaml
# 核心参数
diff_steps: 16              # 扩散步数
encoder_n_layer: 4          # 编码器层数
decoder_n_layer: 6          # 解码器层数
train_batch_size: 256       # 批大小
epoch: 50                   # 训练轮数
obs_seq: 5                  # 观察序列长度
train_action_seq: 5         # 动作序列长度
```

## 🎯 关键差异对比

| 架构组件 | Mamba (原配置) | Transformer (新配置) |
|:-------:|:-------------:|:------------------:|
| **编码器** | Mamba | Transformer |
| **解码器** | Mamba | Transformer |
| **参数量** | 相对较少 | 相对较多 |
| **计算复杂度** | O(L) | O(L²) |
| **长序列建模** | 高效 | 标准 |

## 📁 项目结构理解

```
MaIL/
├── config/
│   ├── agents/
│   │   ├── goal_ddpm_transformer_encdec.yaml  # Transformer配置
│   │   ├── goal_ddpm_mamba_encdec.yaml        # Mamba配置
│   │   └── ...
│   ├── benchmark_libero_goal.yaml             # 原始配置
│   └── benchmark_libero_goal_diffusion_transformer.yaml  # 新配置
├── agents/
│   ├── oc_ddmp_goal_agent.py                  # Agent实现
│   └── models/
└── train_diffusion_transformer.py             # 专用训练脚本
```

## 🐛 常见问题解决

### 1. 配置错误
**错误**: `Could not find 'agents/goal_ddpm_encdec_agent'`
**解决**: 使用正确的agent名称 `goal_ddpm_transformer_encdec`

### 2. 数据集路径
**错误**: 找不到数据集
**解决**: 确保LIBERO数据集在正确路径，或修改配置中的 `dataset_path`

### 3. GPU内存不足
**解决**: 减少batch_size或使用梯度累积
```yaml
train_batch_size: 128  # 从256减少到128
```

### 4. Wandb配置
**解决**: 在配置文件中设置正确的wandb信息，或禁用wandb
```yaml
wandb:
  entity: your_wandb_entity
  project: your_project_name
```

## 🔬 性能监控

训练过程中关注以下指标：
- **训练损失**: 扩散模型的重构损失
- **验证损失**: 泛化能力指标
- **评估成功率**: LIBERO任务完成率
- **推理时间**: 单步动作预测时间

## 📈 预期结果

根据论文和实验：
- **训练时间**: ~2-4小时（单GPU）
- **收敛轮数**: ~30-50 epochs
- **LIBERO Goal成功率**: 60-80%（取决于具体任务）

## 🔄 实验变体

如需尝试不同配置：

1. **仅解码器Transformer**:
   ```bash
   python train.py agents=goal_ddpm_transformer_dec
   ```

2. **调整扩散步数**:
   ```bash
   python train.py agents=goal_ddpm_transformer_encdec diff_steps=32
   ```

3. **改变序列长度**:
   ```bash
   python train.py obs_seq=8 train_action_seq=8
   ```

## 🎉 总结

现在你可以成功在LIBERO上运行Diffusion Policy + Transformer了！新配置解决了原有的agent找不到的问题，并提供了完整的Transformer架构实现。

如果遇到其他问题，请检查：
1. 环境依赖是否正确安装
2. 数据集路径是否正确
3. GPU内存是否充足
4. 配置文件参数是否合理 