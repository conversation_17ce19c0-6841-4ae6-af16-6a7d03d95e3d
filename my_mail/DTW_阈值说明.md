# DTW 正负样本阈值确定方法

## 📊 CLASS 项目的阈值确定方法

### **1. DTW 距离到相似度的转换**

CLASS 项目使用了一个基于 **分位数 (quantile)** 的方法来将 DTW 距离转换为相似度分数：

```python
def _get_cdf_dist(self, dist, quantile=0.025):
    """Convert DTW distances to similarity scores using CDF (from CLASS)"""
    # Get upper triangular part (excluding diagonal)
    triu_mask = torch.triu(torch.ones_like(dist, dtype=torch.bool), diagonal=1)
    flat = dist[triu_mask]
    
    # Find top-k smallest distances (most similar pairs)
    k = int(flat.numel() * quantile)
    topk_values, topk_indices = torch.topk(flat, k, largest=False, sorted=True)
    
    # Create inverse CDF mapping: smallest distances get highest similarity scores
    inv_cdf = torch.linspace(1.0, 0.0, steps=k, device=dist.device)
    dist_vals = torch.zeros_like(flat, dtype=inv_cdf.dtype)
    dist_vals[topk_indices] = inv_cdf
    
    # Build symmetric similarity matrix
    similarity_matrix = torch.zeros_like(dist, dtype=dist_vals.dtype)
    similarity_matrix[triu_mask] = dist_vals
    similarity_matrix = similarity_matrix + similarity_matrix.T  # Make symmetric
    
    return similarity_matrix
```

### **2. 核心思想**

1. **分位数选择**: 使用 `quantile=0.025` (2.5%) 意味着只有最相似的 2.5% 的样本对会被赋予非零相似度分数
2. **逆 CDF 映射**: 最小的 DTW 距离 → 最高的相似度分数 (1.0)
3. **稀疏性**: 大部分样本对的相似度为 0，只有最相似的少数对有正值

### **3. 阈值的确定**

基于这个转换方法，正负样本阈值的确定逻辑如下：

#### **正样本阈值 (positive_threshold = 0.1)**
- **含义**: 相似度 > 0.1 的样本对被视为正样本
- **来源**: 由于使用了 2.5% 分位数，相似度分数在 [0, 1] 范围内分布
- **实际效果**: 大约选择了最相似的 0.25% 的样本对作为正样本 (0.1 * 2.5% ≈ 0.25%)

#### **负样本阈值 (negative_threshold = 0.05)**
- **含义**: 相似度 < 0.05 的样本对被视为负样本
- **来源**: 设置为正样本阈值的一半，确保有明确的边界
- **实际效果**: 包括了所有相似度为 0 的样本对 + 一些低相似度的样本对

### **4. 参数调优建议**

#### **分位数 (dist_quantile)**
```yaml
dist_quantile: 0.025  # 默认值，可调整范围 [0.01, 0.05]
```
- **更小值 (0.01)**: 更严格的相似性要求，更少的正样本
- **更大值 (0.05)**: 更宽松的相似性要求，更多的正样本

#### **正样本阈值 (positive_threshold)**
```yaml
positive_threshold: 0.1  # 默认值，可调整范围 [0.05, 0.2]
```
- **计算公式**: `positive_threshold ≈ 0.1 / sqrt(dist_quantile / 0.025)`
- **经验值**: 当 `dist_quantile=0.025` 时，`positive_threshold=0.1` 是一个经验最优值

#### **负样本阈值 (negative_threshold)**
```yaml
negative_threshold: 0.05  # 默认值，通常设为 positive_threshold 的一半
```

### **5. 实际数据分布示例**

假设有 10,000 个样本对：
- **总样本对数**: 10,000
- **分位数 2.5%**: 250 个样本对有非零相似度
- **正样本 (>0.1)**: 约 25-50 个样本对
- **负样本 (<0.05)**: 约 9,750+ 个样本对
- **中性样本 (0.05-0.1)**: 约 200-225 个样本对

### **6. 验证方法**

可以通过以下方式验证阈值设置的合理性：

```python
# 统计相似度分布
dtw_similarity = dataset.dtw_similarity_matrix
positive_pairs = (dtw_similarity > positive_threshold).sum()
negative_pairs = (dtw_similarity < negative_threshold).sum()
total_pairs = dtw_similarity.numel()

print(f"正样本比例: {positive_pairs / total_pairs:.4f}")
print(f"负样本比例: {negative_pairs / total_pairs:.4f}")
print(f"相似度均值: {dtw_similarity.mean():.4f}")
print(f"相似度标准差: {dtw_similarity.std():.4f}")
```

### **7. 调优策略**

1. **初始设置**: 使用默认值 `positive_threshold=0.1`, `negative_threshold=0.05`
2. **观察训练**: 监控正负样本比例和损失收敛情况
3. **动态调整**: 
   - 如果正样本太少 → 降低 `positive_threshold`
   - 如果负样本太少 → 提高 `negative_threshold`
   - 如果训练不稳定 → 调整 `dist_quantile`

### **8. 与其他方法的对比**

| 方法 | 阈值确定方式 | 优点 | 缺点 |
|------|-------------|------|------|
| **CLASS (分位数)** | 基于数据分布的分位数 | 自适应、数据驱动 | 需要预计算 DTW |
| **固定阈值** | 人工设定固定值 | 简单直接 | 不适应数据分布 |
| **聚类方法** | K-means 等聚类结果 | 考虑全局结构 | 计算复杂度高 |
| **学习阈值** | 端到端学习 | 最优化 | 训练复杂度高 |

## 🎯 总结

CLASS 项目的阈值确定方法是一个**数据驱动的自适应方法**：

1. **分位数方法**: 确保只有最相似的少数样本对成为正样本
2. **稀疏性**: 大部分样本对相似度为 0，避免噪声
3. **可调性**: 通过调整 `dist_quantile` 和阈值参数适应不同数据集
4. **经验验证**: 默认参数在多个数据集上表现良好

这种方法的核心思想是**"宁缺毋滥"** - 宁可少选一些正样本，也要确保选中的都是真正相似的样本对。
