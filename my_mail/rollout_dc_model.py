#!/usr/bin/env python3
"""
DC Model Rollout Script for MaIL project.
Load pretrained DC model and perform rollout evaluation.
Based on train_diffusion_transformer.py rollout section.
"""

import os
import logging
import random
import sys
import time
from datetime import datetime
from pathlib import Path
import multiprocessing as mp

import hydra
import numpy as np
import torch
from omegaconf import DictConfig, OmegaConf
import yaml

# Disable wandb
import os
os.environ["WANDB_MODE"] = "disabled"
import wandb

from agents.utils import sim_framework_path

# Setup logging
logging.basicConfig(level=logging.INFO)
log = logging.getLogger(__name__)

print(f"CUDA Available: {torch.cuda.is_available()}")
if torch.cuda.is_available():
    print(f"CUDA Device: {torch.cuda.get_device_name()}")

OmegaConf.register_new_resolver(
    "add", lambda *numbers: sum(numbers)
)
torch.cuda.empty_cache()


def set_seed_everywhere(seed):
    """Set random seed for reproducibility"""
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed_all(seed)
    np.random.seed(seed)
    random.seed(seed)


def load_dc_training_config(dc_training_dir):
    """
    从DC训练目录中读取配置文件，提取关键参数

    Args:
        dc_training_dir: DC训练输出目录路径

    Returns:
        dict: 包含DC配置参数的字典
    """
    config_path = os.path.join(dc_training_dir, ".hydra", "config.yaml")

    if not os.path.exists(config_path):
        raise FileNotFoundError(f"DC training config not found: {config_path}")

    log.info(f"📖 Reading DC training config from: {config_path}")

    with open(config_path, 'r') as f:
        dc_config = yaml.safe_load(f)

    # 提取关键参数
    config_params = {
        # 基础参数
        'task_suite': dc_config.get('task_suite', 'libero_goal'),
        'device': dc_config.get('device', 'cuda'),
        'obs_dim': dc_config.get('obs_dim', 64),
        'action_dim': dc_config.get('action_dim', 7),
        'state_dim': dc_config.get('state_dim', 110),

        # 序列参数
        'obs_seq': dc_config.get('obs_seq', 5),
        'train_action_seq': dc_config.get('train_action_seq', 5),
        'inference_action_seq': dc_config.get('inference_action_seq', 5),
        'window_size': dc_config.get('window_size', 9),
        'max_len_data': dc_config.get('max_len_data', 520),

        # DC参数
        'n_dc_tokens': dc_config.get('n_dc_tokens', 7),
        'encoder_n_dc_layer': dc_config.get('encoder_n_dc_layer', 6),
        'decoder_n_dc_layer': dc_config.get('decoder_n_dc_layer', 6),

        # 模型参数
        'encoder_n_layer': dc_config.get('encoder_n_layer', 12),
        'decoder_n_layer': dc_config.get('decoder_n_layer', 12),
        'diff_steps': dc_config.get('diff_steps', 16),

        # 数据集参数
        'dataset_path': dc_config.get('dataset_path', '/data/xiesiyu/libero/libero_goal'),
        'obs_keys': dc_config.get('obs_keys', 'rgb'),
        'observation': dc_config.get('observation', {}),

        # 预训练模型路径
        'pretrained_model_path': dc_config.get('dc_training', {}).get('pretrained_model_path',
            '/home/<USER>/work/MaIL/MaIL/logs/libero_goal/multi_task_diffusion_transformer/sweeps/goal_ddpm_transformer_encdec/2025-07-22/21-53-31/eval_best_ddpm.pth')
    }

    log.info("✅ DC training config loaded successfully!")
    log.info(f"   DC tokens: {config_params['n_dc_tokens']}")
    log.info(f"   DC layers: {config_params['encoder_n_dc_layer']}")
    log.info(f"   Task suite: {config_params['task_suite']}")

    return config_params


def setup_logging_for_rollout():
    """Setup logging system for rollout evaluation"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_dir = Path(f"logs/dc_rollout/{timestamp}")
    log_dir.mkdir(parents=True, exist_ok=True)
    
    # Configure logger
    logger = logging.getLogger("dc_rollout")
    logger.setLevel(logging.INFO)
    
    # Clear existing handlers
    logger.handlers.clear()
    
    # Create formatter
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # File handler
    file_handler = logging.FileHandler(log_dir / "dc_rollout.log")
    file_handler.setLevel(logging.INFO)
    file_handler.setFormatter(formatter)
    
    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)
    
    # Add handlers
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    
    logger.info("=" * 50)
    logger.info("🚀 DC Model Rollout Logging initialized")
    logger.info(f"📁 Log directory: {log_dir}")
    logger.info("=" * 50)
    
    return logger, log_dir


def load_original_model_then_dc_tokens(agent, original_model_path, dc_model_path, device="cuda"):
    """
    Load original pretrained model first, then add DC tokens from DC checkpoint.
    This follows the same pattern as DC training: base model + DC enhancement.

    Args:
        agent: DC agent instance
        original_model_path: Path to the original pretrained model
        dc_model_path: Path to the DC model checkpoint (contains DC tokens)
        device: Device to load the model on

    Returns:
        Agent with original model weights + DC tokens
    """
    log.info("🔄 Loading original model + DC tokens (2-step process)")

    # Step 1: Load original pretrained model
    log.info(f"📥 Step 1: Loading original model from: {original_model_path}")
    if not os.path.exists(original_model_path):
        raise FileNotFoundError(f"Original model not found: {original_model_path}")

    original_checkpoint = torch.load(original_model_path, map_location=device)

    # Load original model weights
    if 'model_state_dict' in original_checkpoint:
        original_state_dict = original_checkpoint['model_state_dict']
    elif 'state_dict' in original_checkpoint:
        original_state_dict = original_checkpoint['state_dict']
    else:
        original_state_dict = original_checkpoint

    # Load original weights (excluding DC-specific parameters)
    model_dict = agent.model.state_dict()
    filtered_dict = {}

    for k, v in original_state_dict.items():
        if k in model_dict and 'dc_tokens' not in k:
            filtered_dict[k] = v

    model_dict.update(filtered_dict)
    agent.model.load_state_dict(model_dict)
    log.info("✅ Original model weights loaded successfully!")

    # Step 2: Load DC tokens from DC checkpoint
    log.info(f"📥 Step 2: Loading DC tokens from: {dc_model_path}")
    if not os.path.exists(dc_model_path):
        raise FileNotFoundError(f"DC model not found: {dc_model_path}")

    dc_checkpoint = torch.load(dc_model_path, map_location=device)

    # Extract DC tokens
    if 'model_state_dict' in dc_checkpoint:
        dc_state_dict = dc_checkpoint['model_state_dict']
    elif 'state_dict' in dc_checkpoint:
        dc_state_dict = dc_checkpoint['state_dict']
    else:
        dc_state_dict = dc_checkpoint

    # Load all DC-related tokens
    dc_keys_to_load = [
        'model.model.dc_tokens',                    # 编码器DC tokens
        'model.model.decoder_dc_tokens',            # 解码器DC tokens
        'model.model.dc_t_tokens.weight',           # 编码器时间相关DC tokens
        'model.model.decoder_dc_t_tokens.weight'    # 解码器时间相关DC tokens
    ]

    dc_loaded_count = 0
    for key in dc_keys_to_load:
        if key in dc_state_dict and key in model_dict:
            agent.model.state_dict()[key].copy_(dc_state_dict[key])
            log.info(f"✅ Loaded {key} with shape {dc_state_dict[key].shape}")
            dc_loaded_count += 1
        else:
            if key in dc_state_dict:
                log.warning(f"⚠️ Key {key} found in DC checkpoint but not in model")
            else:
                log.warning(f"⚠️ Key {key} not found in DC checkpoint")

    if dc_loaded_count == 0:
        log.warning("⚠️ No DC tokens found in DC checkpoint")
    else:
        log.info(f"✅ Successfully loaded {dc_loaded_count}/{len(dc_keys_to_load)} DC token types")

    # Set to evaluation mode
    agent.model.eval()

    # Verification: Compare DC tokens between checkpoint and loaded model
    log.info("🔍 Verifying DC token loading...")
    verify_dc_token_loading(agent.model, dc_state_dict)

    log.info("🎉 Successfully loaded original model + DC tokens!")
    return agent


def verify_dc_token_loading(model, dc_checkpoint_state_dict):
    """
    验证DC tokens是否正确加载
    比较checkpoint中的DC tokens和已加载模型中的DC tokens
    """
    model_state_dict = model.state_dict()

    # 定义需要验证的DC token键
    dc_keys_to_verify = [
        'model.model.dc_tokens',
        'model.model.decoder_dc_tokens',
        'model.model.dc_t_tokens.weight',
        'model.model.decoder_dc_t_tokens.weight'
    ]

    log.info("📋 DC Token Verification Report:")
    log.info("=" * 60)

    all_verified = True

    for key in dc_keys_to_verify:
        if key in dc_checkpoint_state_dict and key in model_state_dict:
            checkpoint_tensor = dc_checkpoint_state_dict[key]
            model_tensor = model_state_dict[key]

            # 检查形状是否匹配
            shape_match = checkpoint_tensor.shape == model_tensor.shape

            # 检查值是否匹配（使用较小的容差）
            values_match = torch.allclose(checkpoint_tensor, model_tensor, atol=1e-6)

            # 计算差异统计
            diff = torch.abs(checkpoint_tensor - model_tensor)
            max_diff = diff.max().item()
            mean_diff = diff.mean().item()

            status = "✅ PASS" if (shape_match and values_match) else "❌ FAIL"
            log.info(f"{status} {key}")
            log.info(f"     Shape: {checkpoint_tensor.shape} -> {model_tensor.shape} ({'✓' if shape_match else '✗'})")
            log.info(f"     Values: max_diff={max_diff:.2e}, mean_diff={mean_diff:.2e} ({'✓' if values_match else '✗'})")

            if not (shape_match and values_match):
                all_verified = False

        elif key in dc_checkpoint_state_dict:
            log.info(f"❌ MISSING {key} - Found in checkpoint but not in model")
            all_verified = False
        elif key in model_state_dict:
            log.info(f"⚠️  EXTRA {key} - Found in model but not in checkpoint")
        else:
            log.info(f"❌ ABSENT {key} - Not found in either checkpoint or model")
            all_verified = False

    log.info("=" * 60)

    if all_verified:
        log.info("🎉 All DC tokens verified successfully!")
    else:
        log.warning("⚠️ Some DC tokens failed verification!")

    # 额外检查：显示模型中实际的DC token信息
    log.info("📊 Current Model DC Token Status:")
    if hasattr(model, 'model') and hasattr(model.model, 'dc_tokens'):
        dc_tokens = model.model.dc_tokens
        if dc_tokens is not None:
            log.info(f"   dc_tokens: {dc_tokens.shape} on {dc_tokens.device}")
        else:
            log.warning("   dc_tokens: None")

    if hasattr(model, 'model') and hasattr(model.model, 'decoder_dc_tokens'):
        decoder_dc_tokens = model.model.decoder_dc_tokens
        if decoder_dc_tokens is not None:
            log.info(f"   decoder_dc_tokens: {decoder_dc_tokens.shape} on {decoder_dc_tokens.device}")
        else:
            log.warning("   decoder_dc_tokens: None")

    return all_verified


def load_original_model_only(agent, original_model_path, device="cuda"):
    """
    Load only the original pretrained model (no DC tokens)

    Args:
        agent: Agent instance
        original_model_path: Path to the original pretrained model
        device: Device to load the model on

    Returns:
        Agent with original model weights
    """
    log.info(f"🔄 Loading original model from: {original_model_path}")

    if not os.path.exists(original_model_path):
        raise FileNotFoundError(f"Original model not found: {original_model_path}")

    # Load checkpoint
    checkpoint = torch.load(original_model_path, map_location=device)

    # Load model state dict
    if 'model_state_dict' in checkpoint:
        agent.model.load_state_dict(checkpoint['model_state_dict'])
        log.info("✅ Loaded model_state_dict from checkpoint")
    elif 'state_dict' in checkpoint:
        agent.model.load_state_dict(checkpoint['state_dict'])
        log.info("✅ Loaded state_dict from checkpoint")
    else:
        # Assume the checkpoint is the state dict itself
        agent.model.load_state_dict(checkpoint)
        log.info("✅ Loaded checkpoint as state_dict")

    # Set to evaluation mode
    agent.model.eval()

    # Log model info
    total_params = sum(p.numel() for p in agent.model.parameters())
    log.info(f"📊 Total model parameters: {total_params:,}")
    log.info("✅ Original model loaded successfully!")

    return agent


def log_system_info():
    """Log system information"""
    log.info("🖥️  System Information:")
    log.info(f"   CUDA Available: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        log.info(f"   CUDA Device: {torch.cuda.get_device_name()}")
        log.info(f"   CUDA Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
    
    log.info(f"   Python Version: {sys.version}")
    log.info(f"   PyTorch Version: {torch.__version__}")
    log.info(f"   Available CPUs: {mp.cpu_count()}")


def log_agent_info(agent, cfg):
    """Log agent and configuration information"""
    log.info("📝 Agent Configuration:")
    log.info(f"   Agent Type: {type(agent).__name__}")
    log.info(f"   Task Suite: {cfg.task_suite}")
    log.info(f"   Device: {cfg.device}")
    log.info(f"   Seed: {cfg.seed}")
    
    # Log model information if available
    if hasattr(agent, 'model'):
        total_params = sum(p.numel() for p in agent.model.parameters())
        trainable_params = sum(p.numel() for p in agent.model.parameters() if p.requires_grad)
        log.info(f"   Total Parameters: {total_params:,}")
        log.info(f"   Trainable Parameters: {trainable_params:,}")


@hydra.main(config_path="config", config_name="original_rollout.yaml", version_base="1.1")
def main(cfg: DictConfig) -> None:
    """
    Main function for DC model rollout evaluation
    """
    # Setup logging
    logger, log_dir = setup_logging_for_rollout()
    
    log.info("🚀 Starting Original Model Rollout Evaluation")
    log.info("📊 Loading pretrained original model for evaluation")
    
    # Set seed
    set_seed_everywhere(cfg.seed)
    log.info(f"🎲 Random seed set to: {cfg.seed}")

    # Initialize wandb in disabled mode
    wandb.init(mode="disabled", project="dc_rollout")
    log.info("📊 Wandb initialized in disabled mode")

    # Log system information
    log_system_info()
    
    try:
        # Load DC training configuration
        dc_training_dir = "/home/<USER>/work/MaIL/logs/dc_training/2025-07-25/00-31-44"
        dc_config = load_dc_training_config(dc_training_dir)

        # Update cfg with DC training parameters
        log.info("🔧 Updating configuration with DC training parameters...")
        for key, value in dc_config.items():
            if hasattr(cfg, key):
                setattr(cfg, key, value)
                log.info(f"   Updated {key}: {value}")

        # Instantiate agent
        log.info("🔧 Instantiating DC agent...")
        agent = hydra.utils.instantiate(cfg.agents)
        log.info(f"✅ Agent created: {type(agent).__name__}")

        # Log agent information
        log_agent_info(agent, cfg)

        # Load original model + DC tokens (2-step process)
        original_model_path = dc_config['pretrained_model_path']
        dc_model_path = os.path.join(dc_training_dir, "checkpoints/dc_training/libero_goal_20250725_003154/best_dc_model.pth")

        log.info("🔄 Loading original model + DC tokens...")
        log.info(f"   Original model: {original_model_path}")
        log.info(f"   DC checkpoint: {dc_model_path}")
        agent = load_original_model_then_dc_tokens(agent, original_model_path, dc_model_path, cfg.device)
        
        # Evaluation phase with detailed logging
        log.info("🧪 Starting rollout evaluation phase...")
        
        eval_start_time = time.time()
        
        # Setup CPU assignment
        num_cpu = mp.cpu_count()
        cpu_set = list(range(num_cpu))
        log.info(f"💻 System CPUs available: {num_cpu}")
        
        # Use n_cores from config, default to 5 if not specified
        n_cores = getattr(cfg, 'n_cores', 5)
        assign_cpus = cpu_set[cfg.seed * n_cores:cfg.seed * n_cores + n_cores]
        log.info(f"🔗 Assigned CPUs for evaluation: {assign_cpus}")
        
        # Create environment simulation
        log.info("🌍 Creating environment simulation...")
        
        # Create simulation config if not present
        if not hasattr(cfg, 'simulation'):
            # Create default simulation config for libero_goal
            simulation_cfg = {
                '_target_': 'simulation.benchmark_sim.BenchmarkSim',
                'task_suite': cfg.task_suite,
                'data_aug': False,
                'aug_factor': 0.1,
                'num_episode': 50,  # Default number of episodes
                'max_step_per_episode': 520,
                'render': False,
                'use_eye_in_hand': True,
                'n_cores': n_cores
            }
            cfg.simulation = OmegaConf.create(simulation_cfg)
        
        env_sim = hydra.utils.instantiate(cfg.simulation)
        log.info("✅ Environment simulation created successfully")
        
        # Log evaluation configuration
        if hasattr(cfg.simulation, 'num_episode'):
            log.info(f"📋 Evaluation episodes per task: {cfg.simulation.num_episode}")
        
        log.info("🎮 Running DC agent rollout evaluation...")
        
        try:
            # Run evaluation using the same method as in train_diffusion_transformer.py
            env_sim.test_agent(agent, assign_cpus, epoch="dc_rollout")
            
            eval_time = time.time() - eval_start_time
            log.info(f"✅ Rollout evaluation completed successfully! Time: {eval_time:.1f}s")
            
        except Exception as e:
            log.error(f"❌ Rollout evaluation failed: {e}")
            raise
        
        # Final logging
        log.info("🎉 Original model rollout evaluation finished successfully!")
        log.info(f"⏱️  Total execution time: {eval_time:.1f}s ({eval_time/60:.1f} minutes)")
        log.info(f"📁 All logs saved to: {log_dir}")

        log.info("=" * 50)
        log.info("🏁 Original model rollout evaluation completed! Check the logs for detailed results.")
        log.info(f"📂 Log files location: {log_dir}")
        log.info("=" * 50)
        
    except Exception as e:
        log.error(f"💥 Fatal error occurred: {e}")
        log.error("Check the log files for detailed error information")
        raise


if __name__ == "__main__":
    main()
