#!/bin/bash

echo "MaIL Environment Setup - Step by Step Installation"
echo "=================================================="

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to install system dependencies
install_system_deps() {
    echo "Step 1: Installing system dependencies..."
    
    # Install cmake if not exists
    if ! command_exists cmake; then
        echo "Installing cmake..."
        conda install cmake -c conda-forge -y
    else
        echo "cmake already installed"
    fi
    
    # Check for OpenGL libraries (for Linux)
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        echo "Note: Make sure OpenGL libraries are installed:"
        echo "  sudo apt-get install libgl1-mesa-dev libglu1-mesa-dev"
        echo "  sudo apt-get install build-essential"
    fi
}

# Function to install core packages
install_core_deps() {
    echo ""
    echo "Step 2: Installing core dependencies..."
    
    # Create a minimal requirements file first
    cat > requirements_core.txt << EOF
hydra-core>=1.3.0
numpy>=1.24.0,<2.0.0
wandb>=0.16.0
easydict>=1.10
transformers>=4.35.0
opencv-python>=4.8.0
einops>=0.7.0
matplotlib>=3.7.0
cloudpickle>=2.2.0
future>=0.18.2
imageio[ffmpeg]>=2.31.0
EOF
    
    pip install -r requirements_core.txt
}

# Function to install robotics packages
install_robotics_deps() {
    echo ""
    echo "Step 3: Installing robotics dependencies..."
    
    # Install robomimic first
    pip install robomimic>=0.3.0
    
    # Install gym
    pip install gym>=0.26.0
    
    # Try to install bddl
    pip install bddl>=2.0.0
    
    # Install thop
    pip install thop>=0.1.1-2209072238
}

# Function to install optional packages
install_optional_deps() {
    echo ""
    echo "Step 4: Installing optional dependencies..."
    
    # Try to install egl_probe
    echo "Attempting to install egl_probe..."
    if pip install egl-probe==1.0.2; then
        echo "egl_probe installed successfully"
        
        # Now try robosuite
        echo "Attempting to install robosuite..."
        pip install robosuite>=1.4.0
    else
        echo "Warning: egl_probe installation failed. Skipping robosuite."
        echo "You may need to install robosuite manually later."
    fi
}

# Function to install PyTorch
install_pytorch() {
    echo ""
    echo "Step 5: Installing PyTorch..."
    echo "Select your CUDA version:"
    echo "1) CUDA 12.1 (recommended)"
    echo "2) CUDA 12.4 (latest)"
    echo "3) CUDA 11.8 (older systems)"
    echo "4) CPU only"
    echo "5) Skip PyTorch installation"
    echo ""
    read -p "Enter choice (1-5): " choice

    case $choice in
        1)
            pip install torch==2.1.0+cu121 torchvision==0.16.0+cu121 torchaudio==2.1.0+cu121 \
                --index-url https://download.pytorch.org/whl/cu121
            ;;
        2)
            pip install torch==2.4.0+cu124 torchvision==0.19.0+cu124 torchaudio==2.4.0+cu124 \
                --index-url https://download.pytorch.org/whl/cu124
            ;;
        3)
            pip install torch==2.1.0+cu118 torchvision==0.16.0+cu118 torchaudio==2.1.0+cu118 \
                --index-url https://download.pytorch.org/whl/cu118
            ;;
        4)
            pip install torch==2.1.0+cpu torchvision==0.16.0+cpu torchaudio==2.1.0+cpu \
                --index-url https://download.pytorch.org/whl/cpu
            ;;
        5)
            echo "Skipping PyTorch installation"
            ;;
        *)
            echo "Invalid choice. Skipping PyTorch installation."
            ;;
    esac
}

# Function to verify installation
verify_installation() {
    echo ""
    echo "Step 6: Verifying installation..."
    
    python -c "
import sys
print(f'Python version: {sys.version}')

try:
    import numpy as np
    print(f'NumPy version: {np.__version__}')
except ImportError:
    print('NumPy: Not installed')

try:
    import torch
    print(f'PyTorch version: {torch.__version__}')
    print(f'CUDA available: {torch.cuda.is_available()}')
except ImportError:
    print('PyTorch: Not installed')

try:
    import transformers
    print(f'Transformers version: {transformers.__version__}')
except ImportError:
    print('Transformers: Not installed')

try:
    import robomimic
    print(f'Robomimic: Available')
except ImportError:
    print('Robomimic: Not installed')

try:
    import robosuite
    print(f'Robosuite: Available')
except ImportError:
    print('Robosuite: Not installed')
"
}

# Main execution
main() {
    install_system_deps
    install_core_deps
    install_robotics_deps
    install_optional_deps
    install_pytorch
    verify_installation
    
    echo ""
    echo "Installation complete!"
    echo "=================================================="
}

# Run main function
main 