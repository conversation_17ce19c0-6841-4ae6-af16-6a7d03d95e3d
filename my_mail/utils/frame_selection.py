"""
Batch selection utilities for maximizing diversity in contrastive learning.
Provides methods to select n_samples from batch with maximum differences using L1/L2 distances.
"""

import torch
import torch.nn.functional as F
import numpy as np
from typing import Tuple, List, Optional
import logging

log = logging.getLogger(__name__)


def compute_batch_distances(batch_data: torch.Tensor, distance_metric: str = 'l2') -> torch.Tensor:
    """
    Compute pairwise distances between samples in a batch.
    
    Args:
        batch_data: Tensor of shape (B, T, C, H, W) or (B, T, feature_dim) or (B, feature_dim)
        distance_metric: 'l1', 'l2', or 'cosine'
    
    Returns:
        Distance matrix of shape (B, B)
    """
    B = batch_data.shape[0]
    
    # Flatten all dimensions except batch dimension
    if batch_data.dim() > 2:
        batch_flat = batch_data.view(B, -1)  # (B, flattened_features)
    else:
        batch_flat = batch_data
    
    # Compute pairwise distances between samples
    distances = torch.zeros(B, B, device=batch_data.device)
    
    for i in range(B):
        for j in range(B):
            if i != j:
                if distance_metric == 'l1':
                    distances[i, j] = torch.norm(batch_flat[i] - batch_flat[j], p=1)
                elif distance_metric == 'l2':
                    distances[i, j] = torch.norm(batch_flat[i] - batch_flat[j], p=2)
                elif distance_metric == 'cosine':
                    # Cosine distance = 1 - cosine similarity
                    cos_sim = F.cosine_similarity(batch_flat[i], batch_flat[j], dim=0)
                    distances[i, j] = 1 - cos_sim
                else:
                    raise ValueError(f"Unsupported distance metric: {distance_metric}")
    
    return distances


def select_diverse_samples_greedy(batch_data: torch.Tensor, n_samples: int, 
                                 distance_metric: str = 'l2') -> Tuple[torch.Tensor, List[int]]:
    """
    Select n_samples from batch with maximum diversity using greedy algorithm.
    
    Args:
        batch_data: Input batch of shape (B, T, C, H, W) or (B, T, feature_dim) or (B, feature_dim)
        n_samples: Number of samples to select from batch
        distance_metric: Distance metric to use ('l1', 'l2', 'cosine')
    
    Returns:
        selected_samples: Selected samples of shape (n_samples, T, C, H, W) or (n_samples, T, feature_dim)
        selected_indices: List of selected sample indices from batch
    """
    B = batch_data.shape[0]
    
    if n_samples >= B:
        log.warning(f"n_samples ({n_samples}) >= B ({B}), returning all samples")
        return batch_data, list(range(B))
    
    # Compute pairwise distances between samples in batch
    distances = compute_batch_distances(batch_data, distance_metric)
    
    # Initialize with the first sample
    selected_idx = [0]
    remaining_idx = list(range(1, B))
    
    # Greedily select samples that maximize minimum distance to already selected samples
    for _ in range(n_samples - 1):
        best_idx = None
        best_min_dist = -1
        
        for candidate_idx in remaining_idx:
            # Compute minimum distance to already selected samples
            min_dist = min(distances[candidate_idx, sel_idx].item() for sel_idx in selected_idx)
            
            if min_dist > best_min_dist:
                best_min_dist = min_dist
                best_idx = candidate_idx
        
        if best_idx is not None:
            selected_idx.append(best_idx)
            remaining_idx.remove(best_idx)
    
    # Extract selected samples
    selected_samples = batch_data[selected_idx]
    
    return selected_samples, selected_idx


def select_diverse_samples_kmeans(batch_data: torch.Tensor, n_samples: int, 
                                 max_iters: int = 100) -> Tuple[torch.Tensor, List[int]]:
    """
    Select n_samples from batch using K-means clustering to maximize diversity.
    
    Args:
        batch_data: Input batch of shape (B, T, C, H, W) or (B, T, feature_dim) or (B, feature_dim)
        n_samples: Number of samples to select from batch
        max_iters: Maximum iterations for K-means
    
    Returns:
        selected_samples: Selected samples of shape (n_samples, T, C, H, W) or (n_samples, T, feature_dim)
        selected_indices: List of selected sample indices from batch
    """
    B = batch_data.shape[0]
    
    if n_samples >= B:
        log.warning(f"n_samples ({n_samples}) >= B ({B}), returning all samples")
        return batch_data, list(range(B))
    
    # Flatten all dimensions except batch dimension
    if batch_data.dim() > 2:
        batch_flat = batch_data.view(B, -1)  # (B, flattened_features)
    else:
        batch_flat = batch_data
    
    # Simple K-means clustering on batch samples
    data = batch_flat  # (B, feature_dim)
    
    # Initialize centroids randomly
    centroids = data[torch.randperm(B)[:n_samples]]
    
    for _ in range(max_iters):
        # Assign points to closest centroids
        distances_to_centroids = torch.cdist(data, centroids)  # (B, n_samples)
        assignments = torch.argmin(distances_to_centroids, dim=1)  # (B,)
        
        # Update centroids
        new_centroids = []
        for k in range(n_samples):
            cluster_points = data[assignments == k]
            if len(cluster_points) > 0:
                new_centroids.append(cluster_points.mean(dim=0))
            else:
                new_centroids.append(centroids[k])  # Keep old centroid if no points assigned
        
        new_centroids = torch.stack(new_centroids)
        
        # Check for convergence
        if torch.allclose(centroids, new_centroids, atol=1e-6):
            break
        
        centroids = new_centroids
    
    # Select samples closest to final centroids
    distances_to_final_centroids = torch.cdist(data, centroids)  # (B, n_samples)
    selected_idx = []
    
    for k in range(n_samples):
        # Find the sample closest to centroid k
        closest_sample_idx = torch.argmin(distances_to_final_centroids[:, k]).item()
        selected_idx.append(closest_sample_idx)
    
    # Remove duplicates
    selected_idx = list(set(selected_idx))
    
    # If we have fewer unique samples than needed, add more greedily
    if len(selected_idx) < n_samples:
        remaining_idx = [i for i in range(B) if i not in selected_idx]
        while len(selected_idx) < n_samples and remaining_idx:
            # Add the sample that's furthest from already selected samples
            best_idx = None
            best_min_dist = -1
            
            for candidate_idx in remaining_idx:
                min_dist = min(torch.norm(data[candidate_idx] - data[sel_idx], p=2).item() 
                             for sel_idx in selected_idx)
                if min_dist > best_min_dist:
                    best_min_dist = min_dist
                    best_idx = candidate_idx
            
            if best_idx is not None:
                selected_idx.append(best_idx)
                remaining_idx.remove(best_idx)
    
    selected_idx = selected_idx[:n_samples]  # Ensure we don't exceed n_samples
    
    # Extract selected samples
    selected_samples = batch_data[selected_idx]
    
    return selected_samples, selected_idx


def select_diverse_samples_uniform(batch_data: torch.Tensor, n_samples: int) -> Tuple[torch.Tensor, List[int]]:
    """
    Select n_samples from batch uniformly (baseline method).
    
    Args:
        batch_data: Input batch of shape (B, T, C, H, W) or (B, T, feature_dim) or (B, feature_dim)
        n_samples: Number of samples to select from batch
    
    Returns:
        selected_samples: Selected samples of shape (n_samples, T, C, H, W) or (n_samples, T, feature_dim)
        selected_indices: List of selected sample indices from batch
    """
    B = batch_data.shape[0]
    
    if n_samples >= B:
        log.warning(f"n_samples ({n_samples}) >= B ({B}), returning all samples")
        return batch_data, list(range(B))
    
    # Select uniformly spaced indices
    indices = np.linspace(0, B - 1, n_samples, dtype=int).tolist()
    
    # Extract selected samples
    selected_samples = batch_data[indices]
    
    return selected_samples, indices


def select_diverse_samples(batch_data: torch.Tensor, n_samples: int, 
                          method: str = 'greedy', distance_metric: str = 'l2',
                          **kwargs) -> Tuple[torch.Tensor, List[int]]:
    """
    Main interface for selecting diverse samples from batch.
    
    Args:
        batch_data: Input batch of shape (B, T, C, H, W) or (B, T, feature_dim) or (B, feature_dim)
        n_samples: Number of samples to select from batch
        method: Selection method ('greedy', 'kmeans', 'uniform')
        distance_metric: Distance metric for greedy method ('l1', 'l2', 'cosine')
        **kwargs: Additional arguments for specific methods
    
    Returns:
        selected_samples: Selected samples of shape (n_samples, T, C, H, W) or (n_samples, T, feature_dim)
        selected_indices: List of selected sample indices from batch
    """
    if method == 'greedy':
        return select_diverse_samples_greedy(batch_data, n_samples, distance_metric)
    elif method == 'kmeans':
        return select_diverse_samples_kmeans(batch_data, n_samples, kwargs.get('max_iters', 100))
    elif method == 'uniform':
        return select_diverse_samples_uniform(batch_data, n_samples)
    else:
        raise ValueError(f"Unsupported selection method: {method}")


def apply_batch_selection(batch: dict, n_samples: int, 
                         method: str = 'greedy', distance_metric: str = 'l2',
                         reference_key: str = 'bp_imgs') -> dict:
    """
    Apply batch selection to reduce batch size while maintaining diversity.
    
    Args:
        batch: Dictionary containing batch data
        n_samples: Number of samples to select from batch
        method: Selection method ('greedy', 'kmeans', 'uniform')
        distance_metric: Distance metric for greedy method
        reference_key: Key to use as reference for computing diversity (should be most informative)
    
    Returns:
        Modified batch with selected samples
    """
    if reference_key not in batch:
        log.warning(f"Reference key '{reference_key}' not found in batch, using first available key")
        reference_key = list(batch.keys())[0]
    
    # Use reference data to compute diversity
    reference_data = batch[reference_key]
    
    # Select diverse samples based on reference data
    _, selected_indices = select_diverse_samples(
        reference_data, n_samples, method, distance_metric
    )
    
    # Apply the same indices to all batch data
    modified_batch = {}
    for key, value in batch.items():
        if isinstance(value, torch.Tensor) and value.shape[0] == reference_data.shape[0]:
            # Apply selection to tensors with matching batch dimension
            modified_batch[key] = value[selected_indices]
        else:
            # Keep non-tensor data or data with different batch dimension unchanged
            modified_batch[key] = value
    
    return modified_batch


# Example usage and testing functions
def test_batch_selection():
    """Test the batch selection functions"""
    # Create dummy data
    B, T, C, H, W = 32, 10, 3, 64, 64
    batch_data = torch.randn(B, T, C, H, W)
    n_samples = 16
    
    print("Testing batch selection methods...")
    print(f"Original batch size: {B}")
    print(f"Target samples: {n_samples}")
    
    # Test greedy selection
    selected_samples, indices = select_diverse_samples(batch_data, n_samples, method='greedy', distance_metric='l2')
    print(f"Greedy L2 - Selected samples shape: {selected_samples.shape}")
    print(f"Greedy L2 - Selected indices: {indices}")
    
    # Test K-means selection
    selected_samples, indices = select_diverse_samples(batch_data, n_samples, method='kmeans')
    print(f"K-means - Selected samples shape: {selected_samples.shape}")
    print(f"K-means - Selected indices: {indices}")
    
    # Test uniform selection
    selected_samples, indices = select_diverse_samples(batch_data, n_samples, method='uniform')
    print(f"Uniform - Selected samples shape: {selected_samples.shape}")
    print(f"Uniform - Selected indices: {indices}")
    
    # Test batch application
    batch = {
        'bp_imgs': torch.randn(B, T, C, H, W),
        'inhand_imgs': torch.randn(B, T, C, H, W),
        'action': torch.randn(B, T, 7),
        'goal_imgs': torch.randn(B, 1, 512)
    }
    
    print(f"\nOriginal batch shapes:")
    for key, value in batch.items():
        if isinstance(value, torch.Tensor):
            print(f"{key}: {value.shape}")
    
    modified_batch = apply_batch_selection(
        batch, n_samples, method='greedy', distance_metric='l2',
        reference_key='bp_imgs'
    )
    
    print(f"\nAfter batch selection:")
    for key, value in modified_batch.items():
        if isinstance(value, torch.Tensor):
            print(f"{key}: {value.shape}")


if __name__ == "__main__":
    test_batch_selection()