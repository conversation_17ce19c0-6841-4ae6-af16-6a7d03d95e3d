import os
import sys
import glob
import json
import argparse

import h5py
import numpy as np
import torch
from tqdm import tqdm

# Make sure we can import from the project root so that
# `from dataset.multi_task_dataset_goal import MultiTaskDataset` works
THIS_DIR = os.path.dirname(os.path.abspath(__file__))
PROJECT_ROOT = os.path.dirname(THIS_DIR)
if PROJECT_ROOT not in sys.path:
    sys.path.append(PROJECT_ROOT)

from dataset.multi_task_dataset_goal import MultiTaskDataset  # noqa: E402


def find_max_episode_len(hdf5_dir: str) -> int:
    """Scan all .hdf5 files under hdf5_dir to get the maximum episode length.

    This value is used as max_len_data when building MultiTaskDataset,
    to ensure no sequence is truncated.
    """
    files = sorted(glob.glob(os.path.join(hdf5_dir, "*.hdf5")))
    if not files:
        raise FileNotFoundError(f"No .hdf5 found under {hdf5_dir}")
    max_len = 0
    for fp in files:
        with h5py.File(fp, "r") as f:
            if "data" not in f:
                continue
            for demo_name in f["data"].keys():
                demo = f["data"][demo_name]
                if "num_samples" in demo.attrs:
                    max_len = max(max_len, int(demo.attrs["num_samples"]))
                elif "actions" in demo:
                    max_len = max(max_len, demo["actions"].shape[0])
    if max_len <= 0:
        raise RuntimeError("Failed to determine max episode length from files.")
    return max_len


def build_dataset(hdf5_dir: str, window_size: int, action_dim: int, num_data_per_file: int) -> MultiTaskDataset:
    max_len_data = find_max_episode_len(hdf5_dir)
    # Minimal obs settings (images will be loaded by dataset but we do not use them here)
    obs_keys = "rgb"
    obs_modalities = {"obs": {"rgb": ["agentview_rgb", "eye_in_hand_rgb"]}}
    ds = MultiTaskDataset(
        data_directory=hdf5_dir,
        task_suite="libero_goal",
        obs_keys=obs_keys,
        obs_modalities=obs_modalities,
        device="cpu",
        obs_dim=32,
        action_dim=action_dim,
        state_dim=45,
        max_len_data=max_len_data,
        window_size=window_size,
        num_data=num_data_per_file,
        data_aug=False,
    )
    return ds


def stack_action_chunks_from_slices(ds: MultiTaskDataset, dist_horizon: int) -> torch.Tensor:
    """Collect fixed-length action chunks from slices.

    Returns a tensor of shape [N, T, D], where T == dist_horizon.
    """
    chunks = []
    for (i, start, end) in ds.slices:
        if end - start != dist_horizon:
            # Safety check; by construction slices follow exact window_size
            continue
        a = ds.actions[i, start:end]  # [T, D]
        chunks.append(a.unsqueeze(0))
    if not chunks:
        raise RuntimeError("No valid slices collected. Check window_size/dist_horizon and dataset.")
    arr = torch.cat(chunks, dim=0)  # [N, T, D]
    return arr


def normalize_and_reformat(arr_TD: torch.Tensor, scale_rot6d: bool) -> torch.Tensor:
    """Normalize actions globally and permute to [N, D, T]. Optionally scale rot6d dims.

    - Input:  arr_TD [N, T, D]
    - Output: arr_D  [N, D, T]
    """
    flat = arr_TD.reshape(-1, arr_TD.shape[-1]).float()
    mean, std = flat.mean(0), flat.std(0).clamp_min(1e-6)
    arr = ((arr_TD - mean) / std).permute(0, 2, 1).contiguous()  # [N, D, T]

    # If actions are ABS with layout [pos3, rot6d, joint7, grip1], balance 6D scale
    if scale_rot6d and arr.shape[1] >= 10:
        arr[:, 3:9, :] /= 2.0
    return arr


def compute_dtw_matrix(arr: np.ndarray, method: str = "dtw") -> torch.Tensor:
    """Compute pairwise DTW distances for arr of shape [N, D, T]. Small/medium scale only.

    Returns a dense torch.float32 tensor [N, N].
    """
    from aeon.distances import distance

    N = arr.shape[0]
    dists = np.zeros((N, N), dtype=np.float32)
    for i in tqdm(range(N), desc="Row", mininterval=0.2):
        for j in range(i + 1, N):
            d = distance(arr[i], arr[j], method=method)
            dists[i, j] = dists[j, i] = d
    return torch.from_numpy(dists)


def main():
    parser = argparse.ArgumentParser(description="Compute DTW matrix from LIBERO ABS slices.")
    parser.add_argument("--input_dir", type=str, required=True, help="Directory with converted ABS HDF5 files")
    parser.add_argument("--output", type=str, required=True, help="Path to save DTW matrix (.pth)")
    parser.add_argument("--window_size", type=int, default=16, help="Slice / DTW horizon")
    parser.add_argument("--action_dim", type=int, default=17, help="Action dimension (ABS=17)")
    parser.add_argument("--num_data_per_file", type=int, default=10000, help="Max demos to load per file")
    parser.add_argument("--method", type=str, default="dtw", help="Distance method for aeon (default: dtw)")
    parser.add_argument("--scale_rot6d", action="store_true", help="Divide rot6d dims by 2 for balance")
    args = parser.parse_args()

    print(json.dumps(vars(args), indent=2))

    print("Building dataset...")
    ds = build_dataset(
        hdf5_dir=args.input_dir,
        window_size=args.window_size,
        action_dim=args.action_dim,
        num_data_per_file=args.num_data_per_file,
    )
    print(f"Loaded demos: {ds.num_data}, total slices: {len(ds.slices)}")

    print("Stacking action chunks from slices...")
    arr_TD = stack_action_chunks_from_slices(ds, args.window_size)  # [N, T, D]
    print(f"Chunks tensor: {tuple(arr_TD.shape)}  (N, T, D)")

    print("Normalizing and reformatting to [N, D, T]...")
    arr_NDT = normalize_and_reformat(arr_TD, scale_rot6d=args.scale_rot6d)  # [N, D, T]
    print(f"Normalized tensor: {tuple(arr_NDT.shape)}  (N, D, T)")

    print("Computing DTW matrix (small/medium scale)...")
    dists = compute_dtw_matrix(arr_NDT.numpy(), method=args.method)  # [N, N] torch.float32

    out_dir = os.path.dirname(args.output)
    if out_dir:
        os.makedirs(out_dir, exist_ok=True)
    torch.save(dists, args.output)
    print(f"Saved DTW matrix to: {args.output}")


if __name__ == "__main__":
    main()


