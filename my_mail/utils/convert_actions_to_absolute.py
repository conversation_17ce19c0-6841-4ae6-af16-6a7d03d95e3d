import argparse
import copy
import os
import sys
import warnings

import h5py
import numpy as np
import torch

# External deps expected in your environment (as in CLASS):
#   - robomimic
#   - robosuite
try:
    import robomimic.utils.file_utils as FileUtils
    import robomimic.utils.env_utils as EnvUtils
    import robomimic.utils.obs_utils as ObsUtils
except Exception as e:
    print("[Error] robomimic is required: pip install robomimic")
    raise e

try:
    from robosuite.controllers import load_controller_config
except Exception as e:
    print("[Error] robosuite is required: pip install robosuite")
    raise e

# Ensure LIBERO envs are registered into robosuite before any env creation
try:
    import libero.libero.envs as _libero_autoreg  # noqa: F401
except Exception as e:
    print("[Warn] Failed to import LIBERO envs for robosuite registration:", e)

# Optional: SciPy for Euler->Rotation conversion in fallback path
try:
    from scipy.spatial.transform import Rotation as R
except Exception:
    R = None


def matrix_to_rotation_6d(rot_mats: torch.Tensor) -> torch.Tensor:
    """Convert rotation matrices (..., 3, 3) to 6D representation (..., 6).
    Uses first two columns of rotation matrix.
    """
    assert rot_mats.shape[-2:] == (3, 3)
    first_col = rot_mats[..., :, 0]
    second_col = rot_mats[..., :, 1]
    return torch.cat([first_col, second_col], dim=-1)


def _normalize_controller_type(controller_cfg: dict, fallback: str = "OSC_POSE") -> None:
    raw = str(controller_cfg.get("type", fallback)).upper().replace("-", "_").strip()
    mapping = {
        "BASIC": "OSC_POSE",
        "OSC": "OSC_POSE",
        "OSC_CARTESIAN": "OSC_POSE",
        "OSC_POSITION": "OSC_POSE",
        "POSE": "OSC_POSE",
    }
    controller_cfg["type"] = mapping.get(raw, raw if raw else fallback)


def _fill_controller_defaults(controller_cfg: dict) -> None:
    ctrl_type = controller_cfg.get("type", "OSC_POSE")
    try:
        default_cfg = load_controller_config(default_controller=ctrl_type)
        for k, v in default_cfg.items():
            controller_cfg.setdefault(k, v)
    except Exception:
        controller_cfg.setdefault("interpolation", "linear")
        controller_cfg.setdefault("ramp_ratio", 1.0)


def _safe_set_state(env, state_flattened: np.ndarray) -> None:
    """Best-effort: set MuJoCo sim state from flattened state for either robomimic env or raw robosuite env."""
    # robomimic env wrapper usually has .env.sim
    if hasattr(env, "env") and hasattr(env.env, "sim"):
        env.env.sim.set_state_from_flattened(state_flattened)
        env.env.sim.forward()
        return
    # raw env with .sim
    if hasattr(env, "sim"):
        env.sim.set_state_from_flattened(state_flattened)
        env.sim.forward()
        return
    # as a last resort, try reset_to API if present
    if hasattr(env, "reset_to"):
        env.reset_to({"states": state_flattened})
        return
    raise RuntimeError("Cannot set simulator state on the provided environment wrapper.")


def _get_robots(env):
    """Return list of robot objects, handling robomimic wrappers."""
    if hasattr(env, "env") and hasattr(env.env, "robots"):
        return env.env.robots
    if hasattr(env, "robots"):
        return env.robots
    raise RuntimeError("Cannot access robots from environment.")


def _get_sim(env):
    if hasattr(env, "env") and hasattr(env.env, "sim"):
        return env.env.sim
    if hasattr(env, "sim"):
        return env.sim
    raise RuntimeError("Cannot access sim from environment.")


def build_envs_from_hdf5(hdf5_path: str):
    """Create two environments from dataset metadata:
    - base_env: controller per dataset metadata (typically delta pose)
    - joint_env: JOINT_POSITION controller for PD backsolve
    """
    # Minimal ObsUtils init to avoid robomimic errors
    dummy_spec = dict(
        obs=dict(
            low_dim=["robot0_eef_pos", "object"],
            rgb=["agentview_image", "robot0_eye_in_hand_image"],
        ),
    )
    ObsUtils.initialize_obs_utils_with_obs_specs(obs_modality_specs=dummy_spec)

    env_meta = FileUtils.get_env_metadata_from_dataset(hdf5_path)
    # Clean / ensure controller configs exist
    if "env_kwargs" in env_meta and isinstance(env_meta["env_kwargs"], dict):
        env_meta["env_kwargs"].pop("lite_physics", None)
    env_meta.setdefault("env_kwargs", {})
    env_meta["env_kwargs"].setdefault("controller_configs", {})
    _normalize_controller_type(env_meta["env_kwargs"]["controller_configs"], fallback="OSC_POSE")
    _fill_controller_defaults(env_meta["env_kwargs"]["controller_configs"])

    # Try to fix invalid or remote BDDL paths embedded in dataset metadata
    try:
        from libero.libero import get_libero_path as _get_libero_path
        bddl_in_meta = env_meta["env_kwargs"].get("bddl_file_name", "")
        bddl_is_valid = isinstance(bddl_in_meta, str) and os.path.exists(bddl_in_meta)
        if not bddl_is_valid:
            suite_name = os.path.basename(os.path.dirname(hdf5_path))  # e.g., libero_goal
            base_name = os.path.splitext(os.path.basename(hdf5_path))[0]
            task_name = base_name[:-5] if base_name.endswith("_demo") else base_name
            guessed_bddl = os.path.join(_get_libero_path("bddl_files"), suite_name, f"{task_name}.bddl")
            if os.path.exists(guessed_bddl):
                env_meta["env_kwargs"]["bddl_file_name"] = guessed_bddl
    except Exception:
        # If LIBERO is not importable, we'll likely fallback anyway
        pass

    # Camera defaults (not actually rendering here, but kept for parity)
    env_meta["env_kwargs"]["camera_names"] = ["agentview", "robot0_eye_in_hand"]
    env_meta["env_kwargs"]["camera_heights"] = 256
    env_meta["env_kwargs"]["camera_widths"] = 256

    # Base env: as-is from dataset
    base_env = EnvUtils.create_env_from_metadata(
        env_meta=env_meta,
        render=False,
        render_offscreen=False,
        use_image_obs=False,
    )

    # Joint env: JOINT_POSITION controller for torque/PD mapping
    joint_env_meta = copy.deepcopy(env_meta)
    joint_env_meta["env_kwargs"].setdefault("controller_configs", {})
    joint_env_meta["env_kwargs"]["controller_configs"]["type"] = "JOINT_POSITION"
    _normalize_controller_type(joint_env_meta["env_kwargs"]["controller_configs"], fallback="JOINT_POSITION")
    _fill_controller_defaults(joint_env_meta["env_kwargs"]["controller_configs"])
    joint_env_meta["env_kwargs"]["controller_configs"]["input_max"] = np.pi
    joint_env_meta["env_kwargs"]["controller_configs"]["input_min"] = -np.pi
    joint_env_meta["env_kwargs"]["controller_configs"]["output_max"] = np.pi
    joint_env_meta["env_kwargs"]["controller_configs"]["output_min"] = -np.pi

    joint_env = EnvUtils.create_env_from_metadata(
        env_meta=joint_env_meta,
        render=False,
        render_offscreen=False,
        use_image_obs=False,
    )

    # Warm reset
    if hasattr(base_env, "reset"):
        base_env.reset()
    if hasattr(joint_env, "reset"):
        joint_env.reset()

    return base_env, joint_env


def convert_actions_with_states(states: np.ndarray, actions: np.ndarray, base_env, joint_env) -> np.ndarray:
    """Convert delta actions to absolute targets using controller goals and PD backsolve.

    Returns: absolute actions as shape (T, 17 * num_robots)
    Layout per robot: [pos3, rot6d, joint7, gripper1]
    """
    assert actions.ndim == 2, "Expected actions shape (T, action_dim)"
    # Support multi-robot: reshape (T, 7*R) -> (T, R, 7)
    if actions.shape[-1] % 7 != 0:
        raise ValueError(f"Action dim {actions.shape[-1]} is not divisible by 7; expected per-robot 7 dims (dx,dy,dz,rot3,grip)")
    num_robots = actions.shape[-1] // 7
    stacked_actions = actions.reshape(actions.shape[0], num_robots, 7)

    # Buffers
    T = states.shape[0]
    action_goal_pos = np.zeros((T, num_robots, 3), dtype=np.float32)
    action_goal_ori = np.zeros((T, num_robots, 3, 3), dtype=np.float32)
    action_goal_qpos = np.zeros((T, num_robots, 7), dtype=np.float32)
    action_gripper = stacked_actions[..., [-1]].astype(np.float32)

    # Access robots and sim references once
    for t in range(T):
        _safe_set_state(base_env, states[t])
        _safe_set_state(joint_env, states[t])

        base_robots = _get_robots(base_env)
        joint_robots = _get_robots(joint_env)

        for r_idx, robot in enumerate(base_robots):
            # Drive controller with original delta action
            robot.control(stacked_actions[t, r_idx], policy_step=True)
            joint_robot = joint_robots[r_idx]

            controller = robot.controller
            joint_controller = joint_robot.controller

            # Read desired EE pos and ori (3x3)
            action_goal_pos[t, r_idx] = np.array(controller.goal_pos, dtype=np.float32)
            action_goal_ori[t, r_idx] = np.array(controller.goal_ori, dtype=np.float32)

            # PD backsolve for desired joint positions
            torques = controller.torques
            # mass_matrix is (7x7); torque_compensation same shape as torques
            desired_torque = np.linalg.solve(
                joint_controller.mass_matrix,
                torques - joint_controller.torque_compensation,
            )
            sim = _get_sim(controller)
            joint_pos = np.array(sim.data.qpos[controller.qpos_index])
            joint_vel = np.array(sim.data.qvel[controller.qvel_index])
            position_error = (desired_torque + np.multiply(joint_vel, joint_controller.kd)) / joint_controller.kp
            desired_qpos = position_error + joint_pos
            action_goal_qpos[t, r_idx] = desired_qpos.astype(np.float32)

    # Pack tensors
    action_goal_pos_t = torch.from_numpy(action_goal_pos)
    action_goal_ori_t = torch.from_numpy(action_goal_ori)
    action_goal_qpos_t = torch.from_numpy(action_goal_qpos)
    action_goal_ori_6d_t = matrix_to_rotation_6d(action_goal_ori_t)
    action_gripper_t = torch.from_numpy(action_gripper)

    stacked_abs_actions = torch.cat(
        [action_goal_pos_t, action_goal_ori_6d_t, action_goal_qpos_t, action_gripper_t], dim=-1
    )  # (T, R, 17)
    abs_actions = stacked_abs_actions.reshape(T, 17 * num_robots).cpu().numpy().astype(np.float32)
    return abs_actions


def _pick_obs_key(obs_group: h5py.Group, candidates):
    for k in candidates:
        if k in obs_group:
            return k
    return None


def _euler_to_rotmat_batch(eulers: np.ndarray, order: str = "xyz") -> np.ndarray:
    """Convert batch of Euler angles (T,3) to rotation matrices (T,3,3)."""
    if R is None:
        # Minimal fallback for 'xyz' order
        cx, cy, cz = np.cos(eulers[:, 0]), np.cos(eulers[:, 1]), np.cos(eulers[:, 2])
        sx, sy, sz = np.sin(eulers[:, 0]), np.sin(eulers[:, 1]), np.sin(eulers[:, 2])
        # R = Rz * Ry * Rx if order is 'xyz' for intrinsic rotations (approximation)
        rot = np.empty((eulers.shape[0], 3, 3), dtype=np.float32)
        for i in range(eulers.shape[0]):
            Rx = np.array([[1, 0, 0], [0, cx[i], -sx[i]], [0, sx[i], cx[i]]], dtype=np.float32)
            Ry = np.array([[cy[i], 0, sy[i]], [0, 1, 0], [-sy[i], 0, cy[i]]], dtype=np.float32)
            Rz = np.array([[cz[i], -sz[i], 0], [sz[i], cz[i], 0], [0, 0, 1]], dtype=np.float32)
            rot[i] = Rz @ Ry @ Rx
        return rot
    else:
        return R.from_euler(order, eulers, degrees=False).as_matrix().astype(np.float32)


def convert_actions_from_obs(demo_in: h5py.Group) -> np.ndarray:
    """Fallback path: construct absolute actions from observed absolute kinematics in HDF5.

    Expects typical LIBERO keys under demo['obs']: ee_pos/ee_ori/joint_states/gripper_states.
    Returns (T, 17) = [pos3, rot6d, joint7, grip1].
    """
    T = int(demo_in["actions"].shape[0])
    obs = demo_in.get("obs")
    if obs is None:
        raise RuntimeError("Missing 'obs' group for fallback absolute action conversion.")

    # Position
    pos_key = _pick_obs_key(obs, [
        "ee_pos", "robot0_eef_pos", "eef_pos", "ee_states"
    ])
    if pos_key is None:
        raise RuntimeError("Cannot find EE position keys in obs (ee_pos / robot0_eef_pos / ee_states).")
    if pos_key == "ee_states":
        pos = np.asarray(obs[pos_key][:, :3], dtype=np.float32)
    else:
        pos = np.asarray(obs[pos_key][:], dtype=np.float32)

    # Orientation (Euler -> rotmat -> rot6d)
    eul_key = _pick_obs_key(obs, [
        "ee_ori",  # (T,3) radians
        "robot0_eef_euler",  # alternative naming
        "ee_states",  # last 3
    ])
    if eul_key is None:
        raise RuntimeError("Cannot find EE orientation keys in obs (ee_ori / ee_states).")
    if eul_key == "ee_states":
        eulers = np.asarray(obs[eul_key][:, 3:6], dtype=np.float32)
    else:
        eulers = np.asarray(obs[eul_key][:], dtype=np.float32)
    rotmats = _euler_to_rotmat_batch(eulers, order="xyz")
    rotmats_t = torch.from_numpy(rotmats)
    rot6d = matrix_to_rotation_6d(rotmats_t).numpy().astype(np.float32)

    # Joint positions
    joint_key = _pick_obs_key(obs, [
        "joint_states", "robot0_joint_pos"
    ])
    if joint_key is None:
        raise RuntimeError("Cannot find joint position keys in obs (joint_states / robot0_joint_pos).")
    joint = np.asarray(obs[joint_key][:], dtype=np.float32)
    if joint.shape[-1] != 7:
        raise RuntimeError(f"Expected 7-DoF joints, got shape {joint.shape}")

    # Gripper: prefer original action last dim (keeps semantics), else from obs
    actions = np.asarray(demo_in["actions"][:], dtype=np.float32)
    if actions.shape[-1] >= 1:
        grip = actions[:, -1:]
    else:
        grip_key = _pick_obs_key(obs, ["gripper_states", "robot0_gripper_qpos"])
        if grip_key is None:
            raise RuntimeError("Cannot find gripper info in actions or obs.")
        g = np.asarray(obs[grip_key][:], dtype=np.float32)
        grip = g[:, :1]

    assert pos.shape[0] == T and rot6d.shape[0] == T and joint.shape[0] == T and grip.shape[0] == T, "Mismatched lengths among pos/rot/joint/grip"

    abs_actions = np.concatenate([pos, rot6d, joint, grip], axis=-1).astype(np.float32)
    return abs_actions


def copy_all_root_items(fin: h5py.File, fout: h5py.File) -> None:
    """Copy all top-level items and attrs from fin to fout."""
    for key in fin.keys():
        fin.copy(fin[key], fout, name=key)
    for attr_key, attr_val in fin.attrs.items():
        try:
            fout.attrs[attr_key] = attr_val
        except Exception:
            pass


def process_file(input_hdf5: str, output_hdf5: str, max_episodes: int = -1) -> None:
    warnings.filterwarnings("ignore")
    base_env, joint_env, use_env = None, None, False
    try:
        base_env, joint_env = build_envs_from_hdf5(input_hdf5)
        use_env = True
    except Exception as e:
        print(f"[Warn] Failed to build envs from metadata: {e}\n        Falling back to obs-based absolute action conversion.")

    with h5py.File(input_hdf5, "r") as fin, h5py.File(output_hdf5, "w") as fout:
        # Copy everything first, then replace actions per-demo
        copy_all_root_items(fin, fout)

        if "data" not in fin:
            raise RuntimeError("Input HDF5 missing 'data' group")

        demo_keys = list(fin["data"].keys())
        # Sort demo_i by numeric order
        try:
            demo_keys = sorted(demo_keys, key=lambda k: int(k.split("_")[-1]))
        except Exception:
            pass
        if max_episodes and max_episodes > 0:
            demo_keys = demo_keys[:max_episodes]

        for demo_name in demo_keys:
            demo_in = fin["data"][demo_name]
            demo_out = fout["data"][demo_name]

            if "states" not in demo_in or "actions" not in demo_in:
                print(f"[Skip] {demo_name}: missing 'states' or 'actions'")
                continue

            states = demo_in["states"][:]
            actions = demo_in["actions"][:]

            print(f"Converting {demo_name}: T={len(states)}, action_dim={actions.shape[-1]}")
            if use_env:
                abs_actions = convert_actions_with_states(states, actions, base_env, joint_env)
            else:
                abs_actions = convert_actions_from_obs(demo_in)

            # Move original actions -> actions_rel, then create absolute as actions
            if "actions_rel" in demo_out:
                del demo_out["actions_rel"]
            # h5py supports moving within the same file
            if "actions" in demo_out:
                demo_out.move("actions", "actions_rel")

            # Write new 'actions' (absolute)
            if "actions" in demo_out:
                del demo_out["actions"]
            demo_out.create_dataset("actions", data=abs_actions, dtype=np.float32)

            # Keep num_samples attribute untouched
        print(f"Done. Wrote converted file to: {output_hdf5}")


def parse_args(argv=None):
    parser = argparse.ArgumentParser(description="Convert relative actions in robomimic-style HDF5 to absolute actions using controller goals.")
    parser.add_argument("--input", required=True, help="Path to input HDF5 dataset")
    parser.add_argument("--output", required=True, help="Path to output HDF5 dataset")
    parser.add_argument("--max_episodes", type=int, default=-1, help="Optional limit on number of demos to process")
    return parser.parse_args(argv)


if __name__ == "__main__":
    args = parse_args()
    process_file(args.input, args.output, max_episodes=args.max_episodes)


