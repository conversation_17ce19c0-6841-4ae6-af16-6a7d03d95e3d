"""
Video recording utilities for LIBERO evaluation.
Based on clip-rt implementation.
"""
import os
import imageio
import numpy as np
from datetime import datetime


def get_date_time():
    """Get current date and time strings."""
    now = datetime.now()
    date = now.strftime("%Y-%m-%d")
    date_time = now.strftime("%Y-%m-%d_%H-%M-%S")
    return date, date_time


def save_rollout_video(task_suite_name, epoch, rollout_images, episode_idx, success, task_description, log_file=None):
    """
    Saves an MP4 replay of an episode.
    
    Args:
        task_suite_name: Name of the task suite (e.g., 'libero_goal')
        epoch: Training epoch number
        rollout_images: List of images (numpy arrays) from the episode
        episode_idx: Episode index
        success: Whether the episode was successful (True/False)
        task_description: Description of the task
        log_file: Optional log file to write to
    
    Returns:
        str: Path to the saved video file
    """
    date, date_time = get_date_time()
    
    # Create rollout directory
    rollout_dir = f"./rollouts/{task_suite_name}/epoch_{epoch}/{date}"
    os.makedirs(rollout_dir, exist_ok=True)
    
    # Process task description for filename
    processed_task_description = (
        task_description.lower()
        .replace(" ", "_")
        .replace("\n", "_")
        .replace(".", "_")
        .replace(",", "_")
        .replace("(", "")
        .replace(")", "")
        [:50]  # Limit length
    )
    
    # Create video filename
    mp4_path = f"{rollout_dir}/{date_time}--episode={episode_idx}--success={success}--task={processed_task_description}.mp4"
    
    # Save video
    if len(rollout_images) > 0:
        video_writer = imageio.get_writer(mp4_path, fps=30)
        for img in rollout_images:
            # Ensure image is in correct format
            if isinstance(img, np.ndarray):
                if img.dtype != np.uint8:
                    img = (img * 255).astype(np.uint8) if img.max() <= 1.0 else img.astype(np.uint8)
                # 上下翻转图像
                img = np.flipud(img)
                video_writer.append_data(img)
        video_writer.close()
        
        print(f"📹 Saved rollout MP4 at path {mp4_path}")
        if log_file is not None:
            log_file.write(f"Saved rollout MP4 at path {mp4_path}\n")
    else:
        print(f"⚠️  No images to save for episode {episode_idx}")
        if log_file is not None:
            log_file.write(f"No images to save for episode {episode_idx}\n")
    
    return mp4_path


def process_image_for_video(img):
    """
    Process image for video recording.

    Args:
        img: Image array from environment observation

    Returns:
        numpy.ndarray: Processed image ready for video recording
    """
    if isinstance(img, np.ndarray):
        # Handle different image formats
        if img.dtype == np.float32 or img.dtype == np.float64:
            # Convert from [0,1] to [0,255]
            if img.max() <= 1.0:
                img = (img * 255).astype(np.uint8)
            else:
                img = img.astype(np.uint8)
        elif img.dtype != np.uint8:
            img = img.astype(np.uint8)

        # Ensure correct shape (H, W, C)
        if len(img.shape) == 3 and img.shape[2] == 3:
            # 上下翻转图像
            img = np.flipud(img)
            return img
        elif len(img.shape) == 3 and img.shape[0] == 3:
            # Convert from (C, H, W) to (H, W, C)
            img = np.transpose(img, (1, 2, 0))
            # 上下翻转图像
            img = np.flipud(img)
            return img
        else:
            print(f"⚠️  Unexpected image shape: {img.shape}")
            return img

    return img


class VideoRecorder:
    """
    Video recorder class for collecting images during episodes.
    """
    
    def __init__(self, save_video=True):
        self.save_video = save_video
        self.reset()
    
    def reset(self):
        """Reset the recorder for a new episode."""
        self.images = []
    
    def record_frame(self, obs, camera_name="agentview_image"):
        """
        Record a frame from the environment observation.
        
        Args:
            obs: Environment observation dictionary
            camera_name: Name of the camera to record from
        """
        if self.save_video and camera_name in obs:
            img = obs[camera_name]
            # Process image for video
            processed_img = process_image_for_video(img)
            self.images.append(processed_img)
    
    def save_recorded_video(self, task_suite_name, epoch, episode_idx, success, task_description, log_file=None):
        """
        Save the recorded video.

        Args:
            task_suite_name: Name of the task suite
            epoch: Training epoch number
            episode_idx: Episode index
            success: Whether the episode was successful
            task_description: Description of the task
            log_file: Optional log file to write to

        Returns:
            str: Path to the saved video file
        """
        if self.save_video and len(self.images) > 0:
            return save_rollout_video(
                task_suite_name=task_suite_name,
                epoch=epoch,
                rollout_images=self.images,
                episode_idx=episode_idx,
                success=success,
                task_description=task_description,
                log_file=log_file
            )
        return None
    
    def get_num_frames(self):
        """Get the number of recorded frames."""
        return len(self.images)
