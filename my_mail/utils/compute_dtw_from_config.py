import os
import sys
import json
import argparse

import torch
from hydra import initialize, compose
from omegaconf import OmegaConf

# Local imports
THIS_DIR = os.path.dirname(os.path.abspath(__file__))
PROJECT_ROOT = os.path.dirname(THIS_DIR)
if PROJECT_ROOT not in sys.path:
    sys.path.append(PROJECT_ROOT)

from utils.compute_dtw_from_slices import (  # noqa: E402
    build_dataset,
    stack_action_chunks_from_slices,
    normalize_and_reformat,
    compute_dtw_matrix,
)


def derive_abs_input_dir(dataset_path: str) -> str:
    """Derive ABS dataset dir from original dataset_path by replacing '/libero/' with '/libero_abs/'."""
    if "/libero/" in dataset_path:
        return dataset_path.replace("/libero/", "/libero_abs/")
    # Fallback: place alongside original under 'libero_abs'
    parent = os.path.dirname(os.path.dirname(dataset_path))
    task = os.path.basename(dataset_path)
    return os.path.join(parent + "_abs", task)


def main():
    # Register custom OmegaConf resolver for arithmetic operations
    OmegaConf.register_new_resolver("add", lambda *numbers: sum(int(x) for x in numbers))
    
    parser = argparse.ArgumentParser(description="Compute DTW from slices using Hydra config.")
    parser.add_argument("--config_name", type=str, default="benchmark_libero_goal_diffusion_transformer.yaml")
    parser.add_argument("--input_dir", type=str, default=None, help="ABS HDF5 dir (override)")
    parser.add_argument("--output", type=str, default=None, help="Output .pth path (override)")
    parser.add_argument("--use_train_action_seq", action="store_true", help="Use train_action_seq as window_size (else cfg.window_size)")
    parser.add_argument("--scale_rot6d", action="store_true", help="Divide rot6d by 2 for balance")
    args = parser.parse_args()

    # Load Hydra config from ../config relative to this script
    with initialize(version_base=None, config_path="../config"):
        cfg = compose(config_name=args.config_name)

    # Derive parameters from config
    dataset_path = cfg.dataset_path
    input_dir = args.input_dir or derive_abs_input_dir(dataset_path)

    # Window size: prefer train_action_seq for action chunk horizon
    window_size = int(cfg.train_action_seq) if args.use_train_action_seq else int(cfg.window_size)

    # For ABS actions we use 17 dims
    action_dim = 17
    num_data_per_file = int(cfg.trainset.num_data)

    # Output path
    if args.output:
        output = args.output
    else:
        out_dir = os.path.dirname(os.path.dirname(input_dir))  # e.g., /data/.../libero_abs
        os.makedirs(out_dir, exist_ok=True)
        output = os.path.join(out_dir, f"dtw_abs_pose_{window_size}.pth")

    print(json.dumps({
        "input_dir": input_dir,
        "window_size": window_size,
        "action_dim": action_dim,
        "num_data_per_file": num_data_per_file,
        "output": output,
        "scale_rot6d": bool(args.scale_rot6d),
    }, indent=2))

    # Build dataset and compute DTW
    ds = build_dataset(
        hdf5_dir=input_dir,
        window_size=window_size,
        action_dim=action_dim,
        num_data_per_file=num_data_per_file,
    )
    print(f"Loaded demos: {ds.num_data}, total slices: {len(ds.slices)}")

    arr_TD = stack_action_chunks_from_slices(ds, window_size)
    print(f"Chunks: {tuple(arr_TD.shape)} (N, T, D)")

    arr_NDT = normalize_and_reformat(arr_TD, scale_rot6d=args.scale_rot6d)
    print(f"Normalized: {tuple(arr_NDT.shape)} (N, D, T)")

    dists = compute_dtw_matrix(arr_NDT.numpy(), method="dtw")
    os.makedirs(os.path.dirname(output), exist_ok=True)
    torch.save(dists, output)
    print(f"Saved DTW matrix to: {output}")


if __name__ == "__main__":
    main()


