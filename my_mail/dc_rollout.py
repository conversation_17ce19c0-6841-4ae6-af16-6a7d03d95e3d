#!/usr/bin/env python3
"""
DC Model Rollout Script
This script loads a DC-trained model and performs rollouts in LIBERO environment.
"""

import os
import sys
import logging
import argparse
from pathlib import Path

import torch
import hydra
from omegaconf import DictConfig, OmegaConf

# Add MaIL to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import the standalone rollout evaluator
from standalone_rollout import LiberoRolloutEvaluator, set_seed_everywhere


class DCRolloutEvaluator(LiberoRolloutEvaluator):
    """
    Specialized evaluator for DC-trained models.
    """
    
    def load_dc_agent(self, dc_config_path, dc_weights_path, base_config_path=None):
        """
        Load DC-trained agent with special handling for DC tokens.

        Args:
            dc_config_path: Path to the DC training config file
            dc_weights_path: Path to the DC model weights file
            base_config_path: Optional path to base model config (if different)
        """
        self.logger.info(f"Loading DC agent configuration from: {dc_config_path}")

        # Load DC training configuration
        with hydra.initialize_config_dir(config_dir=str(Path(dc_config_path).parent.absolute())):
            dc_cfg = hydra.compose(config_name=Path(dc_config_path).stem)

        # Create a simplified agent for rollout (without training components)
        self.logger.info("Creating simplified DC agent for rollout...")

        # Import the necessary classes
        from agents.dc_ddpm_agent import DiffusionPolicy_DC
        from agents.utils import Scaler

        # Create the model directly
        model_cfg = dc_cfg.agents.model

        # Create obs_encoder configuration
        obs_encoder_cfg = {
            "_target_": "agents.module.vision.multi_image_obs_encoder.MultiImageObsEncoder",
            "shape_meta": {
                "obs": {
                    "agentview_rgb": {"shape": [3, 128, 128], "type": "rgb"},
                    "eye_in_hand_rgb": {"shape": [3, 128, 128], "type": "rgb"}
                }
            },
            "rgb_model": {
                "_target_": "agents.module.vision.get_model.get_resnet",
                "input_shape": [3, 128, 128],
                "output_size": 128
            },
            "resize_shape": None,
            "random_crop": False,
            "use_group_norm": True,
            "share_rgb_model": False,
            "imagenet_norm": True
        }

        model = DiffusionPolicy_DC(
            model=model_cfg.model,
            obs_encoder=obs_encoder_cfg,
            visual_input=model_cfg.visual_input,
            device=model_cfg.device
        )

        # Create a simple agent-like object
        class SimpleAgent:
            def __init__(self, model, device):
                self.model = model
                self.device = device
                # Create a dummy scaler (we'll need to initialize it properly later)
                self.scaler = Scaler(
                    x_data=torch.zeros(1, 256),  # dummy data
                    y_data=torch.zeros(1, 7),    # dummy data
                    scale_data=False,            # don't scale for rollout
                    device=device
                )
                self.obs_seq_len = dc_cfg.agents.obs_seq_len
                self.action_seq_size = dc_cfg.agents.action_seq_size

            def act(self, obs, goal=None):
                return self.model.predict(obs, goal)

            def predict(self, obs, goal=None):
                """Predict method for compatibility with rollout evaluator"""
                return self.model.predict(obs, goal)

            def reset(self):
                """Reset method for compatibility with rollout evaluator"""
                pass

        agent = SimpleAgent(model, model_cfg.device)

        # Load DC model weights
        self.logger.info(f"Loading DC model weights from: {dc_weights_path}")
        if not os.path.exists(dc_weights_path):
            raise FileNotFoundError(f"DC model weights not found: {dc_weights_path}")

        # Load the DC checkpoint
        checkpoint = torch.load(dc_weights_path, map_location=agent.device)

        # Load model state dict
        if 'model_state_dict' in checkpoint:
            agent.model.load_state_dict(checkpoint['model_state_dict'])
            self.logger.info("✅ Loaded model_state_dict from checkpoint")
        elif 'state_dict' in checkpoint:
            agent.model.load_state_dict(checkpoint['state_dict'])
            self.logger.info("✅ Loaded state_dict from checkpoint")
        else:
            # Assume the checkpoint is the state dict itself
            agent.model.load_state_dict(checkpoint)
            self.logger.info("✅ Loaded checkpoint as state_dict")

        # Set to evaluation mode
        agent.model.eval()

        # Log DC parameters info
        if hasattr(agent.model, 'model') and hasattr(agent.model.model, 'dc_tokens'):
            dc_tokens = agent.model.model.dc_tokens
            if dc_tokens is not None:
                self.logger.info(f"📊 DC tokens shape: {dc_tokens.shape}")
                self.logger.info(f"📊 DC tokens device: {dc_tokens.device}")
            else:
                self.logger.warning("⚠️ DC tokens not found in model")

        self.agent = agent
        self.cfg = dc_cfg

        self.logger.info("✅ DC Agent loaded successfully!")
        return agent


def main():
    parser = argparse.ArgumentParser(description="DC Model Rollout Evaluation")
    parser.add_argument("--dc-config", type=str, required=True,
                        help="Path to the DC training config file")
    parser.add_argument("--dc-weights", type=str, required=True,
                        help="Path to the DC model weights file")
    parser.add_argument("--base-config", type=str, default=None,
                        help="Path to base model config (optional)")
    parser.add_argument("--task-suite", type=str, default="libero_goal",
                        choices=["libero_spatial", "libero_object", "libero_goal", "libero_90"],
                        help="LIBERO task suite to evaluate on")
    parser.add_argument("--episodes", type=int, default=5,
                        help="Number of episodes per task")
    parser.add_argument("--max-steps", type=int, default=600,
                        help="Maximum steps per episode")
    parser.add_argument("--seed", type=int, default=42,
                        help="Random seed for reproducibility")
    parser.add_argument("--device", type=str, default="cuda",
                        help="Device to run on (cuda/cpu)")
    parser.add_argument("--no-video", action="store_true",
                        help="Disable video recording")
    parser.add_argument("--tasks", type=str, default=None,
                        help="Comma-separated list of task IDs to evaluate (e.g., '0,1,2')")
    parser.add_argument("--data-aug", action="store_true",
                        help="Enable data augmentation during evaluation")
    parser.add_argument("--aug-factor", type=float, default=0.02,
                        help="Data augmentation factor")
    
    args = parser.parse_args()
    
    # Set seed
    set_seed_everywhere(args.seed)
    
    # Parse task IDs if specified
    task_ids = None
    if args.tasks:
        task_ids = [int(x.strip()) for x in args.tasks.split(',')]
    
    # Create DC evaluator
    evaluator = DCRolloutEvaluator(
        task_suite=args.task_suite,
        num_episodes=args.episodes,
        max_steps_per_episode=args.max_steps,
        seed=args.seed,
        device=args.device,
        save_video=not args.no_video,
        data_aug=args.data_aug,
        aug_factor=args.aug_factor
    )
    
    # Load DC agent
    try:
        evaluator.load_dc_agent(args.dc_config, args.dc_weights, args.base_config)
    except Exception as e:
        print(f"❌ Failed to load DC agent: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    # Run evaluation
    try:
        results = evaluator.evaluate_tasks(task_ids=task_ids)
        print(f"\n🎉 DC Model Evaluation completed successfully!")
        print(f"📊 Overall Success Rate: {results['overall_success_rate']:.2%}")
        return 0
    except Exception as e:
        print(f"❌ Evaluation failed: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit(main())
