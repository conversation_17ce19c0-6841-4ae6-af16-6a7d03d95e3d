# PyTorch and CUDA dependencies (uncomment when ready)
# torch>=2.0.0,<3.0.0
# torchvision>=0.15.0
# torchaudio>=2.0.0
# For CUDA 12.1 support, use: --index-url https://download.pytorch.org/whl/cu121

# Core dependencies (updated versions)
hydra-core>=1.3.0
numpy>=1.24.0,<2.0.0
wandb>=0.16.0
easydict>=1.10
transformers>=4.35.0
opencv-python>=4.8.0
einops>=0.7.0
thop>=0.1.1-2209072238

# Robotics dependencies (modified for compatibility)
robomimic>=0.3.0
# robosuite>=1.4.0  # May require egl_probe, install separately if needed
bddl>=2.0.0

# Additional dependencies
future>=0.18.2
matplotlib>=3.7.0
cloudpickle>=2.2.0
gym>=0.26.0
imageio[ffmpeg]>=2.31.0

# System dependencies note:
# Before installing, make sure you have:
# - cmake (conda install cmake -c conda-forge)
# - build-essential (apt-get install build-essential)
# - libgl1-mesa-dev libglu1-mesa-dev (for OpenGL)

# Optional packages (install manually if needed):
# egl-probe==1.0.2  # Requires cmake
# robosuite>=1.4.0  # May depend on egl_probe

# Optional GPU acceleration
# Uncomment if using specific CUDA version:
# --find-links https://download.pytorch.org/whl/torch_stable.html
# torch==2.1.0+cu121
# torchvision==0.16.0+cu121
# torchaudio==2.1.0+cu121