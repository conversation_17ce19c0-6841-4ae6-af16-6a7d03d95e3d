#!/usr/bin/env python3
"""
Train Diffusion Policy with Transformer on LIBERO Goal tasks.
Enhanced version with comprehensive logging capabilities and resume training support.
"""
import os
import logging
import random
import sys
import time
from datetime import datetime
from pathlib import Path
import glob

import hydra
import numpy as np
import multiprocessing as mp
import wandb
# import swanlab
from omegaconf import DictConfig, OmegaConf
import torch
from tqdm import tqdm

from agents.utils import sim_framework_path



log = logging.getLogger(__name__)

print(f"CUDA Available: {torch.cuda.is_available()}")
if torch.cuda.is_available():
    print(f"CUDA Device: {torch.cuda.get_device_name()}")

OmegaConf.register_new_resolver(
     "add", lambda *numbers: sum(numbers)
)
torch.cuda.empty_cache()


def setup_logging(cfg):
    """
    Setup comprehensive logging system for training process.
    
    Args:
        cfg: Configuration object containing logging parameters
        
    Returns:
        tuple: (main_logger, training_logger, evaluation_logger, log_dir)
    """
    # Create log directory with timestamp
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_dir = Path(f"logs/{cfg.agent_name}_{cfg.task_suite}_{timestamp}")
    log_dir.mkdir(parents=True, exist_ok=True)
    
    # Configure main logger
    main_logger = logging.getLogger("main")
    main_logger.setLevel(logging.INFO)
    
    # Configure training logger
    training_logger = logging.getLogger("training")
    training_logger.setLevel(logging.DEBUG)
    
    # Configure evaluation logger
    evaluation_logger = logging.getLogger("evaluation")
    evaluation_logger.setLevel(logging.INFO)
    
    # Clear existing handlers
    for logger in [main_logger, training_logger, evaluation_logger]:
        logger.handlers.clear()
    
    # Create formatters
    detailed_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    simple_formatter = logging.Formatter(
        '%(asctime)s - %(levelname)s - %(message)s'
    )
    
    # File handlers
    main_file_handler = logging.FileHandler(log_dir / "main.log")
    main_file_handler.setLevel(logging.INFO)
    main_file_handler.setFormatter(detailed_formatter)
    
    training_file_handler = logging.FileHandler(log_dir / "training.log")
    training_file_handler.setLevel(logging.DEBUG)
    training_file_handler.setFormatter(detailed_formatter)
    
    evaluation_file_handler = logging.FileHandler(log_dir / "evaluation.log")
    evaluation_file_handler.setLevel(logging.INFO)
    evaluation_file_handler.setFormatter(detailed_formatter)
    
    # Console handler (only for main logger to avoid duplicate output)
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(simple_formatter)
    
    # Add handlers to loggers
    main_logger.addHandler(main_file_handler)
    main_logger.addHandler(console_handler)
    
    training_logger.addHandler(training_file_handler)
    evaluation_logger.addHandler(evaluation_file_handler)
    
    # Log setup completion
    main_logger.info("=" * 50)
    main_logger.info("🚀 Logging system initialized")
    main_logger.info(f"📁 Log directory: {log_dir}")
    main_logger.info("=" * 50)
    
    return main_logger, training_logger, evaluation_logger, log_dir


class LoggingTrainingWrapper:
    """
    Wrapper class to add detailed logging to the training process with resume support.
    """

    def __init__(self, agent, cfg, main_logger, training_logger):
        self.agent = agent
        self.cfg = cfg
        self.main_logger = main_logger
        self.training_logger = training_logger
        self.start_time = None
        self.start_epoch = 0
        self.start_step = 0
        self.best_test_mse = float('inf')
        self.checkpoint_dir = None
        
    def log_system_info(self):
        """Log system and configuration information."""
        self.main_logger.info("🖥️  System Information:")
        self.main_logger.info(f"   CUDA Available: {torch.cuda.is_available()}")
        if torch.cuda.is_available():
            self.main_logger.info(f"   CUDA Device: {torch.cuda.get_device_name()}")
            self.main_logger.info(f"   CUDA Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
        
        self.main_logger.info(f"   Python Version: {sys.version}")
        self.main_logger.info(f"   PyTorch Version: {torch.__version__}")
        self.main_logger.info(f"   Available CPUs: {mp.cpu_count()}")
    
    def log_configuration(self):
        """Log detailed configuration information."""
        self.main_logger.info("📝 Detailed Configuration:")
        self.main_logger.info(f"   Agent: {self.cfg.agent_name}")
        self.main_logger.info(f"   Task Suite: {self.cfg.task_suite}")
        self.main_logger.info(f"   Diffusion Steps: {self.cfg.diff_steps}")
        self.main_logger.info(f"   Encoder Layers: {self.cfg.encoder_n_layer}")
        self.main_logger.info(f"   Decoder Layers: {self.cfg.decoder_n_layer}")
        self.main_logger.info(f"   Batch Size: {self.cfg.train_batch_size}")
        self.main_logger.info(f"   Epochs: {self.cfg.epoch}")
        self.main_logger.info(f"   Learning Rate: {getattr(self.cfg, 'lr', 'Not specified')}")
        self.main_logger.info(f"   Device: {self.cfg.device}")
        self.main_logger.info(f"   Seed: {self.cfg.seed}")
        self.main_logger.info(f"   Number of Cores: {self.cfg.n_cores}")
        
        # Log dataset information if available
        if hasattr(self.agent, 'train_dataloader'):
            self.main_logger.info(f"   Training Dataset Size: {len(self.agent.train_dataloader)} batches")
        
        # Log model information if available
        if hasattr(self.agent, 'model'):
            total_params = sum(p.numel() for p in self.agent.model.parameters())
            trainable_params = sum(p.numel() for p in self.agent.model.parameters() if p.requires_grad)
            self.main_logger.info(f"   Total Parameters: {total_params:,}")
            self.main_logger.info(f"   Trainable Parameters: {trainable_params:,}")
    
    def setup_resume_training(self):
        """设置resume训练相关的参数和状态"""
        # 设置checkpoint目录
        if (hasattr(self.cfg, 'resume') and
            hasattr(self.cfg.resume, 'checkpoint_dir') and
            self.cfg.resume.checkpoint_dir is not None):
            self.checkpoint_dir = Path(self.cfg.resume.checkpoint_dir)
        else:
            # 默认checkpoint目录
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            self.checkpoint_dir = Path(f"checkpoints/{self.cfg.agent_name}_{timestamp}")

        self.checkpoint_dir.mkdir(parents=True, exist_ok=True)
        self.main_logger.info(f"📁 Checkpoint目录: {self.checkpoint_dir}")

        # 检查是否需要resume训练
        if hasattr(self.cfg, 'resume') and self.cfg.resume.enabled:
            checkpoint_path = None

            # 如果指定了具体的checkpoint文件
            if hasattr(self.cfg.resume, 'checkpoint_path') and self.cfg.resume.checkpoint_path:
                checkpoint_path = self.cfg.resume.checkpoint_path
                if not Path(checkpoint_path).exists():
                    self.main_logger.error(f"❌ 指定的checkpoint文件不存在: {checkpoint_path}")
                    raise FileNotFoundError(f"Checkpoint文件不存在: {checkpoint_path}")
            else:
                # 自动查找最新的checkpoint
                checkpoint_path = find_latest_checkpoint(self.checkpoint_dir)
                if checkpoint_path:
                    self.main_logger.info(f"🔍 找到最新checkpoint: {checkpoint_path}")
                else:
                    self.main_logger.info("📝 未找到checkpoint文件，将从头开始训练")

            # 如果找到checkpoint，加载它
            if checkpoint_path:
                try:
                    # 获取优化器
                    optimizer = None
                    if hasattr(self.agent, 'optimizer'):
                        optimizer = self.agent.optimizer
                    elif hasattr(self.agent, 'optim'):
                        optimizer = self.agent.optim

                    self.start_epoch, self.start_step, self.best_test_mse = load_training_checkpoint(
                        checkpoint_path, self.agent, optimizer
                    )

                    self.main_logger.info(f"🔄 Resume训练设置完成")
                    self.main_logger.info(f"   起始epoch: {self.start_epoch}")
                    self.main_logger.info(f"   起始step: {self.start_step}")
                    self.main_logger.info(f"   最佳测试MSE: {self.best_test_mse:.6f}")

                except Exception as e:
                    self.main_logger.error(f"❌ 加载checkpoint失败: {e}")
                    self.main_logger.info("📝 将从头开始训练")
                    self.start_epoch = 0
                    self.start_step = 0
                    self.best_test_mse = float('inf')
        else:
            self.main_logger.info("📝 从头开始训练")

    def log_training_start(self):
        """Log training start information."""
        self.start_time = time.time()
        if self.start_epoch > 0:
            self.main_logger.info(f"🔄 Resume训练，从epoch {self.start_epoch + 1}开始...")
        else:
            self.main_logger.info("🎯 Starting enhanced training with detailed logging...")
        self.training_logger.info("Training session started")
        self.training_logger.info(f"Configuration: {OmegaConf.to_yaml(self.cfg)}")
    
    def log_training_completion(self):
        """Log training completion information."""
        total_time = time.time() - self.start_time
        self.main_logger.info(f"✅ Training completed! Total time: {total_time:.1f}s ({total_time/60:.1f} minutes)")
        self.training_logger.info(f"Training session completed. Total duration: {total_time:.1f}s")
    
    def train_with_logging(self):
        """
        Execute training with comprehensive logging and periodic test batch evaluation.
        """
        # 设置resume训练
        self.setup_resume_training()

        self.log_training_start()

        if hasattr(self.agent, 'train_vision_agent'):
            # Implement enhanced training with test batch evaluation
            self.enhanced_train_vision_agent_with_test_batches()
        else:
            self.main_logger.error("❌ Agent does not have train_vision_agent method")
            raise AttributeError("Agent must have train_vision_agent method")
    
    def enhanced_train_vision_agent_with_test_batches(self):
        """
        Enhanced training method that includes periodic test batch evaluation like the original train_agent method.
        """
        self.main_logger.info("🔧 Initializing enhanced training process with test batch evaluation...")
        
        # 获取优化器
        optimizer = None
        if hasattr(self.agent, 'optimizer'):
            optimizer = self.agent.optimizer
        elif hasattr(self.agent, 'optim'):
            optimizer = self.agent.optim
        else:
            self.main_logger.warning("⚠️  未找到优化器，checkpoint将不包含优化器状态")

        # 使用resume的最佳测试MSE，如果没有则使用默认值
        best_test_mse = self.best_test_mse if self.best_test_mse != float('inf') else 1e10

        total_epochs = self.agent.epoch
        remaining_epochs = total_epochs - self.start_epoch

        self.main_logger.info(f"🚀 开始训练，总共 {total_epochs} 个epoch")
        if self.start_epoch > 0:
            self.main_logger.info(f"🔄 Resume训练，从epoch {self.start_epoch + 1}开始，剩余 {remaining_epochs} 个epoch")
        self.main_logger.info(f"📊 数据集大小: {len(self.agent.train_dataloader)} 批次")

        # Check if test dataloader exists
        has_test_dataloader = hasattr(self.agent, 'test_dataloader') and self.agent.test_dataloader is not None
        if has_test_dataloader:
            self.main_logger.info(f"📋 测试数据集大小: {len(self.agent.test_dataloader)} 批次")
            self.main_logger.info(f"🔍 每 {self.agent.eval_every_n_epochs} 个epoch进行一次测试批次评估")
        else:
            self.main_logger.warning("⚠️  未找到测试数据加载器，将跳过测试批次评估")

        # 设置checkpoint保存频率
        checkpoint_freq = getattr(self.cfg, 'checkpoint_every_n_epochs', 10)  # 默认每10个epoch保存一次
        self.main_logger.info(f"💾 每 {checkpoint_freq} 个epoch保存一次checkpoint")

        try:
            # 添加epoch循环和进度条，从start_epoch开始
            epoch_range = range(self.start_epoch, total_epochs)
            for num_epoch in tqdm(epoch_range, desc="🎯 训练进度", unit="epoch", initial=self.start_epoch, total=total_epochs):
                
                # Run test batch every n epochs (like original train_agent method)
                if has_test_dataloader and not (num_epoch + 1) % self.agent.eval_every_n_epochs:
                    self.main_logger.info(f"🧪 Running test batch evaluation at epoch {num_epoch + 1}")
                    self.training_logger.info(f"Starting test batch evaluation at epoch {num_epoch + 1}")
                    
                    test_mse = []
                    test_start_time = time.time()
                    
                    # Run evaluation on test batches
                    for test_batch_idx, data in enumerate(self.agent.test_dataloader):
                        try:
                            # Following the same data format as train_vision_agent
                            bp_imgs, inhand_imgs, action, goal_imgs = data
                            
                            bp_imgs = bp_imgs.to(self.agent.device)
                            inhand_imgs = inhand_imgs.to(self.agent.device)
                            goal_imgs = goal_imgs.to(self.agent.device)
                            
                            action = self.agent.scaler.scale_output(action)
                            action = action[:, self.agent.obs_seq_len - 1:, :].contiguous()
                            bp_imgs = bp_imgs[:, :self.agent.obs_seq_len].contiguous()
                            inhand_imgs = inhand_imgs[:, :self.agent.obs_seq_len].contiguous()
                            
                            state = (bp_imgs, inhand_imgs, goal_imgs)
                            
                            # Compute test loss directly using the model's loss function
                            # (avoiding agent.evaluate method due to scaler compatibility issues)
                            with torch.no_grad():
                                self.agent.model.eval()
                                # Use model's loss function directly for consistent evaluation
                                if hasattr(self.agent.model, 'loss'):
                                    loss = self.agent.model.loss(action, state, goal_imgs)
                                    mean_mse = loss.mean().item()
                                else:
                                    # Fallback: compute prediction and MSE manually
                                    pred_action = self.agent.model(state, goal_imgs)
                                    mean_mse = torch.nn.functional.mse_loss(pred_action, action).item()
                            
                            test_mse.append(mean_mse)
                            
                        except Exception as e:
                            self.training_logger.error(f"Error in test batch {test_batch_idx}: {e}")
                            continue
                    
                    if test_mse:
                        avrg_test_mse = sum(test_mse) / len(test_mse)
                        test_time = time.time() - test_start_time
                        
                        self.main_logger.info(f"📊 Epoch {num_epoch + 1}: Mean test MSE = {avrg_test_mse:.6f} (time: {test_time:.1f}s)")
                        self.training_logger.info(f"Test evaluation completed - MSE: {avrg_test_mse:.6f}, batches: {len(test_mse)}")
                        
                        # Log to wandb
                        wandb.log({
                            "test_mse": avrg_test_mse,
                            "test_epoch": num_epoch + 1,
                            "test_evaluation_time": test_time
                        })
                        
                        # Save best model based on test performance
                        if avrg_test_mse < best_test_mse:
                            best_test_mse = avrg_test_mse

                            self.main_logger.info(f"🏆 New best test MSE: {best_test_mse:.6f}")
                            self.training_logger.info(f"Best model updated at epoch {num_epoch + 1}")

                            # 1. 传统模型保存 (用于推理/部署)
                            if hasattr(self.agent, 'working_dir') and hasattr(self.agent, 'eval_model_name'):
                                self.agent.store_model_weights(self.agent.working_dir, sv_name=self.agent.eval_model_name)
                                self.main_logger.info(f"💾 最佳模型权重已保存: {self.agent.working_dir}/{self.agent.eval_model_name}")

                            # 2. Checkpoint保存 (用于resume训练)
                            if optimizer is not None:
                                save_training_checkpoint(
                                    self.agent, optimizer, num_epoch, 0, avrg_test_mse,
                                    self.checkpoint_dir, is_best=True
                                )
                                self.main_logger.info(f"💾 最佳训练checkpoint已保存: {self.checkpoint_dir}/best_checkpoint.pth")

                            wandb.log({
                                "best_test_mse": best_test_mse,
                                "best_model_epochs": num_epoch + 1
                            })
                    else:
                        self.main_logger.warning("⚠️  No valid test batches processed")
                
                # Training phase (same as original train_vision_agent)
                epoch_losses = []
                
                # 为每个epoch内的batch添加进度条
                batch_iterator = tqdm(
                    self.agent.train_dataloader, 
                    desc=f"Epoch {num_epoch+1}/{self.agent.epoch}", 
                    leave=False,
                    unit="batch"
                )
                
                for batch_idx, data in enumerate(batch_iterator):
                    try:
                        bp_imgs, inhand_imgs, action, goal_imgs = data
                        
                        bp_imgs = bp_imgs.to(self.agent.device)
                        inhand_imgs = inhand_imgs.to(self.agent.device)
                        goal_imgs = goal_imgs.to(self.agent.device)
                        
                        action = self.agent.scaler.scale_output(action)
                        action = action[:, self.agent.obs_seq_len - 1:, :].contiguous()
                        bp_imgs = bp_imgs[:, :self.agent.obs_seq_len].contiguous()
                        inhand_imgs = inhand_imgs[:, :self.agent.obs_seq_len].contiguous()
                        
                        state = (bp_imgs, inhand_imgs, goal_imgs)
                        
                        batch_loss = self.agent.train_step(state, action)
                        epoch_losses.append(batch_loss.item())
                        
                        # 更新批次进度条显示当前loss
                        batch_iterator.set_postfix({
                            'Loss': f'{batch_loss.item():.4f}',
                            'Avg_Loss': f'{np.mean(epoch_losses):.4f}'
                        })
                        
                        # Log training progress periodically
                        if batch_idx % 10 == 0:
                            self.training_logger.debug(
                                f"Epoch {num_epoch+1}, Batch {batch_idx+1}, Loss: {batch_loss.item():.6f}"
                            )
                        
                        # wandb logging
                        wandb.log({
                            "train_loss": batch_loss.item(),
                            "epoch": num_epoch,
                            "batch": batch_idx
                        })
                        
                    except Exception as e:
                        self.training_logger.error(f"Error in training batch {batch_idx}: {e}")
                        continue
                
                # epoch结束后的统计
                if epoch_losses:
                    avg_epoch_loss = np.mean(epoch_losses)

                    self.main_logger.info(f"📈 Epoch {num_epoch+1}/{total_epochs} 完成 - 平均损失: {avg_epoch_loss:.4f}")
                    self.training_logger.info(f"Epoch {num_epoch+1} training completed - Average loss: {avg_epoch_loss:.6f}")

                    # 记录epoch级别的指标
                    wandb.log({
                        "epoch_avg_loss": avg_epoch_loss,
                        "epoch": num_epoch
                    })

                    # 定期保存checkpoint (用于resume训练)
                    if optimizer is not None and (num_epoch + 1) % checkpoint_freq == 0:
                        save_training_checkpoint(
                            self.agent, optimizer, num_epoch, 0, avg_epoch_loss,
                            self.checkpoint_dir, is_best=False
                        )
                        self.main_logger.info(f"💾 定期训练checkpoint已保存 (epoch {num_epoch + 1}) - 用于resume训练")
            
            self.main_logger.info("✅ Enhanced training with test batch evaluation completed!")

            self.main_logger.info("💾 保存训练结果...")

            # 1. 传统模型保存 (用于推理/部署)
            if hasattr(self.agent, 'working_dir') and hasattr(self.agent, 'last_model_name'):
                self.agent.store_model_weights(self.agent.working_dir, sv_name=self.agent.last_model_name)
                self.main_logger.info(f"✅ 最终模型权重已保存: {self.agent.working_dir}/{self.agent.last_model_name}")
                self.main_logger.info(f"   📋 用途: 模型推理、部署、微调")

            # 2. Checkpoint保存 (用于resume训练)
            if optimizer is not None:
                final_loss = np.mean(epoch_losses) if epoch_losses else float('inf')
                save_training_checkpoint(
                    self.agent, optimizer, total_epochs - 1, 0, final_loss,
                    self.checkpoint_dir, is_best=False
                )
                self.main_logger.info(f"✅ 最终训练checkpoint已保存: {self.checkpoint_dir}")
                self.main_logger.info(f"   📋 用途: Resume训练、实验管理")

            self.log_training_completion()
            
        except Exception as e:
            self.main_logger.error(f"❌ Enhanced training failed with error: {e}")
            self.training_logger.error(f"Enhanced training failed: {e}", exc_info=True)
            raise


def set_seed_everywhere(seed):
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed_all(seed)
    np.random.seed(seed)
    random.seed(seed)


def save_training_checkpoint(agent, optimizer, epoch, step, loss, checkpoint_dir, is_best=False):
    """
    保存训练checkpoint，包含完整的训练状态信息 (用于resume训练)

    与agent.store_model_weights()的区别:
    - agent.store_model_weights(): 只保存模型权重，用于推理/部署/微调
    - save_training_checkpoint(): 保存完整训练状态，用于resume训练

    Args:
        agent: 训练的agent对象
        optimizer: 优化器对象
        epoch: 当前epoch
        step: 当前训练步数
        loss: 当前损失值
        checkpoint_dir: checkpoint保存目录
        is_best: 是否为最佳模型
    """
    checkpoint_dir = Path(checkpoint_dir)
    checkpoint_dir.mkdir(parents=True, exist_ok=True)

    # 准备checkpoint数据
    checkpoint_data = {
        'epoch': epoch,
        'step': step,
        'loss': loss,
        'model_state_dict': agent.model.state_dict(),
        'optimizer_state_dict': optimizer.state_dict(),
        'random_state': torch.get_rng_state(),
        'numpy_random_state': np.random.get_state(),
        'python_random_state': random.getstate(),
    }

    # 如果使用EMA，保存EMA状态
    if hasattr(agent, 'use_ema') and agent.use_ema and hasattr(agent, 'ema_helper'):
        checkpoint_data['ema_state'] = {
            'decay': agent.ema_helper.decay,
            'shadow_params': [param.clone() for param in agent.ema_helper.shadow_params]
        }

    # 保存checkpoint
    if is_best:
        checkpoint_path = checkpoint_dir / "best_checkpoint.pth"
        log.info(f"💾 保存最佳checkpoint到: {checkpoint_path}")
    else:
        checkpoint_path = checkpoint_dir / f"checkpoint_epoch_{epoch}.pth"
        log.info(f"💾 保存checkpoint到: {checkpoint_path}")

    torch.save(checkpoint_data, checkpoint_path)

    # 同时保存最新的checkpoint
    latest_path = checkpoint_dir / "latest_checkpoint.pth"
    torch.save(checkpoint_data, latest_path)

    return checkpoint_path


def load_training_checkpoint(checkpoint_path, agent, optimizer=None):
    """
    从checkpoint恢复训练状态

    Args:
        checkpoint_path: checkpoint文件路径
        agent: agent对象
        optimizer: 优化器对象（可选）

    Returns:
        tuple: (start_epoch, start_step, best_loss)
    """
    checkpoint_path = Path(checkpoint_path)

    if not checkpoint_path.exists():
        raise FileNotFoundError(f"Checkpoint文件不存在: {checkpoint_path}")

    log.info(f"🔄 从checkpoint恢复训练: {checkpoint_path}")

    # 加载checkpoint数据
    checkpoint_data = torch.load(checkpoint_path, map_location=agent.device)

    # 恢复模型权重
    agent.model.load_state_dict(checkpoint_data['model_state_dict'])
    log.info("✅ 模型权重已恢复")

    # 恢复优化器状态
    if optimizer is not None and 'optimizer_state_dict' in checkpoint_data:
        optimizer.load_state_dict(checkpoint_data['optimizer_state_dict'])
        log.info("✅ 优化器状态已恢复")

    # 恢复随机数状态
    if 'random_state' in checkpoint_data:
        torch.set_rng_state(checkpoint_data['random_state'])
    if 'numpy_random_state' in checkpoint_data:
        np.random.set_state(checkpoint_data['numpy_random_state'])
    if 'python_random_state' in checkpoint_data:
        random.setstate(checkpoint_data['python_random_state'])
    log.info("✅ 随机数状态已恢复")

    # 恢复EMA状态
    if 'ema_state' in checkpoint_data and hasattr(agent, 'use_ema') and agent.use_ema:
        if hasattr(agent, 'ema_helper'):
            ema_state = checkpoint_data['ema_state']
            agent.ema_helper.decay = ema_state['decay']
            for i, shadow_param in enumerate(ema_state['shadow_params']):
                agent.ema_helper.shadow_params[i].copy_(shadow_param)
            log.info("✅ EMA状态已恢复")

    start_epoch = checkpoint_data.get('epoch', 0)
    start_step = checkpoint_data.get('step', 0)
    best_loss = checkpoint_data.get('loss', float('inf'))

    log.info(f"📊 恢复训练状态: epoch={start_epoch}, step={start_step}, loss={best_loss:.6f}")

    return start_epoch, start_step, best_loss


def find_latest_checkpoint(checkpoint_dir):
    """
    在指定目录中查找最新的checkpoint文件

    Args:
        checkpoint_dir: checkpoint目录路径

    Returns:
        str or None: 最新checkpoint文件路径，如果没有找到则返回None
    """
    checkpoint_dir = Path(checkpoint_dir)

    if not checkpoint_dir.exists():
        return None

    # 首先查找latest_checkpoint.pth
    latest_path = checkpoint_dir / "latest_checkpoint.pth"
    if latest_path.exists():
        return str(latest_path)

    # 查找所有checkpoint文件
    checkpoint_pattern = checkpoint_dir / "checkpoint_epoch_*.pth"
    checkpoint_files = glob.glob(str(checkpoint_pattern))

    if not checkpoint_files:
        return None

    # 按修改时间排序，返回最新的
    latest_file = max(checkpoint_files, key=os.path.getmtime)
    return latest_file


@hydra.main(config_path="config", config_name="benchmark_libero_goal_diffusion_transformer.yaml", version_base="1.1")
def main(cfg: DictConfig) -> None:
    # Setup comprehensive logging system
    main_logger, training_logger, evaluation_logger, log_dir = setup_logging(cfg)
    
    main_logger.info("🚀 Training Diffusion Policy + Transformer on LIBERO Goal")
    main_logger.info("📊 Enhanced version with comprehensive logging")
    
    set_seed_everywhere(cfg.seed)
    main_logger.info(f"🎲 Random seed set to: {cfg.seed}")
    
    # Create wrapper for enhanced logging
    try:
        # Setup wandb with logging FIRST (before agent instantiation)
        wandb_enabled = True
        main_logger.info("🔍 Initializing Wandb logging (before agent creation)...")
        
        if hasattr(cfg, 'wandb'):
            main_logger.info(f"   Entity: {getattr(cfg.wandb, 'entity', 'Not specified')}")
            main_logger.info(f"   Project: {getattr(cfg.wandb, 'project', 'Not specified')}")
        
        if wandb_enabled:
            try:
                wandb_config = OmegaConf.to_container(cfg, resolve=True, throw_on_missing=True)
                
                # Generate run name with timestamp
                timestamp = datetime.now().strftime("%m%d_%H%M")
                run_name = f"{cfg.agent_name}_{cfg.task_suite}_ep{cfg.epoch}_{timestamp}"
                
                init_kwargs = {
                    "project": cfg.wandb.project,
                    "group": cfg.group,
                    "name": run_name,
                    "mode": "online",
                    "config": wandb_config,
                    "tags": [cfg.agent_name, cfg.task_suite, f"epochs_{cfg.epoch}"]
                }
                if cfg.wandb.entity is not None:
                    init_kwargs["entity"] = cfg.wandb.entity

                run = wandb.init(**init_kwargs)
                main_logger.info(f"✅ Wandb initialized successfully with run: {run_name}")
                
                # Log the wandb run URL
                main_logger.info(f"📊 Wandb URL: {run.get_url()}")
                
            except Exception as e:
                main_logger.warning(f"⚠️  Wandb initialization failed: {e}")
                main_logger.info("📊 Continuing without wandb logging")
                wandb_enabled = False
                try:
                    wandb.init(mode="disabled", project="dummy")
                except:
                    pass
        else:
            main_logger.info("📊 Wandb disabled - continuing without online logging")
            try:
                wandb.init(mode="disabled", project="dummy")
            except:
                pass

        # Now instantiate agent (after wandb is initialized)
        main_logger.info("🔧 Instantiating agent...")
        agent = hydra.utils.instantiate(cfg.agents)
        main_logger.info(f"✅ Agent created: {type(agent).__name__}")
        
        # Create logging wrapper
        training_wrapper = LoggingTrainingWrapper(agent, cfg, main_logger, training_logger)
        
        # Log system and configuration information
        training_wrapper.log_system_info()
        training_wrapper.log_configuration()

        # Execute training with enhanced logging
        training_wrapper.train_with_logging()

        # Evaluation phase with detailed logging
        main_logger.info("🧪 Starting evaluation phase...")
        evaluation_logger.info("Evaluation phase started")
        
        eval_start_time = time.time()
        
        num_cpu = mp.cpu_count()
        cpu_set = list(range(num_cpu))
        main_logger.info(f"💻 System CPUs available: {num_cpu}")
        evaluation_logger.info(f"Available CPUs: {num_cpu}")

        assign_cpus = cpu_set[cfg.seed * cfg.n_cores:cfg.seed * cfg.n_cores + cfg.n_cores]
        main_logger.info(f"🔗 Assigned CPUs for evaluation: {assign_cpus}")
        evaluation_logger.info(f"Assigned CPUs: {assign_cpus}")

        main_logger.info("🌍 Creating environment simulation...")
        env_sim = hydra.utils.instantiate(cfg.simulation)
        main_logger.info("✅ Environment simulation created successfully")
        evaluation_logger.info("Environment simulation instantiated")
        
        # Log evaluation configuration
        if hasattr(cfg.simulation, 'num_episode'):
            main_logger.info(f"📋 Evaluation episodes per task: {cfg.simulation.num_episode}")
            evaluation_logger.info(f"Episodes per task: {cfg.simulation.num_episode}")
        
        main_logger.info("🎮 Running agent evaluation...")
        evaluation_logger.info(f"Starting agent evaluation at epoch {agent.epoch}")
        
        try:
            env_sim.test_agent(agent, assign_cpus, epoch=agent.epoch)
            
            eval_time = time.time() - eval_start_time
            main_logger.info(f"✅ Evaluation completed successfully! Time: {eval_time:.1f}s")
            evaluation_logger.info(f"Evaluation completed successfully in {eval_time:.1f}s")
            
        except Exception as e:
            main_logger.error(f"❌ Evaluation failed: {e}")
            evaluation_logger.error(f"Evaluation failed: {e}", exc_info=True)
            raise

        # Final logging
        total_time = time.time() - training_wrapper.start_time if training_wrapper.start_time else 0
        main_logger.info("🎉 Training and evaluation finished successfully!")
        main_logger.info(f"⏱️  Total execution time: {total_time:.1f}s ({total_time/60:.1f} minutes)")
        main_logger.info(f"📁 All logs saved to: {log_dir}")
        
        evaluation_logger.info("All processes completed successfully")
        
        if wandb_enabled:
            main_logger.info("📊 Finishing Wandb session...")
            wandb.finish()

        main_logger.info("=" * 50)
        main_logger.info("🏁 All done! Check the detailed logs for complete results.")
        main_logger.info(f"📂 Log files location: {log_dir}")
        main_logger.info("=" * 50)
        
    except Exception as e:
        main_logger.error(f"💥 Fatal error occurred: {e}")
        main_logger.error("Check the log files for detailed error information")
        if 'training_logger' in locals():
            training_logger.error(f"Fatal error: {e}", exc_info=True)
        raise


if __name__ == "__main__":
    main() 