#!/bin/bash

echo "MaIL Environment Setup - Fixed Version"
echo "======================================"

# Activate conda environment
source ~/.bashrc
conda activate mail

echo "Step 1: Installing core dependencies without problematic packages..."

# Install core packages first
pip install hydra-core>=1.3.0
pip install "numpy>=1.24.0,<2.0.0"
pip install wandb>=0.16.0
pip install easydict>=1.10
pip install transformers>=4.35.0
pip install opencv-python>=4.8.0
pip install einops>=0.7.0
pip install matplotlib>=3.7.0
pip install cloudpickle>=2.2.0
pip install future>=0.18.2
pip install "imageio[ffmpeg]>=2.31.0"

echo ""
echo "Step 2: Installing robotics dependencies (skipping problematic ones)..."

# Install robomimic
pip install robomimic>=0.3.0

# Install gym
pip install gym>=0.26.0

# Install bddl
pip install bddl>=2.0.0

# Install thop
pip install thop>=0.1.1-2209072238

echo ""
echo "Step 3: Attempting alternative approaches for robosuite..."

# Try different approaches for robosuite
echo "Trying to install robosuite without egl_probe..."

# Method 1: Try installing robosuite directly (it might work without egl_probe)
if pip install robosuite>=1.4.0 --no-deps; then
    echo "Robosuite installed successfully without dependencies"
    # Then install its other dependencies manually
    pip install pynput
    pip install six
    pip install requests
else
    echo "Direct robosuite installation failed"
fi

# Method 2: Try installing an older version of robosuite
if ! python -c "import robosuite" 2>/dev/null; then
    echo "Trying older version of robosuite..."
    pip install robosuite==1.3.2
fi

# Method 3: Skip robosuite entirely if still failing
if ! python -c "import robosuite" 2>/dev/null; then
    echo "Warning: Robosuite installation failed. Continuing without it."
    echo "You can try installing it manually later with:"
    echo "  pip install robosuite --no-deps"
fi

echo ""
echo "Step 4: Installing PyTorch..."
echo "Select your CUDA version:"
echo "1) CUDA 12.1 (recommended)"
echo "2) CUDA 12.4 (latest)"  
echo "3) CUDA 11.8 (older systems)"
echo "4) CPU only"
echo "5) Skip PyTorch"
echo ""
read -p "Enter choice (1-5): " choice

case $choice in
    1)
        pip install torch==2.1.0+cu121 torchvision==0.16.0+cu121 torchaudio==2.1.0+cu121 \
            --index-url https://download.pytorch.org/whl/cu121
        ;;
    2)
        pip install torch==2.4.0+cu124 torchvision==0.19.0+cu124 torchaudio==2.4.0+cu124 \
            --index-url https://download.pytorch.org/whl/cu124
        ;;
    3)
        pip install torch==2.1.0+cu118 torchvision==0.16.0+cu118 torchaudio==2.1.0+cu118 \
            --index-url https://download.pytorch.org/whl/cu118
        ;;
    4)
        pip install torch==2.1.0+cpu torchvision==0.16.0+cpu torchaudio==2.1.0+cpu \
            --index-url https://download.pytorch.org/whl/cpu
        ;;
    5)
        echo "Skipping PyTorch installation"
        ;;
    *)
        echo "Invalid choice. Skipping PyTorch."
        ;;
esac

echo ""
echo "Step 5: Verification..."

python -c "
import sys
print(f'Python version: {sys.version}')

packages = [
    ('numpy', 'NumPy'),
    ('torch', 'PyTorch'), 
    ('transformers', 'Transformers'),
    ('robomimic', 'Robomimic'),
    ('robosuite', 'Robosuite'),
    ('hydra', 'Hydra'),
    ('wandb', 'Wandb'),
    ('cv2', 'OpenCV'),
    ('einops', 'Einops'),
    ('matplotlib', 'Matplotlib')
]

for pkg, name in packages:
    try:
        if pkg == 'torch':
            import torch
            print(f'{name}: {torch.__version__} (CUDA: {torch.cuda.is_available()})')
        elif pkg == 'cv2':
            import cv2
            print(f'{name}: {cv2.__version__}')
        else:
            module = __import__(pkg)
            version = getattr(module, '__version__', 'Available')
            print(f'{name}: {version}')
    except ImportError:
        print(f'{name}: Not installed')
"

echo ""
echo "Installation Summary:"
echo "===================="
echo "✅ Core dependencies: Installed"
echo "✅ Robotics dependencies: Installed (robomimic, bddl, gym)"
echo "⚠️  Robosuite: May have issues due to egl_probe"
echo "✅ PyTorch: Installed (if selected)"
echo ""
echo "If you need robosuite with GPU rendering, you may need to:"
echo "1. Install system OpenGL libraries: sudo apt install libgl1-mesa-dev libglu1-mesa-dev"
echo "2. Try alternative robosuite installation methods"
echo ""
echo "Installation complete!" 