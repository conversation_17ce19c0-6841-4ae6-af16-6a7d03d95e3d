#!/usr/bin/env python3
"""
Simple script to run DC model rollout evaluation.
Usage: python run_dc_rollout.py [--model_path MODEL_PATH] [--episodes NUM_EPISODES] [--device DEVICE]
"""

import argparse
import os
import sys
from pathlib import Path

def main():
    parser = argparse.ArgumentParser(description="Run DC model rollout evaluation")
    parser.add_argument(
        "--model_path",
        type=str,
        default="/home/<USER>/work/MaIL/MaIL/logs/libero_goal/multi_task_diffusion_transformer/sweeps/goal_ddpm_transformer_encdec/2025-07-22/21-53-31/eval_best_ddpm.pth",
        help="Path to the model checkpoint file"
    )
    parser.add_argument(
        "--episodes", 
        type=int, 
        default=50,
        help="Number of episodes per task (default: 50)"
    )
    parser.add_argument(
        "--device", 
        type=str, 
        default="cuda",
        help="Device to use (default: cuda)"
    )
    parser.add_argument(
        "--seed", 
        type=int, 
        default=42,
        help="Random seed (default: 42)"
    )
    parser.add_argument(
        "--n_cores", 
        type=int, 
        default=5,
        help="Number of CPU cores to use (default: 5)"
    )
    parser.add_argument(
        "--task_suite", 
        type=str, 
        default="libero_goal",
        help="Task suite to evaluate (default: libero_goal)"
    )
    
    args = parser.parse_args()
    
    # Check if model file exists
    if not os.path.exists(args.model_path):
        print(f"❌ Error: Model file not found: {args.model_path}")
        print("Please check the path and try again.")
        return 1
    
    print("🚀 Starting Original Model Rollout Evaluation")
    print(f"📁 Model path: {args.model_path}")
    print(f"🎮 Episodes per task: {args.episodes}")
    print(f"🖥️  Device: {args.device}")
    print(f"🎲 Seed: {args.seed}")
    print(f"💻 CPU cores: {args.n_cores}")
    print(f"📋 Task suite: {args.task_suite}")
    print("=" * 50)
    
    # Update the rollout script with the model path
    rollout_script_path = Path(__file__).parent / "rollout_dc_model.py"
    
    # Read the current script
    with open(rollout_script_path, 'r') as f:
        content = f.read()
    
    # Replace the hardcoded model path with the provided one
    old_path = "/home/<USER>/work/MaIL/logs/dc_training/2025-07-25/00-31-44/checkpoints/dc_training/libero_goal_20250725_003154/best_dc_model.pth"
    new_content = content.replace(old_path, args.model_path)
    
    # Write back the updated script
    with open(rollout_script_path, 'w') as f:
        f.write(new_content)
    
    # Update the config file with the provided parameters
    config_path = Path(__file__).parent / "config" / "dc_rollout.yaml"
    
    # Read the current config
    with open(config_path, 'r') as f:
        config_content = f.read()
    
    # Update parameters
    config_content = config_content.replace("seed: 42", f"seed: {args.seed}")
    config_content = config_content.replace("n_cores: 5", f"n_cores: {args.n_cores}")
    config_content = config_content.replace("device: 'cuda'", f"device: '{args.device}'")
    config_content = config_content.replace("task_suite: libero_goal", f"task_suite: {args.task_suite}")
    config_content = config_content.replace("num_episode: 50", f"num_episode: {args.episodes}")
    
    # Write back the updated config
    with open(config_path, 'w') as f:
        f.write(config_content)
    
    print("✅ Configuration updated successfully")
    print("🔄 Running rollout evaluation...")
    
    # Run the rollout script
    import subprocess
    try:
        result = subprocess.run([
            sys.executable, 
            str(rollout_script_path)
        ], cwd=Path(__file__).parent, check=True)
        
        print("✅ Rollout evaluation completed successfully!")
        return 0
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Rollout evaluation failed with exit code: {e.returncode}")
        return e.returncode
    except Exception as e:
        print(f"❌ Error running rollout evaluation: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
