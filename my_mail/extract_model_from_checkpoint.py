#!/usr/bin/env python3
"""
Extract model weights from training checkpoint for rollout evaluation.
"""
import torch
import argparse
from pathlib import Path

def extract_model_weights(checkpoint_path, output_path):
    """
    Extract model weights from checkpoint file.
    
    Args:
        checkpoint_path: Path to checkpoint file
        output_path: Path to save extracted model weights
    """
    print(f"Loading checkpoint from: {checkpoint_path}")
    
    # Load checkpoint
    checkpoint = torch.load(checkpoint_path, map_location='cpu')
    
    # Extract model state dict
    if 'model_state_dict' in checkpoint:
        model_state_dict = checkpoint['model_state_dict']
        print(f"✅ Found model state dict with {len(model_state_dict)} parameters")
    else:
        print("❌ No model_state_dict found in checkpoint")
        return False
    
    # Save model weights
    torch.save(model_state_dict, output_path)
    print(f"💾 Model weights saved to: {output_path}")
    
    # Print checkpoint info
    if 'epoch' in checkpoint:
        print(f"📊 Checkpoint info:")
        print(f"   Epoch: {checkpoint['epoch']}")
        print(f"   Loss: {checkpoint.get('loss', 'N/A')}")
        print(f"   Step: {checkpoint.get('step', 'N/A')}")
    
    return True

def main():
    parser = argparse.ArgumentParser(description="Extract model weights from checkpoint")
    parser.add_argument("--checkpoint", type=str, required=True,
                        help="Path to checkpoint file")
    parser.add_argument("--output", type=str, default=None,
                        help="Output path for model weights (default: model_state_dict.pth)")
    
    args = parser.parse_args()
    
    checkpoint_path = Path(args.checkpoint)
    if not checkpoint_path.exists():
        print(f"❌ Checkpoint file not found: {checkpoint_path}")
        return 1
    
    # Default output path
    if args.output is None:
        output_path = checkpoint_path.parent / "model_state_dict.pth"
    else:
        output_path = Path(args.output)
    
    # Extract weights
    success = extract_model_weights(checkpoint_path, output_path)
    
    if success:
        print(f"\n🎉 Success! You can now use the extracted weights for rollout:")
        print(f"python standalone_rollout.py --weights {output_path} [other options]")
        return 0
    else:
        return 1

if __name__ == "__main__":
    exit(main())