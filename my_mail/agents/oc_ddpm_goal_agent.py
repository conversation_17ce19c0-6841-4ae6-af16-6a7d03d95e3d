import os
import torch
import einops
import torch.nn as nn
import numpy as np
from tqdm import tqdm
from typing import Optional
from collections import deque

from omegaconf import DictConfig
import hydra
import logging
import wandb
# from sklearn.neighbors import KernelDensity
# import loralib as lora
import pdb
from agents.base_agent import BaseAgent
from agents.models.oc_ddpm.ema import ExponentialMovingAverage


# A logger for this file
log = logging.getLogger(__name__)


class DiffusionPolicy(nn.Module):
    def __init__(self, model: DictConfig, obs_encoder: DictConfig,
                 visual_input: bool = True, device: str = "cpu"):
        super(DiffusionPolicy, self).__init__()

        self.visual_input = visual_input

        self.obs_encoder = hydra.utils.instantiate(obs_encoder).to(device)
        # self.goal_encoder = hydra.utils.instantiate(goal_encoder).to(device)

        self.model = hydra.utils.instantiate(model).to(device)

        self.linear = nn.Linear(512, 256)

    def forward(self, inputs, goal, action=None, if_train=False, if_return_obs=False):
        # encode state and visual inputs
        # the encoder should be shared by all the baselines

        if self.visual_input:
            agentview_image, in_hand_image, goal_imgs = inputs

           

            B, T, C, H, W = agentview_image.size()

            agentview_image = agentview_image.view(B * T, C, H, W)
            in_hand_image = in_hand_image.view(B * T, C, H, W)
            # state = state.view(B * T, -1)

            # bp_imgs = einops.rearrange(bp_imgs, "B T C H W -> (B T) C H W")
            # inhand_imgs = einops.rearrange(inhand_imgs, "B T C H W -> (B T) C H W")
            # _, T1, C1, H1, W1 = goal_imgs.size()
            # goal_imgs = goal_imgs.view(B * T1, C1, H1, W1)
            #
            # goals_dict = {"goal_rgb": goal_imgs}
            # goal_emb = self.goal_encoder(goals_dict)
            
            goal_emb = self.linear(goal_imgs)#变化从[128,1,512]到[128,1,256]
           

            obs_dict = {"agentview_rgb": agentview_image,
                        "eye_in_hand_rgb": in_hand_image,}
                        # "robot_ee_pos": state}

            obs = self.obs_encoder(obs_dict)
            obs = obs.view(B, T, -1)#obs.shape=torch.Size([256, 5, 256])
            

            # goal_emb = goal_emb.view(B, T1, -1)

            obs = torch.cat((goal_emb, obs), dim=1)#这边应该是直接将task_embedding和obs拼接起来一起输入,obs.shape=torch.Size([256, 6, 256])

          

        else:
            obs = self.obs_encoder(inputs)

        if if_train:
            return self.model.loss(action, obs, goal)#这里传送给diffusion,x=action

        # make prediction
        pred = self.model(obs, goal)

        if if_return_obs:
            return pred, obs

        return pred

    def get_params(self):
        return self.parameters()




class DiffusionAgent(BaseAgent):

    def __init__(
            self,
            model: DictConfig,
            optimization: DictConfig,
            trainset: DictConfig,
            valset: DictConfig,
            train_batch_size,
            val_batch_size,
            num_workers,
            device: str,
            epoch: int,
            scale_data,
            use_ema: bool,
            discount: int,
            decay: float,
            update_ema_every_n_steps: int,
            goal_window_size: int,
            window_size: int,
            obs_seq_len: int,
            action_seq_size: int,
            pred_last_action_only: bool = False,
            diffusion_kde: bool = False,
            diffusion_kde_samples: int = 100,
            goal_conditioned: bool = False,
            eval_every_n_epochs: int = 50
    ):
        super().__init__(model, trainset=trainset, valset=valset, train_batch_size=train_batch_size,
                         val_batch_size=val_batch_size, num_workers=num_workers, device=device,
                         epoch=epoch, scale_data=scale_data, eval_every_n_epochs=eval_every_n_epochs)

        # Define the bounds for the sampler class
        self.model.model.min_action = torch.from_numpy(self.scaler.y_bounds[0, :]).to(self.device)
        self.model.model.max_action = torch.from_numpy(self.scaler.y_bounds[1, :]).to(self.device)

        # lora.mark_only_lora_as_trainable(self.model)

        self.eval_model_name = "eval_best_ddpm.pth"
        self.last_model_name = "last_ddpm.pth"

        self.optimizer = hydra.utils.instantiate(
            optimization, params=self.model.parameters()
        )

        self.steps = 0

        self.ema_helper = ExponentialMovingAverage(self.model.parameters(), decay, self.device)
        self.use_ema = use_ema
        self.discount = discount
        self.decay = decay
        self.update_ema_every_n_steps = update_ema_every_n_steps
        # here all the parameters required for the GPT variant
        self.goal_window_size = goal_window_size
        self.window_size = window_size
        self.pred_last_action_only = pred_last_action_only

        self.goal_condition = goal_conditioned

        # if we use DiffusionGPT we need an action context
        if not self.pred_last_action_only:
            self.action_context = deque(maxlen=self.window_size - 1)
            self.que_actions = True
        else:
            self.que_actions = False

        self.diffusion_kde = diffusion_kde
        self.diffusion_kde_samples = diffusion_kde_samples

        self.obs_seq_len = obs_seq_len
        self.action_seq_size = action_seq_size
        self.action_counter = self.action_seq_size

        self.bp_image_context = deque(maxlen=self.obs_seq_len)
        self.inhand_image_context = deque(maxlen=self.obs_seq_len)
        self.des_robot_pos_context = deque(maxlen=self.window_size)

        self.obs_context = deque(maxlen=self.obs_seq_len)
        self.goal_context = deque(maxlen=self.goal_window_size)

    def train_agent(self):

        best_test_mse = 1e10

        for num_epoch in tqdm(range(self.epoch)):

            # run a test batch every n steps
            if not (num_epoch+1) % self.eval_every_n_epochs:

                test_mse = []
                for data in self.test_dataloader:

                    if self.goal_condition:
                        state, action, mask, goal = data
                        mean_mse = self.evaluate(state, action, goal)
                    else:
                        state, action, mask = data
                        mean_mse = self.evaluate(state, action)

                    test_mse.append(mean_mse)
                avrg_test_mse = sum(test_mse) / len(test_mse)

                log.info("Epoch {}: Mean test mse is {}".format(num_epoch, avrg_test_mse))
                if avrg_test_mse < best_test_mse:
                    best_test_mse = avrg_test_mse
                    self.store_model_weights(self.working_dir, sv_name=self.eval_model_name)

                    wandb.log(
                        {
                            "best_model_epochs": num_epoch
                        }
                    )

                    log.info('New best test loss. Stored weights have been updated!')

            train_loss = []
            for data in self.train_dataloader:

                if self.goal_condition:
                    state, action, mask, goal = data
                    batch_loss, _ = self.train_step(state, action, goal)
                else:
                    state, action, mask = data
                    batch_loss, _ = self.train_step(state, action)

                train_loss.append(batch_loss)

                wandb.log(
                    {
                        "loss": batch_loss,
                    }
                )

        self.store_model_weights(self.working_dir, sv_name=self.last_model_name)

        log.info("Training done!")

    def _display_dispersive_loss_status(self):
        """Display Dispersive Loss configuration and status"""
        try:
            # Check if the model has dispersive loss functionality
            if hasattr(self.model, 'model') and hasattr(self.model.model, 'get_disp_loss_info'):
                disp_info = self.model.model.get_disp_loss_info()
                
                print("=" * 60)
                print("🎨 Dispersive Loss 配置状态")
                print("=" * 60)
                print(f"📊 状态: {disp_info['status'].upper()}")
                
                if disp_info['use_disp_loss']:
                    print(f"⚖️  权重: {disp_info['disp_loss_weight']}")
                    print(f"🎯 作用: 鼓励表示分散，提高生成多样性")
                    
                    # Check if model supports feature extraction
                    if hasattr(self.model.model, 'model') and hasattr(self.model.model.model, 'forward_with_features'):
                        print(f"🔍 特征提取: 支持中间层特征 (推荐)")
                        # Try to get feature extraction configuration
                        if hasattr(self.model.model.model, 'feature_extraction_mode'):
                            mode = self.model.model.model.feature_extraction_mode
                            print(f"📋 提取模式: {mode}")
                    else:
                        print(f"🔍 特征提取: 使用模型输出 (基础模式)")
                        print(f"💡 提示: 使用 oc_ddpm_with_features 可获得更好效果")
                        
                    print(f"📈 预期效果: 更稳定训练、更多样化生成、避免模式坍塌")
                else:
                    print(f"❌ Dispersive Loss 未启用")
                    
                print("=" * 60)
                
            else:
                print("ℹ️  当前模型不支持 Dispersive Loss")
                
        except Exception as e:
            print(f"⚠️  获取 Dispersive Loss 状态时出错: {e}")

    def train_vision_agent(self):
        
        print(f"🚀 开始训练，总共 {self.epoch} 个epoch")
        print(f"📊 数据集大小: {len(self.train_dataloader)} 批次")



        
        # Display Dispersive Loss status
        self._display_dispersive_loss_status()
        
        # 添加epoch循环和进度条
        for num_epoch in tqdm(range(self.epoch), desc="🎯 训练进度", unit="epoch"):
            
            epoch_losses = []
            
            # 为每个epoch内的batch添加进度条
            batch_iterator = tqdm(
                self.train_dataloader, 
                desc=f"Epoch {num_epoch+1}/{self.epoch}", 
                leave=False,
                unit="batch"
            )
            
            for batch_idx, data in enumerate(batch_iterator):

                bp_imgs, inhand_imgs, action, goal_imgs, episode_indices = data #这里面的goal_imgs实际上是text_embedding
                #print(f'data consists of :goal_imgs.shape={goal_imgs.shape}, bp_imgs.shape={bp_imgs.shape}, inhand_imgs.shape={inhand_imgs.shape}, action.shape={action.shape}')
                '''goal_imgs.shape=torch.Size([128, 1, 512]), bp_imgs.shape=torch.Size([128, 9, 3, 128, 128]), inhand_imgs.shape=torch.Size([128, 9, 3, 128, 128]), action.shape=torch.Size([128, 9, 7]) '''
                #pdb.set_trace()

                bp_imgs = bp_imgs.to(self.device)
                inhand_imgs = inhand_imgs.to(self.device)
                goal_imgs = goal_imgs.to(self.device)

                # obs = self.scaler.scale_input(obs)
                action = self.scaler.scale_output(action)

                action = action[:, self.obs_seq_len - 1:, :].contiguous()

                # obs = obs[:, :self.obs_seq_len].contiguous()
                bp_imgs = bp_imgs[:, :self.obs_seq_len].contiguous()
                inhand_imgs = inhand_imgs[:, :self.obs_seq_len].contiguous()

                state = (bp_imgs, inhand_imgs, goal_imgs)

                batch_loss, loss_info = self.train_step(state, action)
                epoch_losses.append(batch_loss.item())

                # 更新批次进度条显示当前loss和dispersive loss状态
                postfix_dict = {
                    'Loss': f'{batch_loss.item():.4f}',
                    'Avg_Loss': f'{np.mean(epoch_losses):.4f}'
                }
                
                # Add dispersive loss info to progress bar if enabled
                if loss_info.get('disp_enabled', False):
                    disp_val = loss_info.get('disp_loss_value', 0)
                    postfix_dict['Disp'] = f"{disp_val:.3f}"
                    postfix_dict['λ'] = f"{loss_info['disp_weight']}"
                
                batch_iterator.set_postfix(postfix_dict)
                
                # wandb logging
                log_dict = {
                    "train_loss": batch_loss.item(),
                    "epoch": num_epoch,
                    "batch": batch_idx
                }
                
                # Add dispersive loss info to wandb logs
                if loss_info.get('disp_enabled', False):
                    log_dict["dispersive_loss_enabled"] = True
                    log_dict["dispersive_loss_weight"] = loss_info['disp_weight']
                    log_dict["dispersive_loss_value"] = loss_info.get('disp_loss_value', 0)
                    log_dict["main_loss"] = loss_info.get('main_loss', batch_loss.item())
                else:
                    log_dict["dispersive_loss_enabled"] = False
                
                wandb.log(log_dict)

            # epoch结束后的统计
            avg_epoch_loss = np.mean(epoch_losses)
            print(f"📈 Epoch {num_epoch+1}/{self.epoch} 完成 - 平均损失: {avg_epoch_loss:.4f}")
            
            # 记录epoch级别的指标
            wandb.log({
                "epoch_avg_loss": avg_epoch_loss,
                "epoch": num_epoch
            })

        print("✅ 训练完成!")
        
        # 保存模型
        if hasattr(self, 'working_dir') and hasattr(self, 'last_model_name'):
            self.store_model_weights(self.working_dir, sv_name=self.last_model_name)
            print(f"💾 模型已保存到: {self.working_dir}/{self.last_model_name}")

    def train_step(self, state: torch.Tensor, action: torch.Tensor, goal: Optional[torch.Tensor] = None):

        # state = state.to(self.device).to(torch.float32)  # [B, V]
        # action = action.to(self.device).to(torch.float32)  # [B, D]
        # scale data if necessarry, otherwise the scaler will return unchanged values
        self.model.train()

        # state = self.scaler.scale_input(state)
        # action = self.scaler.scale_output(action)

        if goal is not None:
            goal = self.scaler.scale_input(goal)

        # action = action[:, self.obs_seq_len-1:, :]
        # state = state[:, :self.obs_seq_len, :]

        # Compute the loss.
        loss = self.model(state, goal, action=action, if_train=True)#这里调用的是diffusionpolicy而不是diffusion transformer encdec
        
        # Extract dispersive loss information if available
        loss_info = {'total_loss': loss.item()}
        if hasattr(self.model, 'model') and hasattr(self.model.model, 'get_disp_loss_info'):
            disp_info = self.model.model.get_disp_loss_info()
            if disp_info['use_disp_loss']:
                loss_info['disp_enabled'] = True
                loss_info['disp_weight'] = disp_info['disp_loss_weight']
                loss_info['disp_loss_value'] = disp_info['last_disp_loss']
                # Calculate main loss (approximate)
                loss_info['main_loss'] = loss.item() - (disp_info['disp_loss_weight'] * disp_info['last_disp_loss'])
            else:
                loss_info['disp_enabled'] = False
        
        # Before the backward pass, zero all the network gradients
        self.optimizer.zero_grad()
        # Backward pass: compute gradient of the loss with respect to parameters
        loss.backward()
        # Calling the step function to update the parameters
        self.optimizer.step()

        self.steps += 1

        # update the ema model
        if self.steps % self.update_ema_every_n_steps == 0:
            self.ema_helper.update(self.model.parameters())
        
        # Return both loss tensor and info dict
        return loss, loss_info

    @torch.no_grad()
    def evaluate(
            self, state: torch.tensor, action: torch.tensor, goal: Optional[torch.Tensor] = None
    ) -> float:

        # scale data if necessarry, otherwise the scaler will return unchanged values
        state = self.scaler.scale_input(state)
        action = self.scaler.scale_output(action)

        action = action[:, self.obs_seq_len - 1:, :]
        state = state[:, :self.obs_seq_len, :]

        if goal is not None:
            goal = self.scaler.scale_input(goal)

        total_mse = 0.0
        # use the EMA model variant
        if self.use_ema:
            self.ema_helper.store(self.model.parameters())
            self.ema_helper.copy_to(self.model.parameters())

        self.model.eval()

        # Compute the loss.
        loss = self.model.loss(action, state, goal)

        # model_pred = self.model(state, goal)
        # mse = nn.functional.mse_loss(model_pred, action, reduction="none")

        total_mse += loss.mean().item()

        # restore the previous model parameters
        if self.use_ema:
            self.ema_helper.restore(self.model.parameters())
        return total_mse

    def reset(self):
        """ Resets the context of the model."""
        self.obs_context.clear()
        self.action_counter = self.action_seq_size

        self.bp_image_context.clear()
        self.inhand_image_context.clear()
        self.des_robot_pos_context.clear()

    @torch.no_grad()
    def predict(self, state: torch.Tensor, goal: Optional[torch.Tensor] = None, extra_args=None, if_vision=True) -> torch.Tensor:
        # scale data if necessarry, otherwise the scaler will return unchanged values

        if if_vision:
            bp_image, inhand_image, goal_image = state

            bp_image = torch.from_numpy(bp_image).to(self.device).float().permute(2, 0, 1).unsqueeze(0) / 255.
            inhand_image = torch.from_numpy(inhand_image).to(self.device).float().permute(2, 0, 1).unsqueeze(0) / 255.

            goal_image = goal_image.to(self.device).unsqueeze(0)

            # goal_image = torch.from_numpy(goal_image).to(self.device).float().permute(0, 3, 1, 2).unsqueeze(0) / 255.
            # des_robot_pos = torch.from_numpy(des_robot_pos).to(self.device).float().unsqueeze(0)

            # des_robot_pos = self.scaler.scale_input(des_robot_pos)

            self.bp_image_context.append(bp_image)
            self.inhand_image_context.append(inhand_image)
            # self.des_robot_pos_context.append(des_robot_pos)

            bp_image_seq = torch.stack(tuple(self.bp_image_context), dim=1)
            inhand_image_seq = torch.stack(tuple(self.inhand_image_context), dim=1)
            # des_robot_pos_seq = torch.stack(tuple(self.des_robot_pos_context), dim=1)

            input_state = (bp_image_seq, inhand_image_seq, goal_image)
        else:
            obs = torch.from_numpy(state).float().to(self.device).unsqueeze(0)
            obs = self.scaler.scale_input(obs)
            self.obs_context.append(obs)
            input_state = torch.stack(tuple(self.obs_context), dim=1)  # type: ignore

        if self.action_counter == self.action_seq_size:
            self.action_counter = 0

            if self.use_ema:
                self.ema_helper.store(self.model.parameters())
                self.ema_helper.copy_to(self.model.parameters())

            self.model.eval()

            model_pred = self.model(input_state, goal)

            # restore the previous model parameters
            if self.use_ema:
                self.ema_helper.restore(self.model.parameters())
            model_pred = self.scaler.inverse_scale_output(model_pred)

            self.curr_action_seq = model_pred

        next_action = self.curr_action_seq[:, self.action_counter, :]
        self.action_counter += 1
        return next_action.detach().cpu().numpy()

    def random_sample(self, state: torch.Tensor, goal: Optional[torch.Tensor] = None, extra_args=None, if_vision=True):
        # scale data if necessarry, otherwise the scaler will return unchanged values

        if if_vision:
            bp_imgs, inhand_imgs = state

            bp_imgs = bp_imgs[:, :self.obs_seq_len].contiguous()
            inhand_imgs = inhand_imgs[:, :self.obs_seq_len].contiguous()

            input_state = (bp_imgs, inhand_imgs)
        else:
            obs = torch.from_numpy(state).float().to(self.device).unsqueeze(0)
            obs = self.scaler.scale_input(obs)
            self.obs_context.append(obs)
            input_state = torch.stack(tuple(self.obs_context), dim=1)  # type: ignore

        # self.model.eval()
        # do default model evaluation
        model_pred, obs_embedding = self.model(input_state, goal, if_return_obs=True)

        return model_pred, obs_embedding
    @torch.no_grad()
    def load_pretrained_model(self, weights_path: str, sv_name=None, **kwargs) -> None:
        """
        Method to load a pretrained model weights inside self.model
        """
        # self.model.load_state_dict(torch.load(os.path.join(weights_path, "model_state_dict.pth")))
        self.model.load_state_dict(torch.load(os.path.join(weights_path, sv_name)), strict=False)
        self.ema_helper = ExponentialMovingAverage(self.model.parameters(), self.decay, self.device)
        log.info('Loaded pre-trained model parameters')

    @torch.no_grad()
    def store_model_weights(self, store_path: str, sv_name=None) -> None:
        """
        Store the model weights inside the store path as model_weights.pth
        """
        if self.use_ema:
            self.ema_helper.store(self.model.parameters())
            self.ema_helper.copy_to(self.model.parameters())
        # torch.save(self.model.state_dict(), os.path.join(store_path, "model_state_dict.pth"))
        torch.save(self.model.state_dict(), os.path.join(store_path, sv_name))
        if self.use_ema:
            self.ema_helper.restore(self.model.parameters())
        torch.save(self.model.state_dict(), os.path.join(store_path, "non_ema_model_state_dict.pth"))
