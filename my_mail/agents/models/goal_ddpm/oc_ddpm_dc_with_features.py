"""
DC DiffusionEncDec model with feature extraction capability for Dispersive Loss.
Extends the standard DC model to support intermediate feature collection.
"""

import hydra
from omegaconf import DictConfig
import torch
import torch.nn as nn
import einops
from typing import Optional, List, Tuple

from .oc_ddpm_dc import DiffusionEncDec


class DiffusionEncDecWithFeatures_DC(DiffusionEncDec):
    """
    DC-enhanced DiffusionEncDec with feature extraction capabilities.
    Extends the base DC model to support collecting intermediate features
    from specified decoder layers for Dispersive Loss computation.
    """
    
    def __init__(
        self,
        encoder: DictConfig,
        decoder: DictConfig,
        state_dim: int,
        action_dim: int,
        device: str,
        embed_dim: int,
        embed_pdrob: float,
        goal_seq_len: int,
        obs_seq_len: int,
        action_seq_len: int,
        goal_drop: float = 0.1,
        goal_conditioned: bool = False,
        linear_output: bool = False,
        # DC parameters
        n_dc_layers: int = 6,
        n_dc_tokens: int = 7,
        use_dc_t: bool = True,
        use_dc: bool = True,
        n_decoder_dc_layers: int = 6,
        n_decoder_dc_tokens: int = 7,
        use_decoder_dc_t: bool = True,
        use_decoder_dc: bool = True,
        # Feature extraction parameters for Dispersive Loss
        decoder_feature_layers: List[int] = None,
        feature_extraction_mode: str = "decoder",
        # NEW: encoder feature layers
        encoder_feature_layers: List[int] = None,
    ):
        super().__init__(
            encoder=encoder,
            decoder=decoder,
            state_dim=state_dim,
            action_dim=action_dim,
            device=device,
            embed_dim=embed_dim,
            embed_pdrob=embed_pdrob,
            goal_seq_len=goal_seq_len,
            obs_seq_len=obs_seq_len,
            action_seq_len=action_seq_len,
            goal_drop=goal_drop,
            goal_conditioned=goal_conditioned,
            linear_output=linear_output,
            n_dc_layers=n_dc_layers,
            n_dc_tokens=n_dc_tokens,
            use_dc_t=use_dc_t,
            use_dc=use_dc,
            n_decoder_dc_layers=n_decoder_dc_layers,
            n_decoder_dc_tokens=n_decoder_dc_tokens,
            use_decoder_dc_t=use_decoder_dc_t,
            use_decoder_dc=use_decoder_dc,
        )
        
        # Feature extraction configuration
        self.decoder_feature_layers = decoder_feature_layers or [-2, -1]  # Default: last two layers
        self.feature_extraction_mode = feature_extraction_mode
        # NEW: encoder feature layers (default: last layer -> late)
        self.encoder_feature_layers = encoder_feature_layers or [-1]
        
        # Storage for collected features
        self.collected_features = []
        
        print(f"🎨 DiffusionEncDecWithFeatures_DC initialized:")
        print(f"  - Decoder feature layers: {self.decoder_feature_layers}")
        print(f"  - Feature extraction mode: {self.feature_extraction_mode}")
        print(f"  - Encoder feature layers: {self.encoder_feature_layers}")
        print(f"  - DC configuration: {n_dc_tokens} tokens, {n_dc_layers} encoder layers, {n_decoder_dc_layers} decoder layers")
    
    def _should_collect_decoder_feature(self, layer_idx: int) -> bool:
        """Check if we should collect features from this decoder layer"""
        if self.feature_extraction_mode not in ['decoder', 'both']:
            return False
            
        # Support both positive and negative indexing
        return (layer_idx in self.decoder_feature_layers or 
                (layer_idx - len(self.decoder.blocks)) in self.decoder_feature_layers)

    # NEW: support encoder feature selection
    def _should_collect_encoder_feature(self, layer_idx: int) -> bool:
        if self.feature_extraction_mode not in ['encoder', 'both']:
            return False
        total = len(self.encoder.blocks)
        # Support positive indices and negative indexing semantics
        return (layer_idx in self.encoder_feature_layers or 
                (layer_idx - total) in self.encoder_feature_layers)

    # NEW: forward through encoder while collecting specified layer features
    def _forward_encoder_with_features(self, input_seq):
        x = input_seq
        for i, block in enumerate(self.encoder.blocks):
            x = block(x)
            if self._should_collect_encoder_feature(i):
                self.collected_features.append({
                    'layer_idx': i,
                    'layer_type': 'encoder',
                    'output': x.detach().clone()
                })
        return self.encoder.ln(x)

    def forward_with_features(
        self, 
        actions, 
        time, 
        states, 
        goals, 
        uncond: Optional[bool] = False,
        keep_last_actions: Optional[bool] = False
    ) -> Tuple[torch.Tensor, List[torch.Tensor]]:
        """
        Forward pass that returns both output and intermediate features.
        
        This method replicates the forward logic but collects features from
        specified decoder layers for Dispersive Loss computation.
        
        Returns:
            Tuple of (output_tensor, list_of_features)
        """
        # Clear previous features
        self.collected_features = []
        
        # Replicate the forward pass logic but collect features along the way
        if len(states.size()) != 3:
            states = states.unsqueeze(0)
        if len(actions.size()) != 3:
            actions = actions.unsqueeze(0)

        b, t, dim = states.size()
        assert t <= self.block_size, "Cannot forward, model block size is exhausted."
        
        # Get time embedding
        times = einops.rearrange(time, 'b -> b 1')
        emb_t = self.time_emb(times)

        if self.goal_conditioned:
            if self.training:
                goals = self.mask_cond(goals)
            if uncond:
                goals = torch.zeros_like(goals).to(self.device)
            goal_embed = self.tok_emb(goals)

        # Embed inputs
        state_embed = self.tok_emb(states)
        action_embed = self.action_emb(actions)

        # Position embeddings
        position_embeddings = self.pos_emb[:, :(t + self.goal_seq_len + self.action_seq_len - 1), :]
        
        if self.goal_conditioned:
            goal_x = self.drop(goal_embed + position_embeddings[:, :self.goal_seq_len, :])

        state_x = self.drop(state_embed + position_embeddings[:, self.goal_seq_len:(self.goal_seq_len + t), :])
        action_x = self.drop(action_embed + position_embeddings[:, (self.goal_seq_len + t - 1):, :])

        input_seq = torch.cat([emb_t, state_x], dim=1)

        # Encode with feature collection if needed
        if self.feature_extraction_mode in ["encoder", "both"]:
            encoder_output = self._forward_encoder_with_features(input_seq)
        else:
            encoder_output = self.encoder(input_seq)
        
        # Decode with feature collection if requested
        if self.feature_extraction_mode in ["decoder", "both"]:
            decoder_output = self._forward_decoder_with_features(action_x, encoder_output)
        else:
            decoder_output = self.decoder(action_x, encoder_output)
        
        # Final prediction
        pred_actions = self.action_pred(decoder_output)
        
        # Extract just the feature tensors from collected features
        features = [f['output'] for f in self.collected_features]
        
        return pred_actions, features
    
    def _forward_decoder_with_features(self, action_x, encoder_output):
        """Forward through decoder while collecting specified layer features"""
        for i, block in enumerate(self.decoder.blocks):
            action_x = block(action_x, cond=encoder_output)
            
            # Collect features from specified layers
            if self._should_collect_decoder_feature(i):
                self.collected_features.append({
                    'layer_idx': i,
                    'layer_type': 'decoder',
                    'output': action_x.detach().clone()
                })
        
        return self.decoder.ln(action_x)

    # NEW: support return_features flag to integrate with agent calling convention
    def forward(
        self,
        actions,
        time,
        states,
        goals,
        uncond: Optional[bool] = False,
        keep_last_actions: Optional[bool] = False,
        return_features: bool = False,
    ):
        if return_features:
            return self.forward_with_features(actions, time, states, goals, uncond, keep_last_actions)
        return super().forward(actions, time, states, goals, uncond, keep_last_actions)

    # NEW: provide a dispersive loss aligned with d2ppo (InfoNCE-L2 with temperature)
    def disp_loss(self, features, temperature: float = 0.5) -> torch.Tensor:
        """
        Compute Dispersive Loss (InfoNCE-L2 style) on a list/tensor of features.
        If a list is given, use the last collected feature (typically late layer encoder output).
        """
        if isinstance(features, (list, tuple)):
            if len(features) == 0:
                return torch.tensor(0.0, device=self.device)
            z = features[-1]
        else:
            z = features
        
        if not isinstance(z, torch.Tensor):
            return torch.tensor(0.0, device=self.device)
        
        if z.size(0) <= 1:
            return torch.tensor(0.0, device=z.device)
        
        # Flatten per-sample
        z = z.reshape(z.shape[0], -1)
        # Pairwise L2 distances (full matrix)
        diff = z.unsqueeze(1) - z.unsqueeze(0)
        distances = (diff ** 2).sum(dim=-1)
        # InfoNCE-like objective: log E[exp(-D/τ)]
        exp_neg = torch.exp(-distances / temperature)
        return torch.log(exp_neg.mean())
    
    def clear_features_for_multiprocess(self):
        """
        清理collected_features以避免multiprocessing序列化问题
        """
        self.collected_features = []


# Test function to verify the DC model with features
def test_dc_model_with_features():
    """Test the DC model with feature extraction capabilities"""
    print("Testing DiffusionEncDecWithFeatures_DC...")
    
    try:
        from omegaconf import DictConfig
        
        # Mock encoder and decoder configs
        encoder_config = DictConfig({
            '_target_': 'agents.models.goal_ddpm.oc_ddpm.TransformerEncoder',
            'embed_dim': 128,
            'n_heads': 4,
            'n_layers': 4,
            'attn_pdrop': 0.1,
            'resid_pdrop': 0.1,
            'bias': False,
            'block_size': 14,
        })
        
        decoder_config = DictConfig({
            '_target_': 'agents.models.goal_ddpm.oc_ddpm.TransformerDecoder',
            'embed_dim': 128,
            'cross_embed': 128,
            'n_heads': 4,
            'n_layers': 4,
            'attn_pdrop': 0.1,
            'resid_pdrop': 0.1,
            'bias': False,
            'block_size': 14,
        })
        
        # Create model
        model = DiffusionEncDecWithFeatures_DC(
            encoder=encoder_config,
            decoder=decoder_config,
            state_dim=256,
            action_dim=7,
            device='cpu',
            embed_dim=128,
            embed_pdrob=0.0,
            goal_seq_len=1,
            obs_seq_len=5,
            action_seq_len=8,
            decoder_feature_layers=[-2, -1],
            feature_extraction_mode="decoder",
            encoder_feature_layers=[-1]
        )
        
        # Test forward_with_features
        batch_size = 4
        actions = torch.randn(batch_size, 8, 7)
        time = torch.randint(0, 100, (batch_size,))
        states = torch.randn(batch_size, 6, 256)  # goal + obs
        goals = None
        
        pred_actions, features = model.forward_with_features(actions, time, states, goals)
        
        print(f"✅ Prediction shape: {pred_actions.shape}")
        print(f"✅ Number of collected features: {len(features)}")
        for i, feature in enumerate(features):
            print(f"   Feature {i}: {feature.shape}")
        
        print("✅ DC model with features test passed!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        raise e


if __name__ == "__main__":
    test_dc_model_with_features()