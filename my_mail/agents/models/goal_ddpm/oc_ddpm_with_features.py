"""
Enhanced version of the diffusion model with support for intermediate feature extraction
for Dispersive Loss implementation. Fixed for multiprocessing compatibility.
"""

import torch
import torch.nn as nn
import functools
from typing import Optional, List, Tuple, Union
from .oc_ddpm import (
    EncoderBlock, DecoderBlock, TransformerEncoder, TransformerDecoder,
    DiffusionEncDec, LayerNorm, SinusoidalPosEmb
)


class TransformerEncoderWithFeatures(TransformerEncoder):
    """
    Enhanced TransformerEncoder that can optionally return intermediate features
    from specified layers for Dispersive Loss computation.
    """
    
    def __init__(
        self,
        embed_dim: int,
        n_heads: int,
        attn_pdrop: float,
        resid_pdrop: float,
        n_layers: int,
        block_size: int,
        bias: bool = False,
        feature_layers: Optional[List[int]] = None,
    ):
        super().__init__(embed_dim, n_heads, attn_pdrop, resid_pdrop, n_layers, block_size, bias)
        
        # Specify which layers to extract features from (default: last layer)
        self.feature_layers = feature_layers if feature_layers is not None else [n_layers - 1]
        self.n_layers = n_layers
        
    def forward(self, x, return_features: bool = False):
        """
        Forward pass with optional feature extraction
        
        Args:
            x: Input tensor
            return_features: Whether to return intermediate features
            
        Returns:
            If return_features=False: output tensor
            If return_features=True: (output tensor, list of intermediate features)
        """
        features = []
        
        for i, layer in enumerate(self.blocks):
            x = layer(x)
            
            # Collect features from specified layers
            if return_features and i in self.feature_layers:
                features.append(x.clone())
        
        x = self.ln(x)
        
        if return_features:
            # Also add the final layer norm output as a feature option
            if self.n_layers in self.feature_layers:
                features.append(x.clone())
            return x, features
        else:
            return x


class TransformerDecoderWithFeatures(TransformerDecoder):
    """
    Enhanced TransformerDecoder that can optionally return intermediate features
    from specified layers for Dispersive Loss computation.
    """
    
    def __init__(
        self,
        embed_dim: int,
        cross_embed: int,
        n_heads: int,
        attn_pdrop: float,
        resid_pdrop: float,
        n_layers: int,
        block_size: int,
        bias: bool = False,
        feature_layers: Optional[List[int]] = None,
    ):
        super().__init__(embed_dim, cross_embed, n_heads, attn_pdrop, resid_pdrop, 
                        n_layers, block_size, bias)
        
        # Specify which layers to extract features from (default: last layer)
        self.feature_layers = feature_layers if feature_layers is not None else [n_layers - 1]
        self.n_layers = n_layers
        
    def forward(self, x, cond=None, return_features: bool = False):
        """
        Forward pass with optional feature extraction
        
        Args:
            x: Input tensor
            cond: Conditioning tensor
            return_features: Whether to return intermediate features
            
        Returns:
            If return_features=False: output tensor
            If return_features=True: (output tensor, list of intermediate features)
        """
        features = []
        
        for i, layer in enumerate(self.blocks):
            x = layer(x, cond=cond)
            
            # Collect features from specified layers
            if return_features and i in self.feature_layers:
                features.append(x.clone())
        
        x = self.ln(x)
        
        if return_features:
            # Also add the final layer norm output as a feature option
            if self.n_layers in self.feature_layers:
                features.append(x.clone())
            return x, features
        else:
            return x


class DiffusionEncDecWithFeatures(DiffusionEncDec):
    """
    Enhanced DiffusionEncDec that supports intermediate feature extraction
    for Dispersive Loss computation. Fixed for multiprocessing compatibility.
    """
    
    def __init__(
        self,
        encoder,
        decoder,
        state_dim: int,
        action_dim: int,
        device: str,
        goal_conditioned: bool,
        embed_dim: int,
        embed_pdrob: float,
        goal_seq_len: int,
        obs_seq_len: int,
        action_seq_len: int,
        goal_drop: float = 0.1,
        linear_output: bool = False,
        # New parameters for feature extraction
        encoder_feature_layers: Optional[List[int]] = None,
        decoder_feature_layers: Optional[List[int]] = None,
        feature_extraction_mode: str = 'decoder',  # 'encoder', 'decoder', or 'both'
    ):
        # Initialize the base class first
        super().__init__(
            encoder, decoder, state_dim, action_dim, device, goal_conditioned,
            embed_dim, embed_pdrob, goal_seq_len, obs_seq_len, action_seq_len,
            goal_drop, linear_output
        )
        
        # Store feature extraction configuration
        self.encoder_feature_layers = encoder_feature_layers if encoder_feature_layers is not None else [-1]
        self.decoder_feature_layers = decoder_feature_layers if decoder_feature_layers is not None else [-1]
        self.feature_extraction_mode = feature_extraction_mode
        
        # Storage for collected features (no hooks needed!)
        self.collected_features = []
    
    def _should_collect_encoder_feature(self, layer_idx: int) -> bool:
        """Check if we should collect features from this encoder layer"""
        if self.feature_extraction_mode not in ['encoder', 'both']:
            return False
        
        # Support both positive and negative indexing
        return (layer_idx in self.encoder_feature_layers or 
                (layer_idx - len(self.encoder.blocks)) in self.encoder_feature_layers)
    
    def _should_collect_decoder_feature(self, layer_idx: int) -> bool:
        """Check if we should collect features from this decoder layer"""
        if self.feature_extraction_mode not in ['decoder', 'both']:
            return False
            
        # Support both positive and negative indexing
        return (layer_idx in self.decoder_feature_layers or 
                (layer_idx - len(self.decoder.blocks)) in self.decoder_feature_layers)
        
    def forward_with_features(
        self, 
        actions, 
        time, 
        states, 
        goals, 
        uncond: Optional[bool] = False,
        keep_last_actions: Optional[bool] = False
    ) -> Tuple[torch.Tensor, List[torch.Tensor]]:
        """
        Forward pass that returns both output and intermediate features.
        
        This method directly collects intermediate features without using hooks,
        making it safe for multiprocessing environments.
        
        Returns:
            Tuple of (output_tensor, list_of_features)
        """
        # Clear previous features
        self.collected_features = []
        
        # Replicate the forward pass logic but collect features along the way
        if len(states.size()) != 3:
            states = states.unsqueeze(0)
        if len(actions.size()) != 3:
            actions = actions.unsqueeze(0)

        b, t, dim = states.size()
        assert t <= self.block_size, "Cannot forward, model block size is exhausted."
        
        # Get time embedding
        import einops
        times = einops.rearrange(time, 'b -> b 1')
        emb_t = self.time_emb(times)

        if self.goal_conditioned:
            if self.training:
                goals = self.mask_cond(goals)
            if uncond:
                goals = torch.zeros_like(goals).to(self.device)
            goal_embed = self.tok_emb(goals)

        # Embed inputs
        state_embed = self.tok_emb(states)
        action_embed = self.action_emb(actions)

        # Position embeddings
        position_embeddings = self.pos_emb[:, :(t + self.goal_seq_len + self.action_seq_len - 1), :]
        
        if self.goal_conditioned:
            goal_x = self.drop(goal_embed + position_embeddings[:, :self.goal_seq_len, :])

        state_x = self.drop(state_embed + position_embeddings[:, self.goal_seq_len:(self.goal_seq_len + t), :])
        action_x = self.drop(action_embed + position_embeddings[:, (self.goal_seq_len + t - 1):, :])

        input_seq = torch.cat([emb_t, state_x], dim=1)

        # Encode with feature collection
        encoder_output = self._forward_encoder_with_features(input_seq)
        
        # Decode with feature collection  
        decoder_output = self._forward_decoder_with_features(action_x, encoder_output)

        # Final prediction
        pred_actions = self.action_pred(decoder_output)

        # Extract just the feature tensors from collected features
        features = [f['output'] for f in self.collected_features]
        
        return pred_actions, features

    def _forward_encoder_with_features(self, x):
        """Forward through encoder while collecting specified layer features"""
        for i, block in enumerate(self.encoder.blocks):
            x = block(x)
            
            # Collect features from specified layers
            if self._should_collect_encoder_feature(i):
                self.collected_features.append({
                    'layer_idx': i,
                    'layer_type': 'encoder',
                    'output': x.detach().clone()
                })
        
        return self.encoder.ln(x)
    
    def _forward_decoder_with_features(self, action_x, encoder_output):
        """Forward through decoder while collecting specified layer features"""
        for i, block in enumerate(self.decoder.blocks):
            action_x = block(action_x, cond=encoder_output)
            
            # Collect features from specified layers
            if self._should_collect_decoder_feature(i):
                self.collected_features.append({
                    'layer_idx': i,
                    'layer_type': 'decoder',
                    'output': action_x.detach().clone()
                })
        
        return self.decoder.ln(action_x)

    def forward(
        self,
        actions,
        time,
        states,
        goals,
        uncond: Optional[bool] = False,
        keep_last_actions: Optional[bool] = False
    ):
        """Standard forward pass without feature extraction"""
        return super().forward(actions, time, states, goals, uncond, keep_last_actions)
    
    def clear_features_for_multiprocess(self):
        """
        清理collected_features以避免multiprocessing序列化问题
        """
        self.collected_features = []


# Test function to verify pickle compatibility
def test_pickle_compatibility():
    """
    Test that the DiffusionEncDecWithFeatures class can be properly pickled
    and unpickled without issues.
    """
    import pickle
    import torch
    from omegaconf import DictConfig
    
    print("Testing pickle compatibility of DiffusionEncDecWithFeatures...")
    
    try:
        # Create a simple test model
        model = DiffusionEncDecWithFeatures(
            encoder=None,  # Would be configured properly in real use
            decoder=None,  # Would be configured properly in real use
            state_dim=256,
            action_dim=7,
            device='cpu',
            goal_conditioned=False,
            embed_dim=128,
            embed_pdrob=0.0,
            goal_seq_len=10,
            obs_seq_len=9,
            action_seq_len=8,
            goal_drop=0.1,
            linear_output=True,
            encoder_feature_layers=[-1],
            decoder_feature_layers=[-2, -1],
            feature_extraction_mode='decoder'
        )
        
        # Test pickle serialization
        import pickle
        pickled_data = pickle.dumps(model)
        restored_model = pickle.loads(pickled_data)
        
        print("✅ Pickle test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Pickle test failed: {e}")
        return False


if __name__ == "__main__":
    test_pickle_compatibility()