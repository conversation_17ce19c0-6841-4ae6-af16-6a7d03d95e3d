import hydra
from omegaconf import DictConfig, OmegaConf
import torch
import torch.nn as nn
import einops
import math
from typing import Optional
from torch.nn import functional as F
import logging

from .utils import SinusoidalPosEmb

logger = logging.getLogger(__name__)


class LayerNorm(nn.Module):
    """ LayerNorm but with an optional bias. PyTorch doesn't support simply bias=False """

    def __init__(self, ndim, bias):
        super().__init__()
        self.weight = nn.Parameter(torch.ones(ndim))
        self.bias = nn.Parameter(torch.zeros(ndim)) if bias else None

    def forward(self, input):
        return F.layer_norm(input, self.weight.shape, self.weight, self.bias, 1e-5)


class SelfAttention(nn.Module):
    """
    A vanilla multi-head masked self-attention layer with a projection at the end.
    It is possible to use torch.nn.MultiheadAttention here but I am including an
    implementation here to show that there is nothing too scary here.
    """

    def __init__(
            self,
            n_embd: int,
            n_heads: int,
            attn_pdrop: float,
            resid_pdrop: float,
            block_size: int,
    ):
        super().__init__()
        assert n_embd % n_heads == 0
        # key, query, value projections for all heads
        self.key = nn.Linear(n_embd, n_embd)
        self.query = nn.Linear(n_embd, n_embd)
        self.value = nn.Linear(n_embd, n_embd)
        # regularization
        self.attn_drop = nn.Dropout(attn_pdrop)
        self.resid_drop = nn.Dropout(resid_pdrop)
        # output projection
        self.proj = nn.Linear(n_embd, n_embd)
        # causal mask to ensure that attention is only applied to the left in the input sequence
        self.register_buffer(
            "mask",
            torch.tril(torch.ones(block_size, block_size)).view(
                1, 1, block_size, block_size
            ),
        )
        self.n_head = n_heads
        self.block_size = block_size

    def forward(self, x):
        (
            B,
            T,
            C,
        ) = x.size()  # batch size, sequence length, embedding dimensionality (n_embd)

        # Dynamically adjust mask if sequence length exceeds block_size
        if T > self.block_size:
            # Create a new mask for the larger sequence length
            new_mask = torch.tril(torch.ones(T, T, device=x.device)).view(1, 1, T, T)
        else:
            new_mask = self.mask[:, :, :T, :T]

        # calculate query, key, values for all heads in batch and move head forward to be the batch dim
        k = (self.key(x).view(B, T, self.n_head, C // self.n_head).transpose(1, 2)
             )  # (B, nh, T, hs)
        q = (
            self.query(x).view(B, T, self.n_head, C // self.n_head).transpose(1, 2)
        )  # (B, nh, T, hs)
        v = (
            self.value(x).view(B, T, self.n_head, C // self.n_head).transpose(1, 2)
        )  # (B, nh, T, hs)

        # causal self-attention; Self-attend: (B, nh, T, hs) x (B, nh, hs, T) -> (B, nh, T, T)
        att = (q @ k.transpose(-2, -1)) * (1.0 / math.sqrt(k.size(-1)))
        att = att.masked_fill(new_mask == 0, float("-inf"))
        att = F.softmax(att, dim=-1)
        att = self.attn_drop(att)
        y = att @ v  # (B, nh, T, T) x (B, nh, T, hs) -> (B, nh, T, hs)
        y = (
            y.transpose(1, 2).contiguous().view(B, T, C)
        )  # re-assemble all head outputs side by side

        # output projection
        y = self.resid_drop(self.proj(y))
        return y


class CausalSelfCrossAttention(nn.Module):
    def __init__(self, n_embd, cross_embed, n_heads, attn_pdrop, resid_pdrop, block_size):
        super().__init__()

        assert n_embd % n_heads == 0

        # Self-Attention Projections
        self.key = nn.Linear(n_embd, n_embd)
        self.query = nn.Linear(n_embd, n_embd)
        self.value = nn.Linear(n_embd, n_embd)

        # Cross-Attention Projections
        self.cross_key = nn.Linear(cross_embed, n_embd)
        self.cross_query = nn.Linear(n_embd, n_embd)
        self.cross_value = nn.Linear(cross_embed, n_embd)

        # Regularization
        self.attn_drop = nn.Dropout(attn_pdrop)
        self.resid_drop = nn.Dropout(resid_pdrop)

        # Output Projection
        self.proj = nn.Linear(n_embd, n_embd)

        # Causal mask for Self-Attention
        self.register_buffer("mask", torch.tril(torch.ones(block_size, block_size)).view(1, 1, block_size, block_size))

        self.n_head = n_heads
        self.block_size = block_size

    def forward(self, x, cross_input=None):
        B, T, C = x.size()

        # Dynamically adjust mask if sequence length exceeds block_size
        if T > self.block_size:
            # Create a new mask for the larger sequence length
            new_mask = torch.tril(torch.ones(T, T, device=x.device)).view(1, 1, T, T)
        else:
            new_mask = self.mask[:, :, :T, :T]

        # calculate query, key, values for self-attention
        k = self.key(x).view(B, T, self.n_head, C // self.n_head).transpose(1, 2)
        q = self.query(x).view(B, T, self.n_head, C // self.n_head).transpose(1, 2)
        v = self.value(x).view(B, T, self.n_head, C // self.n_head).transpose(1, 2)

        # causal self-attention
        att = (q @ k.transpose(-2, -1)) * (1.0 / math.sqrt(k.size(-1)))
        att = att.masked_fill(new_mask == 0, float('-inf'))
        att = F.softmax(att, dim=-1)
        att = self.attn_drop(att)
        y = att @ v

        if cross_input is not None and cross_input.size(1) > 0:
            # calculate query, key, values for cross-attention
            T_C = cross_input.size(1)
            k_cross = self.cross_key(cross_input).view(B, T_C, self.n_head, C // self.n_head).transpose(1, 2)
            v_cross = self.cross_value(cross_input).view(B, T_C, self.n_head, C // self.n_head).transpose(1, 2)

            q_cross = self.cross_query(x).view(B, T, self.n_head, C // self.n_head).transpose(1, 2)
            # cross-attention
            att_cross = (q_cross @ k_cross.transpose(-2, -1)) * (1.0 / math.sqrt(k_cross.size(-1)))
            att_cross = F.softmax(att_cross, dim=-1)
            att_cross = self.attn_drop(att_cross)
            y_cross = att_cross @ v_cross

            # combine self-attention and cross-attention
            y = y + y_cross  # or any other combination strategy

        y = y.transpose(1, 2).contiguous().view(B, T, C)
        y = self.resid_drop(self.proj(y))

        return y


class CausalSelfCrossAttentionV2(nn.Module):
    def __init__(self, n_embd, cross_embed, n_heads, attn_pdrop, resid_pdrop, block_size, obs_size):
        super().__init__()

        assert n_embd % n_heads == 0
        self.obs_size = obs_size
        self.block_size = block_size
        # Self-Attention Projections
        self.key = nn.Linear(n_embd, n_embd)
        self.query = nn.Linear(n_embd, n_embd)
        self.value = nn.Linear(n_embd, n_embd)

        # Cross-Attention Projections
        self.cross_key = nn.Linear(cross_embed, n_embd)
        self.cross_query = nn.Linear(n_embd, n_embd)
        self.cross_value = nn.Linear(cross_embed, n_embd)

        # Regularization
        self.attn_drop = nn.Dropout(attn_pdrop)
        self.resid_drop = nn.Dropout(resid_pdrop)

        # Output Projection
        self.proj = nn.Linear(n_embd, n_embd)

        # Causal mask for Self-Attention
        mask = torch.tril(torch.ones(block_size, block_size)).view(1, 1, block_size, block_size)
        external_mask = torch.ones(1, 1, block_size, obs_size)
        cross_mask = torch.cat([external_mask, mask], dim=3)

        self.register_buffer("mask", mask)
        self.register_buffer("cross_mask", cross_mask)

        self.n_head = n_heads

    def forward(self, x, cross_input=None):
        B, T, C = x.size()

        # Dynamically adjust mask if sequence length exceeds block_size
        if T > self.block_size:
            # Create a new mask for the larger sequence length
            new_mask = torch.tril(torch.ones(T, T, device=x.device)).view(1, 1, T, T)
        else:
            new_mask = self.mask[:, :, :T, :T]

        # calculate query, key, values for self-attention
        k = self.key(x).view(B, T, self.n_head, C // self.n_head).transpose(1, 2)
        q = self.query(x).view(B, T, self.n_head, C // self.n_head).transpose(1, 2)
        v = self.value(x).view(B, T, self.n_head, C // self.n_head).transpose(1, 2)

        # causal self-attention
        att = (q @ k.transpose(-2, -1)) * (1.0 / math.sqrt(k.size(-1)))
        att = att.masked_fill(new_mask == 0, float('-inf'))
        att = F.softmax(att, dim=-1)
        att = self.attn_drop(att)
        y = att @ v

        if cross_input is not None:
            # calculate query, key, values for cross-attention
            T_C = cross_input.size(1)
            k_cross = self.cross_key(cross_input).view(B, T_C, self.n_head, C // self.n_head).transpose(1, 2)
            v_cross = self.cross_value(cross_input).view(B, T_C, self.n_head, C // self.n_head).transpose(1, 2)

            q_cross = self.cross_query(x).view(B, T, self.n_head, C // self.n_head).transpose(1, 2)
            # cross-attention
            att_cross = (q_cross @ k_cross.transpose(-2, -1)) * (1.0 / math.sqrt(k_cross.size(-1)))
            # For cross attention, we need to handle the case where T > block_size
            if T > self.block_size:
                # Create a new cross mask for the larger sequence length
                new_external_mask = torch.ones(1, 1, T, T_C, device=x.device)
                new_cross_mask = new_external_mask
            else:
                new_cross_mask = self.cross_mask[:, :, :T, :T_C]
            
            att_cross = att_cross.masked_fill(new_cross_mask == 0, float('-inf'))
            att_cross = F.softmax(att_cross, dim=-1)
            att_cross = self.attn_drop(att_cross)
            y_cross = att_cross @ v_cross

            # combine self-attention and cross-attention
            y = y + y_cross  # or any other combination strategy

        y = y.transpose(1, 2).contiguous().view(B, T, C)
        y = self.resid_drop(self.proj(y))

        return y


class DecoderBlock(nn.Module):
    """an unassuming Transformer block"""

    def __init__(
            self,
            n_embd: int,
            cross_embd: int,
            n_heads: int,
            attn_pdrop: float,
            resid_pdrop: float,
            block_size: int,

    ):
        super().__init__()
        self.ln1 = LayerNorm(n_embd, bias=False)
        self.ln2 = LayerNorm(n_embd, bias=False)
        self.attn = CausalSelfCrossAttention(
            n_embd,
            cross_embd,
            n_heads,
            attn_pdrop,
            resid_pdrop,
            block_size,
        )
        self.mlp = nn.Sequential(
            nn.Linear(n_embd, 4 * n_embd),
            nn.GELU(),
            nn.Linear(4 * n_embd, n_embd),
            nn.Dropout(resid_pdrop),
        )

    def forward(self, x, cond=None):
        x = x + self.attn(self.ln1(x), cross_input=cond)
        x = x + self.mlp(self.ln2(x))
        return x


class DecoderBlockV2(nn.Module):
    """an unassuming Transformer block"""

    def __init__(
            self,
            n_embd: int,
            cross_embd: int,
            n_heads: int,
            attn_pdrop: float,
            resid_pdrop: float,
            block_size: int,
            obs_size: int

    ):
        super().__init__()
        self.ln1 = LayerNorm(n_embd, bias=False)
        self.ln2 = LayerNorm(n_embd, bias=False)
        self.attn = CausalSelfCrossAttentionV2(
            n_embd,
            cross_embd,
            n_heads,
            attn_pdrop,
            resid_pdrop,
            block_size,
            obs_size
        )
        self.mlp = nn.Sequential(
            nn.Linear(n_embd, 4 * n_embd),
            nn.GELU(),
            nn.Linear(4 * n_embd, n_embd),
            nn.Dropout(resid_pdrop),
        )

    def forward(self, x, cond=None):
        x = x + self.attn(self.ln1(x), cross_input=cond)
        x = x + self.mlp(self.ln2(x))
        return x


class EncoderBlock(nn.Module):
    """an unassuming Transformer block"""

    def __init__(
            self,
            n_embd: int,
            n_heads: int,
            attn_pdrop: float,
            resid_pdrop: float,
            block_size: int,

    ):
        super().__init__()
        self.ln1 = LayerNorm(n_embd, bias=False)
        self.ln2 = LayerNorm(n_embd, bias=False)
        self.attn = SelfAttention(
            n_embd,
            n_heads,
            attn_pdrop,
            resid_pdrop,
            block_size,
        )
        self.mlp = nn.Sequential(
            nn.Linear(n_embd, 4 * n_embd),
            nn.GELU(),
            nn.Linear(4 * n_embd, n_embd),
            nn.Dropout(resid_pdrop),
        )

    def forward(self, x):
        x = x + self.attn(self.ln1(x))
        x = x + self.mlp(self.ln2(x))
        return x


class TransformerEncoder(nn.Module):

    def __init__(
            self,
            embed_dim: int,
            n_heads: int,
            attn_pdrop: float,
            resid_pdrop: float,
            n_layers: int,
            block_size: int,
            bias: bool = False,
            n_dc_layers: int = 0,
    ):
        super().__init__()
        self.n_dc_layers = n_dc_layers
        self.blocks = nn.Sequential(
            *[EncoderBlock(
                embed_dim,
                n_heads,
                attn_pdrop,
                resid_pdrop,
                block_size,
            )
                for _ in range(n_layers)]
        )
        self.ln = LayerNorm(embed_dim, bias)

    def forward(self, x, dc_tokens=None, dc_time_emb=None):
        """
        前向传播，在每一层前注入DC tokens

        Args:
            x: 输入特征 [batch_size, seq_len, embed_dim]
            dc_tokens: DC tokens参数 [n_dc_layers, n_dc_tokens, embed_dim]
            dc_time_emb: 时间相关的DC tokens嵌入列表
        """
        original_seq_len = x.shape[1]  # 记录原始序列长度

        for layer_idx, layer in enumerate(self.blocks):
            # 在前n_dc_layers层注入DC tokens
            if (dc_tokens is not None and layer_idx < self.n_dc_layers and
                layer_idx < dc_tokens.shape[0]):

                batch_size = x.shape[0]
                n_dc_tokens = dc_tokens.shape[1]

                # 获取当前层的DC tokens
                current_dc = dc_tokens[layer_idx].unsqueeze(0).expand(batch_size, -1, -1)

                # 如果有时间相关的DC tokens，添加时间信息
                if dc_time_emb is not None and layer_idx < len(dc_time_emb):
                    dc_t_raw = dc_time_emb[layer_idx]  # (batch_size, embed_dim)
                    if dc_t_raw.numel() > 0:  # 检查张量不为空
                        dc_t = dc_t_raw.unsqueeze(1).expand(-1, n_dc_tokens, -1)  # (batch_size, n_dc_tokens, embed_dim)
                        current_dc = current_dc + dc_t

                # 将DC tokens拼接到输入序列前面
                x = torch.cat([current_dc, x], dim=1)

            # 通过当前层
            x = layer(x)

            # 如果添加了DC tokens，在层处理后移除它们，保持原始序列长度
            if (dc_tokens is not None and layer_idx < self.n_dc_layers and
                layer_idx < dc_tokens.shape[0]):
                x = x[:, n_dc_tokens:, :]  # 移除DC tokens，保留原始序列

        x = self.ln(x)
        return x


class TransformerDecoder(nn.Module):

    def __init__(
            self,
            embed_dim: int,
            cross_embed: int,
            n_heads: int,
            attn_pdrop: float,
            resid_pdrop: float,
            n_layers: int,
            block_size: int,
            bias: bool = False,
            decoder_n_dc_layers: int = 0,
    ):
        super().__init__()
        self.n_dc_layers = decoder_n_dc_layers
        self.blocks = nn.Sequential(
            *[DecoderBlock(
                embed_dim,
                cross_embed,
                n_heads,
                attn_pdrop,
                resid_pdrop,
                block_size,
            )
                for _ in range(n_layers)]
        )
        self.ln = LayerNorm(embed_dim, bias)

    def forward(self, x, cond=None, dc_tokens=None, dc_time_emb=None):
        """
        前向传播，在每一层前注入DC tokens

        Args:
            x: 输入特征 [batch_size, seq_len, embed_dim]
            cond: 条件特征（来自encoder）
            dc_tokens: DC tokens参数 [n_dc_layers, n_dc_tokens, embed_dim]
            dc_time_emb: 时间相关的DC tokens嵌入列表
        """
        for layer_idx, layer in enumerate(self.blocks):
            # 在前n_dc_layers层注入DC tokens
            if (dc_tokens is not None and layer_idx < self.n_dc_layers and
                layer_idx < dc_tokens.shape[0]):

                batch_size = x.shape[0]
                n_dc_tokens = dc_tokens.shape[1]

                # 获取当前层的DC tokens
                current_dc = dc_tokens[layer_idx].unsqueeze(0).expand(batch_size, -1, -1)

                # 如果有时间相关的DC tokens，添加时间信息
                if dc_time_emb is not None and layer_idx < len(dc_time_emb):
                    dc_t_raw = dc_time_emb[layer_idx]  # (batch_size, embed_dim)
                    if dc_t_raw.numel() > 0:  # 检查张量不为空
                        dc_t = dc_t_raw.unsqueeze(1).expand(-1, n_dc_tokens, -1)  # (batch_size, n_dc_tokens, embed_dim)
                        current_dc = current_dc + dc_t

                # 将DC tokens拼接到输入序列前面
                x = torch.cat([current_dc, x], dim=1)

            # 通过当前层
            x = layer(x, cond=cond)

            # 如果添加了DC tokens，在层处理后移除它们，保持原始序列长度
            if (dc_tokens is not None and layer_idx < self.n_dc_layers and
                layer_idx < dc_tokens.shape[0]):
                x = x[:, n_dc_tokens:, :]  # 移除DC tokens，保留原始序列

        x = self.ln(x)
        return x


class TransformerDecoderV2(nn.Module):

    def __init__(
            self,
            embed_dim: int,
            cross_embed: int,
            n_heads: int,
            attn_pdrop: float,
            resid_pdrop: float,
            n_layers: int,
            block_size: int,
            obs_size: int,
            bias: bool = False,
    ):
        super().__init__()
        self.blocks = nn.Sequential(
            *[DecoderBlockV2(
                embed_dim,
                cross_embed,
                n_heads,
                attn_pdrop,
                resid_pdrop,
                block_size,
                obs_size
            )
                for _ in range(n_layers)]
        )
        self.ln = LayerNorm(embed_dim, bias)

    def forward(self, x, cond=None):
        for layer in self.blocks:
            x = layer(x, cond=cond)
        x = self.ln(x)
        return x


class DiffusionEncDec(nn.Module):
    """Diffusion model with transformer architecture for state, goal, time and action tokens,
    with a context size of block_size"""

    def __init__(
            self,
            encoder: DictConfig,
            decoder: DictConfig,
            state_dim: int,
            action_dim: int,
            device: str,
            embed_dim: int,
            embed_pdrob: float,
            goal_seq_len: int,
            obs_seq_len: int,
            action_seq_len: int,
            goal_drop: float = 0.1,
            goal_conditioned: bool = False,
            linear_output: bool = False,
            n_dc_layers: int = 6,
            n_dc_tokens: int = 7,
            use_dc_t: bool = True,
            use_dc: bool = True,
            # Decoder DC tokens 参数
            n_decoder_dc_layers: int = 6,
            n_decoder_dc_tokens: int = 7,
            use_decoder_dc_t: bool = True,
            use_decoder_dc: bool = True,
    ):
        super().__init__()

        self.encoder = hydra.utils.instantiate(encoder)
        self.decoder = hydra.utils.instantiate(decoder)

        self.device = device
        self.goal_conditioned = goal_conditioned
        if not goal_conditioned:
            goal_seq_len = 0
        # input embedding stem
        # first we need to define the maximum block size
        # it consists of the goal sequence length plus 1 for the sigma embedding and 2 the obs seq len
        block_size = goal_seq_len + action_seq_len + obs_seq_len + 1
        # the seq_size is a little different since we have state action pairs for every timestep
        seq_size = goal_seq_len + obs_seq_len + action_seq_len

        self.tok_emb = nn.Linear(state_dim, embed_dim)
        self.tok_emb.to(self.device)

        self.pos_emb = nn.Parameter(torch.zeros(1, seq_size, embed_dim))
        self.drop = nn.Dropout(embed_pdrob)
        self.drop.to(self.device)

        # needed for calssifier guidance learning
        self.cond_mask_prob = goal_drop

        self.action_dim = action_dim
        self.obs_dim = state_dim
        self.embed_dim = embed_dim

        self.block_size = block_size
        self.goal_seq_len = goal_seq_len
        self.obs_seq_len = obs_seq_len
        self.action_seq_len = action_seq_len

        '''==================================================================================================='''
        # Encoder DC tokens 配置
        self.n_dc_tokens = n_dc_tokens
        self.n_dc_layers = n_dc_layers
        self.use_dc_t = use_dc_t
        self.use_dc = use_dc
        self.use_dc_tokens = use_dc  # Alias for consistency

        # 初始化Encoder DC tokens参数
        if self.use_dc and self.n_dc_tokens > 0 and self.n_dc_layers > 0:
            self.dc_tokens = nn.Parameter(torch.randn(self.n_dc_layers, self.n_dc_tokens, embed_dim))
            
            

            if self.use_dc_t:
                self.dc_t_tokens = nn.Embedding(100, embed_dim * self.n_dc_layers)
                
            else:
                self.dc_t_tokens = None
        else:
            self.dc_tokens = None
            self.dc_t_tokens = None

        # Decoder DC tokens 配置
        self.n_decoder_dc_tokens = n_decoder_dc_tokens
        self.n_decoder_dc_layers = n_decoder_dc_layers
        self.use_decoder_dc_t = use_decoder_dc_t
        self.use_decoder_dc = use_decoder_dc

        # 初始化Decoder DC tokens参数
        if self.use_decoder_dc and self.n_decoder_dc_tokens > 0 and self.n_decoder_dc_layers > 0:
            self.decoder_dc_tokens = nn.Parameter(torch.randn(self.n_decoder_dc_layers, self.n_decoder_dc_tokens, embed_dim))
            
            

            if self.use_decoder_dc_t:
                self.decoder_dc_t_tokens = nn.Embedding(100, embed_dim * self.n_decoder_dc_layers)
                
            else:
                self.decoder_dc_t_tokens = None
        else:
            self.decoder_dc_tokens = None
            self.decoder_dc_t_tokens = None

        '''==================================================================================================='''

        # we need another embedding for the time
        self.time_emb = nn.Sequential(
            SinusoidalPosEmb(embed_dim),
            nn.Linear(embed_dim, embed_dim * 2),
            nn.Mish(),
            nn.Linear(embed_dim * 2, embed_dim),
        )
        self.time_emb.to(self.device)
        # get an action embedding
        self.action_emb = nn.Linear(action_dim, embed_dim)
        self.action_emb.to(self.device)
        # action pred module
        if linear_output:
            self.action_pred = nn.Linear(embed_dim, action_dim)
        else:
            self.action_pred = nn.Sequential(
                nn.Linear(embed_dim, 100),
                nn.GELU(),
                nn.Linear(100, self.action_dim)
            )
        # self.action_pred = nn.Linear(embed_dim, action_dim) # less parameters, worse reward
        self.action_pred.to(self.device)

        self.apply(self._init_weights)

        logger.info(
            "number of parameters: %e", sum(p.numel() for p in self.parameters())
        )

    def get_block_size(self):
        return self.block_size

    def _init_weights(self, module):
        if isinstance(module, (nn.Linear, nn.Embedding)):
            torch.nn.init.normal_(module.weight, mean=0.0, std=0.02)
            if isinstance(module, nn.Linear) and module.bias is not None:
                torch.nn.init.zeros_(module.bias)
        elif isinstance(module, nn.LayerNorm):
            torch.nn.init.zeros_(module.bias)
            torch.nn.init.ones_(module.weight)

    def configure_optimizers(self, train_config):
        """
        This long function is unfortunately doing something very simple and is being very defensive:
        We are separating out all parameters of the model into two buckets: those that will experience
        weight decay for regularization and those that won't (biases, and layernorm/embedding weights).
        We are then returning the PyTorch optimizer object.
        """

        # separate out all parameters to those that will and won't experience regularizing weight decay
        decay = set()
        no_decay = set()
        whitelist_weight_modules = (torch.nn.Linear,)
        blacklist_weight_modules = (torch.nn.LayerNorm, torch.nn.Embedding)
        for mn, m in self.named_modules():
            for pn, p in m.named_parameters():
                fpn = "%s.%s" % (mn, pn) if mn else pn  # full param name

                if pn.endswith("bias"):
                    # all biases will not be decayed
                    no_decay.add(fpn)
                elif pn.endswith("weight") and isinstance(m, whitelist_weight_modules):
                    # weights of whitelist modules will be weight decayed
                    decay.add(fpn)
                elif pn.endswith("weight") and isinstance(m, blacklist_weight_modules):
                    # weights of blacklist modules will NOT be weight decayed
                    no_decay.add(fpn)

        # special case the position embedding parameter in the root GPT module as not decayed
        no_decay.add("pos_emb")

        # validate that we considered every parameter
        param_dict = {pn: p for pn, p in self.named_parameters()}
        inter_params = decay & no_decay
        union_params = decay | no_decay
        assert (
                len(inter_params) == 0
        ), "parameters %s made it into both decay/no_decay sets!" % (str(inter_params),)
        assert (
                len(param_dict.keys() - union_params) == 0
        ), "parameters %s were not separated into either decay/no_decay set!" % (
            str(param_dict.keys() - union_params),
        )

        # create the pytorch optimizer object
        optim_groups = [
            {
                "params": [param_dict[pn] for pn in sorted(list(decay))],
                "weight_decay": train_config.weight_decay,
            },
            {
                "params": [param_dict[pn] for pn in sorted(list(no_decay))],
                "weight_decay": 0.0,
            },
        ]
        optimizer = torch.optim.AdamW(
            optim_groups, lr=train_config.learning_rate, betas=train_config.betas
        )
        return optimizer

    # x: torch.Tensor, t: torch.Tensor, s: torch.Tensor, g: torch.Tensor
    # def forward(self, x, t, state, goal):
    def mask_cond(self, cond, force_mask=False):
        """Mask conditioning for classifier-free guidance"""
        bs, t, d = cond.shape
        if force_mask:
            return torch.zeros_like(cond)
        elif self.training and self.cond_mask_prob > 0.:
            mask = torch.bernoulli(torch.ones(bs, device=cond.device) * self.cond_mask_prob).view(bs, 1, 1)
            return cond * (1. - mask)
        else:
            return cond

    def forward(
            self,
            actions,
            time,
            states,
            goals,
            uncond: Optional[bool] = False,
            keep_last_actions: Optional[bool] = False,
            return_features: Optional[bool] = False,
    ):

        # actions = actions[:, self.obs_seq_len-1:, :]
        # states = states[:, :self.obs_seq_len, :]

        if len(states.size()) != 3:
            states = states.unsqueeze(0)

        if len(actions.size()) != 3:
            actions = actions.unsqueeze(0)

        if goals is not None:
            if len(goals.size()) != 3:
                goals = goals.unsqueeze(0)

        b, t, dim = states.size()
        assert t <= self.block_size, "Cannot forward, model block size is exhausted."



        # get the time embedding
        times = einops.rearrange(time, 'b -> b 1')
        emb_t = self.time_emb(times)

        # 准备Encoder DC tokens相关的时间嵌入
        dc_time_emb = None
        if self.use_dc and self.use_dc_t:
            # 时间步离散化处理，类似SoftREPA的实现
            int_t = time.type(torch.int32)
            if torch.sum(int_t >= 100) > 0:  # 处理边界情况，将时间步映射到0-99范围
                int_t = torch.clamp(int_t, 0, 99)
            dc_time_emb = self.dc_t_tokens(int_t).contiguous()  # (batch_size, embed_dim * n_dc_layers)
            dc_time_emb = dc_time_emb.chunk(self.n_dc_layers, dim=-1)  # 按层分块

        # 准备Decoder DC tokens相关的时间嵌入
        decoder_dc_time_emb = None
        if self.use_decoder_dc and self.use_decoder_dc_t:
            # 时间步离散化处理
            int_t = time.type(torch.int32)
            if torch.sum(int_t >= 100) > 0:  # 处理边界情况，将时间步映射到0-99范围
                int_t = torch.clamp(int_t, 0, 99)
            decoder_dc_time_emb = self.decoder_dc_t_tokens(int_t).contiguous()  # (batch_size, embed_dim * n_decoder_dc_layers)
            decoder_dc_time_emb = decoder_dc_time_emb.chunk(self.n_decoder_dc_layers, dim=-1)  # 按层分块

        if self.goal_conditioned and goals is not None:

            if self.training:
                goals = self.mask_cond(goals)
            # we want to use unconditional sampling during clasisfier free guidance
            if uncond:
                goals = torch.zeros_like(goals).to(self.device)

            goal_embed = self.tok_emb(goals)
        else:
            goal_embed = None

        # embed them into linear representations for the transformer
        state_embed = self.tok_emb(states)
        action_embed = self.action_emb(actions)

        position_embeddings = self.pos_emb[:, :(t + self.goal_seq_len + self.action_seq_len - 1), :]
        # note, that the goal states are at the beginning of the sequence since they are available
        # for all states s_1, ..., s_t otherwise the masking would not make sense
        if self.goal_conditioned and goal_embed is not None:
            goal_x = self.drop(goal_embed + position_embeddings[:, :self.goal_seq_len, :])
            state_x = self.drop(state_embed + position_embeddings[:, self.goal_seq_len:(self.goal_seq_len + t), :])
            input_seq = torch.cat([emb_t, goal_x, state_x], dim=1)
        else:
            # When no goal, adjust position embeddings accordingly
            state_x = self.drop(state_embed + position_embeddings[:, :t, :])
            input_seq = torch.cat([emb_t, state_x], dim=1)

        action_x = self.drop(action_embed + position_embeddings[:, (self.goal_seq_len + t - 1):, :])

        # encode the state, goal and latent z into the hidden dim
        # 将DC tokens传递给encoder，让它在每一层前进行拼接
        if self.use_dc and self.n_dc_tokens > 0:
            encoder_output = self.encoder(input_seq, dc_tokens=self.dc_tokens, dc_time_emb=dc_time_emb)
        else:
            encoder_output = self.encoder(input_seq)

        # decode actions with optional decoder DC tokens
        # 将Decoder DC tokens传递给decoder，让它在每一层前进行拼接
        if self.use_decoder_dc and self.n_decoder_dc_tokens > 0:
            decoder_output = self.decoder(
                action_x,
                cond=encoder_output,
                dc_tokens=self.decoder_dc_tokens,
                dc_time_emb=decoder_dc_time_emb
            )
        else:
            decoder_output = self.decoder(action_x, cond=encoder_output)

        pred_actions = self.action_pred(decoder_output)

        # 如果需要返回特征用于disperse loss，返回encoder输出作为特征（late encoder 特征）
        if return_features:
            return pred_actions, encoder_output
        else:
            return pred_actions

    def disp_loss(self, z):
        """
        Simplified Dispersive Loss implementation (InfoNCE-L2 variant)
        Based on diffusion_policy.py implementation for numerical stability
        
        Args:
            z: 特征张量 [num_samples, ...] 现在可能是 [B*B, feature_dim]
            
        Returns:
            torch.Tensor: Dispersive loss值
        """
        if z is None:
            return torch.tensor(0.0, device='cpu', requires_grad=True)
        
        # 检查输入有效性
        if z.numel() == 0 or z.shape[0] < 2:
            return torch.tensor(0.0, device=z.device, requires_grad=True)
             
        # 将特征张量展平
        z = z.reshape((z.shape[0], -1))  # [num_samples, feature_dim]

        # 数值稳定性：将 NaN/Inf 清理为有限数
        if not torch.isfinite(z).all():
            z = torch.nan_to_num(z, nan=0.0, posinf=1e6, neginf=-1e6)
         
        # 🔥 修复实现：使用标准的 dispersive loss 算法
        # 计算成对的L2距离，按特征维度归一化
        pairwise_distances = torch.nn.functional.pdist(z).pow(2) / max(z.shape[1], 1)
        # 清理距离中的NaN/Inf
        if not torch.isfinite(pairwise_distances).all():
            pairwise_distances = torch.nan_to_num(pairwise_distances, nan=10.0, posinf=10.0, neginf=0.0)
         
        # 检查是否有有效的距离
        if pairwise_distances.numel() == 0:
            return torch.tensor(0.0, device=z.device, requires_grad=True)
         
        # 🔥 数值稳定性：限制距离范围避免exp溢出
        pairwise_distances = torch.clamp(pairwise_distances, min=0.0, max=10.0)
         
        # 🔥 标准InfoNCE风格损失：鼓励样本分散
        # 距离越大越好，所以我们最小化 -log(exp(-distances).mean())
        # 等价于最大化平均距离的负指数
        neg_exp_distances = torch.exp(-pairwise_distances)
        mean_neg_exp = neg_exp_distances.mean()
         
        # 🔥 数值稳定性：避免 log(0)
        mean_neg_exp = torch.clamp(mean_neg_exp, min=1e-8)
         
        # 返回 log(mean(exp(-distances)))，这个值越小越好（表示样本越分散）
        return torch.log(mean_neg_exp)

    def _compute_dispersive_loss(self, features):
        """
        计算dispersive loss，使用模型的最后输出作为特征
        
        Args:
            features: 模型的最后输出张量
            
        Returns:
            torch.Tensor: 计算得到的dispersive loss
        """
        if features is not None and isinstance(features, torch.Tensor):
            return self.disp_loss(features)
        else:
            # 如果没有有效特征，返回零损失
            return torch.tensor(0.0, device=self.device, requires_grad=True)

    

    def get_params(self):
        return self.parameters()


class DiffusionEncDecV2(nn.Module):
    """Diffusion model with transformer architecture for state, goal, time and action tokens,
    with a context size of block_size"""

    def __init__(
            self,
            encoder: DictConfig,
            decoder: DictConfig,
            state_dim: int,
            action_dim: int,
            device: str,
            goal_conditioned: bool,
            embed_dim: int,
            embed_pdrob: float,
            goal_seq_len: int,
            obs_seq_len: int,
            action_seq_len: int,
            goal_drop: float = 0.1,
            linear_output: bool = False,
    ):
        super().__init__()

        self.encoder = hydra.utils.instantiate(encoder)
        self.decoder = hydra.utils.instantiate(decoder)


class DiffusionEncDecPaired(nn.Module):
    """Diffusion model with paired encoder-decoder layers (one encoder layer paired with one decoder layer)
    following LeRobot's TransformerForDiffusion structure"""

    def __init__(
            self,
            encoder: DictConfig,
            decoder: DictConfig,
            state_dim: int,
            action_dim: int,
            device: str,
            goal_conditioned: bool,
            embed_dim: int,
            embed_pdrob: float,
            goal_seq_len: int,
            obs_seq_len: int,
            action_seq_len: int,
            goal_drop: float = 0.1,
            linear_output: bool = False,
    ):
        super().__init__()

        # Store configuration
        self.device = device
        self.goal_conditioned = goal_conditioned
        if not goal_conditioned:
            goal_seq_len = 0

        # Calculate block and sequence sizes
        block_size = goal_seq_len + action_seq_len + obs_seq_len + 1
        seq_size = goal_seq_len + obs_seq_len + action_seq_len

        # Basic embeddings
        self.tok_emb = nn.Linear(state_dim, embed_dim)
        self.tok_emb.to(self.device)

        self.pos_emb = nn.Parameter(torch.zeros(1, seq_size, embed_dim))
        self.drop = nn.Dropout(embed_pdrob)
        self.drop.to(self.device)

        # Conditioning mask probability
        self.cond_mask_prob = goal_drop

        # Store dimensions
        self.action_dim = action_dim
        self.obs_dim = state_dim
        self.embed_dim = embed_dim
        self.block_size = block_size
        self.goal_seq_len = goal_seq_len
        self.obs_seq_len = obs_seq_len
        self.action_seq_len = action_seq_len

        # Time embedding
        self.time_emb = nn.Sequential(
            SinusoidalPosEmb(embed_dim),
            nn.Linear(embed_dim, embed_dim * 2),
            nn.Mish(),
            nn.Linear(embed_dim * 2, embed_dim),
        )
        self.time_emb.to(self.device)

        # Action embedding
        self.action_emb = nn.Linear(action_dim, embed_dim)
        self.action_emb.to(self.device)

        # Get number of layers from encoder config
        self.n_layers = encoder.n_layers

        # Store configs for reference (but don't instantiate the full modules)
        self.encoder_config = encoder
        self.decoder_config = decoder

        # Create paired encoder-decoder layers
        self.encoder_layers = nn.ModuleList()
        self.decoder_layers = nn.ModuleList()

        for i in range(self.n_layers):
            # Create encoder layer
            enc_layer = EncoderBlock(
                encoder.embed_dim,
                encoder.n_heads,
                encoder.attn_pdrop,
                encoder.resid_pdrop,
                encoder.block_size,
            )
            self.encoder_layers.append(enc_layer)

            # Create decoder layer
            dec_layer = DecoderBlock(
                decoder.embed_dim,
                encoder.embed_dim,  # cross_embed from encoder
                decoder.n_heads,
                decoder.attn_pdrop,
                decoder.resid_pdrop,
                decoder.block_size,
            )
            self.decoder_layers.append(dec_layer)

        # Final layer norms
        self.encoder_ln = LayerNorm(embed_dim, False)
        self.decoder_ln = LayerNorm(embed_dim, False)

        # Action prediction head
        if linear_output:
            self.action_pred = nn.Linear(embed_dim, action_dim)
        else:
            self.action_pred = nn.Sequential(
                nn.Linear(embed_dim, 100),
                nn.GELU(),
                nn.Linear(100, self.action_dim)
            )
        self.action_pred.to(self.device)

        self.apply(self._init_weights)

        logger.info(
            "number of parameters: %e", sum(p.numel() for p in self.parameters())
        )

    def get_block_size(self):
        return self.block_size

    def _init_weights(self, module):
        if isinstance(module, (nn.Linear, nn.Embedding)):
            torch.nn.init.normal_(module.weight, mean=0.0, std=0.02)
            if isinstance(module, nn.Linear) and module.bias is not None:
                torch.nn.init.zeros_(module.bias)
        elif isinstance(module, nn.LayerNorm):
            torch.nn.init.zeros_(module.bias)
            torch.nn.init.ones_(module.weight)

    def mask_cond(self, cond, force_mask=False):
        bs, t, d = cond.shape
        if force_mask:
            return torch.zeros_like(cond)
        elif self.training and self.cond_mask_prob > 0.:
            mask = torch.bernoulli(torch.ones(bs, device=cond.device) * self.cond_mask_prob).view(bs, 1, 1)
            return cond * (1. - mask)
        else:
            return cond

    def configure_optimizers(self, train_config):
        """
        This long function is unfortunately doing something very simple and is being very defensive:
        We are separating out all parameters of the model into two buckets: those that will experience
        weight decay for regularization and those that won't (biases, and layernorm/embedding weights).
        We are then returning the PyTorch optimizer object.
        """

        # separate out all parameters to those that will and won't experience regularizing weight decay
        decay = set()
        no_decay = set()
        whitelist_weight_modules = (torch.nn.Linear, )
        blacklist_weight_modules = (torch.nn.LayerNorm, torch.nn.Embedding)
        for mn, m in self.named_modules():
            for pn, p in m.named_parameters():
                fpn = '%s.%s' % (mn, pn) if mn else pn # full param name

                if pn.endswith('bias'):
                    # all biases will not be decayed
                    no_decay.add(fpn)
                elif pn.endswith('weight') and isinstance(m, whitelist_weight_modules):
                    # weights of whitelist modules will be weight decayed
                    decay.add(fpn)
                elif pn.endswith('weight') and isinstance(m, blacklist_weight_modules):
                    # weights of blacklist modules will NOT be weight decayed
                    no_decay.add(fpn)

        # special case the position embedding parameter in the root GPT module as not decayed
        no_decay.add('pos_emb')

        # validate that we considered every parameter
        param_dict = {pn: p for pn, p in self.named_parameters()}
        inter_params = decay & no_decay
        union_params = decay | no_decay
        assert len(inter_params) == 0, "parameters %s made it into both decay/no_decay sets!" % (str(inter_params), )
        assert len(param_dict.keys() - union_params) == 0, "parameters %s were not separated into either decay/no_decay set!" \
                                                    % (str(param_dict.keys() - union_params), )

        # create the pytorch optimizer object
        optim_groups = [
            {"params": [param_dict[pn] for pn in sorted(list(decay))], "weight_decay": train_config.weight_decay},
            {"params": [param_dict[pn] for pn in sorted(list(no_decay))], "weight_decay": 0.0},
        ]
        optimizer = torch.optim.AdamW(optim_groups, lr=train_config.learning_rate, betas=train_config.betas)
        return optimizer

    def forward(
            self,
            actions,
            time,
            states,
            goals,
            uncond: Optional[bool] = False,
            keep_last_actions: Optional[bool] = False
    ):
        """
        Forward pass with paired encoder-decoder layers.
        Each encoder layer output is immediately used by the corresponding decoder layer.
        """

        if len(states.size()) != 3:
            states = states.unsqueeze(0)

        if len(actions.size()) != 3:
            actions = actions.unsqueeze(0)

        if goals is not None:
            if len(goals.size()) != 3:
                goals = goals.unsqueeze(0)

        b, t, dim = states.size()
        assert t <= self.block_size, "Cannot forward, model block size is exhausted."

        # Get the time embedding
        times = einops.rearrange(time, 'b -> b 1')
        emb_t = self.time_emb(times)

        # Handle goal conditioning
        if self.goal_conditioned:
            if self.training:
                goals = self.mask_cond(goals)
            # Use unconditional sampling during classifier free guidance
            if uncond:
                goals = torch.zeros_like(goals).to(self.device)
            goal_embed = self.tok_emb(goals)

        # Embed inputs
        state_embed = self.tok_emb(states)
        action_embed = self.action_emb(actions)

        # Position embeddings
        position_embeddings = self.pos_emb[:, :(t + self.goal_seq_len + self.action_seq_len - 1), :]

        # Prepare encoder input (time + states + goals)
        if self.goal_conditioned:
            goal_x = self.drop(goal_embed + position_embeddings[:, :self.goal_seq_len, :])

        state_x = self.drop(state_embed + position_embeddings[:, self.goal_seq_len:(self.goal_seq_len + t), :])

        # Encoder input: time embedding + states (+ goals if conditioned)
        if self.goal_conditioned:
            encoder_input = torch.cat([emb_t, goal_x, state_x], dim=1)
        else:
            encoder_input = torch.cat([emb_t, state_x], dim=1)

        # Prepare decoder input (actions)
        action_x = self.drop(action_embed + position_embeddings[:, (self.goal_seq_len + t - 1):, :])

        # Paired encoder-decoder processing
        memory = encoder_input
        decoder_input = action_x

        # Process through paired layers
        for enc_layer, dec_layer in zip(self.encoder_layers, self.decoder_layers):
            # Encoder layer processes memory
            memory = enc_layer(memory)

            # Decoder layer uses current memory state
            decoder_input = dec_layer(decoder_input, cond=memory)

        # Apply final layer norms
        memory = self.encoder_ln(memory)
        decoder_output = self.decoder_ln(decoder_input)

        # Predict actions
        pred_actions = self.action_pred(decoder_output)

        return pred_actions

    def get_params(self):
        return self.parameters()
