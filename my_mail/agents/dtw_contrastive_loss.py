"""
DTW-Guided Contrastive Loss for DC Training in MaIL
Implements soft contrastive learning using DTW similarity matrices as supervision.
Based on CLASS project's approach but adapted for MaIL's DC training framework.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import logging
import wandb
from typing import Dict, Tuple, Optional
import math
import pdb

log = logging.getLogger(__name__)


class DTWGuidedContrastiveLoss(nn.Module):
    """
    DTW-guided soft contrastive loss for action-observation alignment.
    Uses DTW similarity matrix as soft labels to guide contrastive learning.
    
    This loss combines:
    1. DTW-guided alignment loss: Aligns diffusion similarity with DTW similarity
    2. Weighted contrastive loss: Uses DTW similarity as weights for positive/negative pairs
    3. Optional dispersive loss: Encourages diversity in learned representations
    """
    
    def __init__(
        self,
        temperature: float = 0.07,
        scale: float = 4.0,
        alignment_weight: float = 1.0,
        contrastive_weight: float = 0.5,
        diagonal_weight: float = 0.1,
        hard_negative_weight: float = 2.0,
        positive_threshold: float = 0.1,
        use_soft_labels: bool = True,
        use_symmetric_loss: bool = True,
        device: str = 'cuda'
    ):
        super().__init__()
        
        self.device = device
        self.temperature = nn.Parameter(torch.tensor(temperature, device=device))
        self.scale = nn.Parameter(torch.tensor(scale, device=device))
        
        # Loss component weights
        self.alignment_weight = alignment_weight
        self.contrastive_weight = contrastive_weight
        self.diagonal_weight = diagonal_weight
        self.hard_negative_weight = hard_negative_weight
        
        # Threshold for positive sample identification
        self.positive_threshold = positive_threshold
        
        # Loss configuration
        self.use_soft_labels = use_soft_labels
        self.use_symmetric_loss = use_symmetric_loss
        
        log.info(f"✅ Initialized DTWGuidedContrastiveLoss:")
        log.info(f"  - Temperature: {temperature}")
        log.info(f"  - Scale: {scale}")
        log.info(f"  - Alignment weight: {alignment_weight}")
        log.info(f"  - Contrastive weight: {contrastive_weight}")
        log.info(f"  - Use soft labels: {use_soft_labels}")
    
    def forward(
        self,
        error_matrix: torch.Tensor,
        dtw_similarity: torch.Tensor,
        extended_batch_info: Optional[Dict] = None
    ) -> Tuple[torch.Tensor, Dict[str, torch.Tensor]]:
        """
        Compute DTW-guided contrastive loss for cross-paired action-observation matching

        Args:
            error_matrix: (B, B) diffusion error matrix where error[i,j] is the error
                         between action_i and observation_j
            dtw_similarity: (B, B) DTW similarity matrix [0, 1] where 1 means most similar
                           between action_i and action_j
            extended_batch_info: Optional batch information for episode-aware processing

        Returns:
            Tuple of:
            - total_loss: Combined loss scalar
            - loss_components: Dictionary with individual loss components

        Logic:
            If DTW shows action_i and action_j are similar, then:
            - (action_i, obs_j) should have low error (positive pair)
            - (action_j, obs_i) should have low error (positive pair)
        """
        # Main loss: DTW-guided cross-pairing contrastive loss
        cross_contrastive_loss = self._compute_cross_pairing_contrastive_loss(error_matrix, dtw_similarity)

        # Optional diagonal loss to ensure self-pairs have low error
        diagonal_loss = self._compute_diagonal_loss(error_matrix)

        # Combine DTW losses (disperse_loss will be handled separately)
        total_loss = (
            self.contrastive_weight * cross_contrastive_loss +
            self.diagonal_weight * diagonal_loss
        )

        # Get disperse_loss from extended_batch_info if available
        disperse_loss = torch.tensor(0.0, device=self.device)
        if extended_batch_info is not None and 'disperse_loss' in extended_batch_info:
            disperse_loss = extended_batch_info['disperse_loss']

        # Prepare loss components for logging
        batch_size = dtw_similarity.shape[0]
        diagonal_mask = torch.eye(batch_size, device=self.device, dtype=torch.bool)
        positive_mask = dtw_similarity > self.positive_threshold
        positive_mask_no_diag = positive_mask & ~diagonal_mask

        # Count weighted pairs
        diagonal_pairs = diagonal_mask.sum()  # Always batch_size
        positive_off_diag_pairs = positive_mask_no_diag.sum()
        total_contributing_pairs = diagonal_pairs + positive_off_diag_pairs

        loss_components = {
            # Main loss components (matching original format)
            'total_loss': total_loss,
            'contrastive_loss': cross_contrastive_loss,  # Renamed to match original
            'diagonal_loss': diagonal_loss,
            'disperse_loss': disperse_loss,  # Placeholder for future dispersive regularization

            # DTW-specific metrics
            'dtw_positive_pairs': positive_off_diag_pairs,  # Off-diagonal positive pairs
            'dtw_diagonal_pairs': diagonal_pairs,  # Diagonal pairs (always contributing)
            'dtw_total_contributing': total_contributing_pairs,  # Total pairs contributing to loss
            'dtw_similarity_mean': dtw_similarity.mean(),
            'error_matrix_mean': error_matrix.mean(),
            'diagonal_error_mean': torch.diag(error_matrix).mean(),

            # Additional useful metrics
            'positive_threshold': torch.tensor(self.positive_threshold, device=self.device),
            'contrastive_weight': torch.tensor(self.contrastive_weight, device=self.device),
            'diagonal_weight': torch.tensor(self.diagonal_weight, device=self.device)
        }

        return total_loss, loss_components

    def forward_with_components(
        self,
        error_matrix: torch.Tensor,
        dtw_similarity: torch.Tensor,
        extended_batch_info: Optional[Dict] = None
    ) -> Dict[str, torch.Tensor]:
        """
        Compute DTW-guided contrastive loss with detailed component breakdown

        Similar to IntegratedEpisodeContrastiveLoss.forward_with_components()
        This method returns a dictionary with all loss components for detailed monitoring.

        Args:
            error_matrix: (B, B) diffusion error matrix
            dtw_similarity: (B, B) DTW similarity matrix
            extended_batch_info: Optional batch information

        Returns:
            Dictionary containing:
            - total_loss: Combined loss scalar
            - contrastive_loss: DTW-guided contrastive loss component
            - diagonal_loss: Self-pairing regularization component
            - disperse_loss: Dispersive regularization (currently 0)
            - Additional DTW metrics and statistics
        """
        # Compute the loss using the main forward method
        total_loss, loss_components = self.forward(error_matrix, dtw_similarity, extended_batch_info)

        # Return the detailed components dictionary
        # This matches the format expected by the training loop
        return loss_components

    def _error_to_similarity(self, error_matrix: torch.Tensor) -> torch.Tensor:
        """Convert diffusion error matrix to similarity matrix"""
        # Use exponential decay: higher error -> lower similarity
        similarity = torch.exp(-error_matrix / self.temperature)
        return similarity
    
    def _compute_cross_pairing_statistics(
        self,
        error_matrix: torch.Tensor,
        dtw_similarity: torch.Tensor
    ) -> Dict[str, float]:
        """
        Compute statistics for cross-pairing contrastive learning
        """
        batch_size = error_matrix.shape[0]

        # Identify positive and negative pairs
        positive_mask = dtw_similarity > self.positive_threshold
        negative_mask = ~positive_mask  # All non-positive pairs are negative
        diagonal_mask = torch.eye(batch_size, device=self.device, dtype=torch.bool)

        positive_mask = positive_mask & ~diagonal_mask
        negative_mask = negative_mask & ~diagonal_mask

        stats = {}

        # Positive pair statistics
        if positive_mask.any():
            pos_indices = torch.where(positive_mask)
            i_indices, j_indices = pos_indices[0], pos_indices[1]

            # Cross-paired errors for positive samples
            cross_errors_ij = error_matrix[i_indices, j_indices]
            cross_errors_ji = error_matrix[j_indices, i_indices]

            stats['positive_cross_error_mean'] = (cross_errors_ij.mean() + cross_errors_ji.mean()).item() / 2
            stats['positive_cross_error_std'] = (cross_errors_ij.std() + cross_errors_ji.std()).item() / 2
            stats['positive_pairs_count'] = positive_mask.sum().item()
        else:
            stats['positive_cross_error_mean'] = 0.0
            stats['positive_cross_error_std'] = 0.0
            stats['positive_pairs_count'] = 0

        # Negative pair statistics
        if negative_mask.any():
            neg_indices = torch.where(negative_mask)
            i_indices, j_indices = neg_indices[0], neg_indices[1]

            cross_errors_ij = error_matrix[i_indices, j_indices]
            cross_errors_ji = error_matrix[j_indices, i_indices]

            stats['negative_cross_error_mean'] = (cross_errors_ij.mean() + cross_errors_ji.mean()).item() / 2
            stats['negative_cross_error_std'] = (cross_errors_ij.std() + cross_errors_ji.std()).item() / 2
            stats['negative_pairs_count'] = negative_mask.sum().item()
        else:
            stats['negative_cross_error_mean'] = 0.0
            stats['negative_cross_error_std'] = 0.0
            stats['negative_pairs_count'] = 0

        # Overall statistics
        stats['total_pairs'] = batch_size * batch_size - batch_size  # Exclude diagonal
        stats['positive_ratio'] = stats['positive_pairs_count'] / stats['total_pairs'] if stats['total_pairs'] > 0 else 0.0
        stats['negative_ratio'] = stats['negative_pairs_count'] / stats['total_pairs'] if stats['total_pairs'] > 0 else 0.0

        return stats
    
    def _compute_cross_pairing_contrastive_loss(
        self,
        error_matrix: torch.Tensor,
        dtw_similarity: torch.Tensor
    ) -> torch.Tensor:
        """
        Compute DTW-guided contrastive loss following original ContrastiveLoss design

        Design philosophy (following original DC ContrastiveLoss):
        1. Convert errors to similarity logits: logits = scale * exp(-errors/temp)
        2. Create DTW-guided target matrix (diagonal + DTW positive pairs)
        3. Use cross-entropy loss for contrastive learning
        4. Add optional diagonal error weight

        Args:
            error_matrix: (B, B) diffusion error matrix
            dtw_similarity: (B, B) DTW similarity matrix

        Returns:
            Scalar contrastive loss
        """
        batch_size = error_matrix.shape[0]

        # 1. Convert errors to similarity logits (following original design)
        # logits = scale * exp(-errors/temp) - lower error = higher similarity
        logits = self.scale * torch.exp(-error_matrix / self.temperature)  # (B, B)

        # 2. Create DTW-guided target matrix
        targets = torch.zeros_like(dtw_similarity, device=self.device)

        # 2a. Diagonal elements are always positive (self-pairs)
        diagonal_mask = torch.eye(batch_size, device=self.device, dtype=torch.bool)
        targets[diagonal_mask] = 1.0

        # 2b. DTW positive pairs are also positive targets
        positive_mask = dtw_similarity > self.positive_threshold
        off_diagonal_positive_mask = positive_mask & ~diagonal_mask
        targets[off_diagonal_positive_mask] = dtw_similarity[off_diagonal_positive_mask]
        # print(f"Targets: {targets}")
        # print(f'a line of targets: {targets[0]}')
        # pdb.set_trace()
        #targets[off_diagonal_positive_mask] = 1.0

        # print(f"Targets: {targets}")
        # pdb.set_trace()

        # 2c. All other elements remain 0 (negative targets)

        # 3. Compute cross-entropy loss with DTW-guided targets
        # Note: We use binary cross-entropy since targets are continuous [0,1]
        loss = F.binary_cross_entropy_with_logits(
            logits, targets, reduction='mean'
        )

        # 4. Optional: add diagonal error weight (following original design)
        if self.diagonal_weight > 0:
            diagonal_errors = torch.diag(error_matrix)
            loss += self.diagonal_weight * diagonal_errors.mean()

        return loss
    
    def _compute_diagonal_loss(self, error_matrix: torch.Tensor) -> torch.Tensor:
        """
        Compute diagonal loss to encourage low error for self-pairs
        
        This ensures that each action sequence has low error when paired 
        with its corresponding observation.
        """
        diagonal_errors = torch.diag(error_matrix)
        return diagonal_errors.mean()



    def get_similarity_statistics(
        self,
        error_matrix: torch.Tensor,
        dtw_similarity: torch.Tensor
    ) -> Dict[str, float]:
        """
        Compute statistics for monitoring cross-pairing contrastive learning progress
        """
        # Get cross-pairing specific statistics
        cross_stats = self._compute_cross_pairing_statistics(error_matrix, dtw_similarity)

        # Add general DTW statistics
        general_stats = {
            'dtw_similarity_mean': dtw_similarity.mean().item(),
            'dtw_similarity_std': dtw_similarity.std().item(),
            'error_matrix_mean': error_matrix.mean().item(),
            'error_matrix_std': error_matrix.std().item(),
            'diagonal_error_mean': torch.diag(error_matrix).mean().item(),
            'diagonal_error_std': torch.diag(error_matrix).std().item()
        }

        # Combine all statistics
        stats = {**cross_stats, **general_stats}

        return stats


# Episode-aware DTW loss has been removed as requested
