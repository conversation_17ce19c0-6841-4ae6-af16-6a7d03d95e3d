"""
DC DDPM Agent for MaIL project.
Implements SoftREPA-style contrastive learning with DC tokens for robot action diffusion.
Based on lerobot-add_transformer's transformer_dc_sampler.py implementation.
"""

import os
import torch
import torch.nn as nn
import torch.nn.functional as F
import einops
import numpy as np
from tqdm import tqdm
from typing import Optional, List, Tuple, Dict, Any
from collections import deque
import logging
import wandb
import pdb
import math

from omegaconf import DictConfig
import hydra

from agents.base_agent import BaseAgent
from agents.models.oc_ddpm.ema import ExponentialMovingAverage
from agents.models.goal_ddpm.oc_ddpm_dc import DiffusionEncDec
from agents.dtw_contrastive_loss import DTWGuidedContrastiveLoss

# A logger for this file
log = logging.getLogger(__name__)


class DiffusionPolicy_DC(nn.Module):
    """
    DC-enhanced DiffusionPolicy for contrastive learning.
    Extends the standard DiffusionPolicy with DC tokens functionality.
    """

    def __init__(self, model: DictConfig, obs_encoder: DictConfig,
                 visual_input: bool = True, device: str = "cpu"):
        super(DiffusionPolicy_DC, self).__init__()

        self.visual_input = visual_input
        self.device = device

        # Initialize encoders
        self.obs_encoder = hydra.utils.instantiate(obs_encoder).to(device)
        self.linear = nn.Linear(512, 256)

        # Initialize DC-enhanced diffusion model
        self.model = hydra.utils.instantiate(model).to(device)

        # Store DC configuration from the nested model (DiffusionEncDec)
        # The DC parameters are in model.model (DiffusionEncDec), not in model (Diffusion)
        diffusion_model = self.model.model  # This is the DiffusionEncDec instance
        self.n_dc_tokens = getattr(diffusion_model, 'n_dc_tokens', 0)
        self.n_dc_layers = getattr(diffusion_model, 'n_dc_layers', 0)
        self.n_decoder_dc_tokens = getattr(diffusion_model, 'n_decoder_dc_tokens', 0)
        self.n_decoder_dc_layers = getattr(diffusion_model, 'n_decoder_dc_layers', 0)
        self.use_dc = getattr(diffusion_model, 'use_dc', False)
        self.use_decoder_dc = getattr(diffusion_model, 'use_decoder_dc', False)

        # Dispersive loss parameters
        self.use_dispersive_in_diffusion = False
        self.dispersive_weight = 0.0

        log.info(f"✅ Created DiffusionPolicy_DC with:")
        log.info(f"  - Encoder DC: {self.n_dc_tokens} tokens, {self.n_dc_layers} layers")
        log.info(f"  - Decoder DC: {self.n_decoder_dc_tokens} tokens, {self.n_decoder_dc_layers} layers")
        log.info(f"  - Use DC: encoder={self.use_dc}, decoder={self.use_decoder_dc}")

    def forward(self, inputs, goal, action=None, if_train=False, if_return_obs=False):
        """
        Forward pass with optional DC token usage.
        """
        # Encode observations
        obs = self._encode_observations(inputs, goal)

        if if_train:
            return self.model.loss(action, obs, goal)

        # Make prediction
        pred = self.model(obs, goal)

        if if_return_obs:
            return pred, obs

        return pred

    def _encode_observations(self, inputs, goal):
        """Encode visual and state observations"""
        if self.visual_input:
            agentview_image, in_hand_image, goal_imgs = inputs

            B, T, C, H, W = agentview_image.size()

            agentview_image = agentview_image.view(B * T, C, H, W)
            in_hand_image = in_hand_image.view(B * T, C, H, W)

            goal_emb = self.linear(goal_imgs)

            obs_dict = {
                "agentview_rgb": agentview_image,
                "eye_in_hand_rgb": in_hand_image,
            }

            obs = self.obs_encoder(obs_dict)
            obs = obs.view(B, T, -1)

            # Concatenate goal embedding with observations
            obs = torch.cat((goal_emb, obs), dim=1)
        else:
            obs = self.obs_encoder(inputs)

        return obs

    def compute_contrastive_error(self, batch: Dict[str, torch.Tensor], use_dc: bool = True,episode_indices: torch.Tensor = None) -> torch.Tensor:
        """
        Compute contrastive error matrix for SoftREPA-style training.

        Args:
            batch: Dictionary containing 'action' and 'obs' data (goal is embedded in obs)
            use_dc: Whether to use DC tokens
            episode_indices: episode index for each sample (B,)
        Returns:
            (B, B) error matrix where error[i,j] is the prediction error
            between action_i and observation_j
        """
        # Create extended batch for all action-observation pairs
        extended_batch = self._create_extended_batch(batch,episode_indices)

        # Compute diffusion losses for all pairs (now returns tuple)
        losses, _ = self._compute_batch_diffusion_losses(extended_batch, use_dc)

        # Reshape to contrastive matrix
        batch_size = batch["action"].shape[0]
        error_matrix = losses.reshape(batch_size, batch_size)

        return error_matrix

    def compute_contrastive_error_with_components(self, batch: Dict[str, torch.Tensor], use_dc: bool = True, episode_indices: torch.Tensor = None) -> tuple:
        """
        Compute contrastive error matrix with separate diffusion loss components for detailed monitoring.

        Args:
            batch: Dictionary containing 'action' and 'obs' data (goal is embedded in obs)
            use_dc: Whether to use DC tokens
            episode_indices: episode index for each sample (B,)
        Returns:
            Tuple of:
            - error_matrix: (B, B) error matrix where error[i,j] is the prediction error
            - raw_diffusion_losses: (B*B,) raw diffusion losses for each action-observation pair
            - diagonal_diffusion_losses: (B,) diffusion losses for positive pairs (diagonal elements)
            - features: Optional features tensor for analysis
        """
        # Create extended batch for all action-observation pairs
        extended_batch = self._create_extended_batch(batch, episode_indices)

        # Compute diffusion losses for all pairs (now returns tuple)
        raw_diffusion_losses, features = self._compute_batch_diffusion_losses(extended_batch, use_dc)

        # Reshape to contrastive matrix
        batch_size = batch["action"].shape[0]
        error_matrix = raw_diffusion_losses.reshape(batch_size, batch_size)

        # Extract diagonal elements (positive pair diffusion losses)
        diagonal_diffusion_losses = torch.diag(error_matrix)

        return error_matrix, raw_diffusion_losses, diagonal_diffusion_losses, features

    def _create_extended_batch(self, batch: Dict[str, torch.Tensor],episode_indices: torch.Tensor = None) -> Dict[str, torch.Tensor]:
        """
        Create extended batch for contrastive learning.
        Each action is paired with all observations.

        Note: Only processes obs and action since goal is already embedded in obs.
        """
        batch_size = batch["action"].shape[0]

        # Expand actions: each action repeated for all observations
        actions = batch["action"]  # (B, horizon, action_dim)
        expanded_actions = actions.unsqueeze(1).repeat(1, batch_size, 1, 1)  # (B, B, horizon, action_dim)
        expanded_actions = expanded_actions.reshape(batch_size * batch_size, *actions.shape[1:])  # (B*B, horizon, action_dim)

        # Expand observations (goal is already embedded in obs)
        obs = batch["obs"]  # (B, seq_len, obs_dim)
        expanded_obs = obs.unsqueeze(0).repeat(batch_size, 1, *([1] * (obs.dim() - 1)))  # (B, B, seq_len, obs_dim)
        expanded_obs = expanded_obs.reshape(batch_size * batch_size, *obs.shape[1:])  # (B*B, seq_len, obs_dim)
        
        '''shape of expanded_actions: torch.Size([8100, 5, 7])
        shape of expanded_obs: torch.Size([8100, 6, 256])'''
        
        # print(f'shape of expanded_actions: {expanded_actions.shape}')
        # print(f'shape of expanded_obs: {expanded_obs.shape}')
        # pdb.set_trace()

        # Construct extended batch
        extended_batch = {
            "action": expanded_actions,
            "obs": expanded_obs
        }

        return extended_batch
    def _compute_batch_diffusion_losses(self, batch: Dict[str, torch.Tensor], use_dc: bool = True) -> Tuple[torch.Tensor, Optional[torch.Tensor]]:
        """
        Compute enhanced diffusion losses for a batch of action-observation pairs.
        Integrates Dispersive Loss directly into diffusion loss computation using existing model features.

        Args:
            batch: Extended batch containing action-observation pairs for contrastive learning
                   Only contains 'action' and 'obs' keys (goal is embedded in obs)
            use_dc: Whether to use DC tokens (passed to underlying model)

        Returns:
            Tuple[torch.Tensor, Optional[torch.Tensor]]: 
                - Per-sample enhanced losses of shape (B*B,) for contrastive matrix
                - Features tensor for additional analysis (optional)
        """
        # Extract data from extended batch
        actions = batch["action"]  # (B*B, horizon, action_dim)
        obs = batch["obs"]        # (B*B, seq_len, obs_dim) - goal already embedded
        goals = None              # Goal is already embedded in obs, so set to None

        batch_size = len(actions)  # This is B*B for extended batch
        
        # Generate random timesteps for each sample in the extended batch
        t = torch.randint(0, self.model.n_timesteps, (batch_size,), device=actions.device).long()

        # Manually compute per-sample losses (similar to p_losses but without averaging)
        noise = torch.randn_like(actions)
        x_noisy = self.model.q_sample(x_start=actions, t=t, noise=noise)
        
        # 🔥 使用 return_features=True 获取模型特征（如果启用 Dispersive Loss）
        if self.use_dispersive_in_diffusion:
            try:
                # 获取模型输出和特征（启用return_features）
                x_recon, features = self.model.model(
                    x_noisy, t, obs, goals, return_features=True
                )
            except Exception as e:
                log.warning(f"⚠️ 无法获取模型特征，使用标准前向传播: {e}")
                x_recon = self.model.model(x_noisy, t, obs, goals)
                features = None
        else:
            # 标准模型调用
            x_recon = self.model.model(x_noisy, t, obs, goals)
            features = None

        # Compute per-sample diffusion losses without averaging
        if self.model.predict_epsilon:
            # Compare predicted noise with actual noise
            loss_per_element = torch.nn.functional.mse_loss(x_recon, noise, reduction='none')
        else:
            # Compare predicted action with actual action
            loss_per_element = torch.nn.functional.mse_loss(x_recon, actions, reduction='none')

        # Average over action dimensions and sequence length, but keep batch dimension
        base_loss_per_sample = loss_per_element.view(batch_size, -1).mean(dim=1)  # (B*B,)

        # 🔥 如果启用了 Dispersive Loss 集成，添加 dispersive loss
        if self.use_dispersive_in_diffusion and features is not None:
            # 优先调用模型内置的dispersion实现（已对齐d2ppo风格）
            if hasattr(self.model.model, 'disp_loss'):
                try:
                    # 若是encoder特征，扩展批次为B*B，选取前B个样本用于分散损失
                    selected = features
                    try:
                        if isinstance(features, (list, tuple)) and len(features) > 0 and isinstance(features[-1], torch.Tensor):
                            f = features[-1]
                        elif isinstance(features, torch.Tensor):
                            f = features
                        else:
                            f = None
                        if f is not None and f.shape[0] == batch_size:
                            ori_B = int(math.isqrt(batch_size))
                            if ori_B * ori_B == batch_size and ori_B > 1:
                                selected = f[:ori_B]
                            else:
                                selected = f
                    except Exception:
                        selected = features

                    dispersive_loss = self.model.model.disp_loss(selected)
                    

                    # Store the dispersive loss for later retrieval
                    # Create the attribute if it doesn't exist
                    self.model.model.last_disp_loss = dispersive_loss.item()


                    # Store the dispersive loss for later retrieval
                    # Create the attribute if it doesn't exist
                    self.model.model.last_disp_loss = dispersive_loss.item()

                    enhanced_loss_per_sample = base_loss_per_sample + self.dispersive_weight * dispersive_loss
                    if wandb.run is not None:
                        wandb.log({
                            'train/base_diffusion_loss_mean': base_loss_per_sample.mean().item(),
                            'train/dispersive_loss': dispersive_loss.item(),
                            'train/dispersive_weight': self.dispersive_weight,
                            'train/enhanced_diffusion_loss_mean': enhanced_loss_per_sample.mean().item()
                        })
                    return enhanced_loss_per_sample, features
                except Exception as e:
                    log.warning(f"⚠️ Dispersive Loss 计算失败: {e}")
                    return base_loss_per_sample, features
            else:
                log.warning("⚠️ 模型不支持 disp_loss 方法，使用基础扩散损失")
                return base_loss_per_sample, features
        else:
            return base_loss_per_sample, features

    def initialize_dc_tokens(self):
        """Initialize DC tokens with small random values"""
        if hasattr(self.model.model, 'dc_tokens') and self.model.model.dc_tokens is not None:
            nn.init.normal_(self.model.model.dc_tokens, mean=0, std=0.02)
            log.info("✅ Initialized encoder DC tokens")

        if hasattr(self.model.model, 'dc_t_tokens') and self.model.model.dc_t_tokens is not None:
            nn.init.normal_(self.model.model.dc_t_tokens.weight, mean=0, std=0.02)
            log.info("✅ Initialized encoder DC_T tokens")

        if hasattr(self.model.model, 'decoder_dc_tokens') and self.model.model.decoder_dc_tokens is not None:
            nn.init.normal_(self.model.model.decoder_dc_tokens, mean=0, std=0.02)
            log.info("✅ Initialized decoder DC tokens")

        if hasattr(self.model.model, 'decoder_dc_t_tokens') and self.model.model.decoder_dc_t_tokens is not None:
            nn.init.normal_(self.model.model.decoder_dc_t_tokens.weight, mean=0, std=0.02)
            log.info("✅ Initialized decoder DC_T tokens")

    def freeze_base_model(self):
        """Freeze all parameters except DC tokens (for SoftREPA training)"""
        frozen_count = 0
        trainable_count = 0

        for name, param in self.named_parameters():
            # Check if this is a DC-related parameter
            is_dc_param = (
                'dc_token' in name or
                'dc_t_token' in name
            )

            if is_dc_param:
                param.requires_grad = True
                trainable_count += param.numel()
                log.debug(f"Trainable DC param: {name} - {param.shape}")
            else:
                param.requires_grad = False
                frozen_count += param.numel()

        log.info(f"🔒 Frozen {frozen_count:,} base model parameters")
        log.info(f"🔓 Trainable {trainable_count:,} DC parameters")
        log.info(f"📊 Training efficiency: {trainable_count/(frozen_count+trainable_count)*100:.2f}% of total params")

    def get_dc_parameters(self) -> List[torch.nn.Parameter]:
        """Get only DC-related parameters for optimization"""
        dc_params = []
        for name, param in self.named_parameters():
            if ('dc_token' in name or 'dc_t_token' in name) and param.requires_grad:
                dc_params.append(param)
        return dc_params

    def print_parameter_stats(self):
        """Print detailed parameter statistics"""
        total_params = 0
        trainable_params = 0
        dc_params = 0

        log.info("Parameter Statistics:")
        log.info("-" * 50)

        for name, param in self.named_parameters():
            param_count = param.numel()
            total_params += param_count

            if param.requires_grad:
                trainable_params += param_count

            if 'dc' in name:
                dc_params += param_count
                log.info(f"DC Parameter: {name}")
                log.info(f"  Shape: {param.shape}")
                log.info(f"  Count: {param_count:,}")
                log.info(f"  Trainable: {param.requires_grad}")

        log.info("-" * 50)
        log.info(f"Total parameters: {total_params:,}")
        log.info(f"Trainable parameters: {trainable_params:,}")
        log.info(f"DC parameters: {dc_params:,}")
        log.info(f"Frozen parameters: {total_params - trainable_params:,}")

    def get_params(self):
        return self.parameters()


class ContrastiveLoss(nn.Module):
    """
    Contrastive loss for action-observation alignment.
    Adapted from SoftREPA for robot action sequences.
    """

    def __init__(self, temp: float = 0.07, scale: float = 4.0, dweight: float = 0.0, device: str = 'cuda'):
        super().__init__()
        self.device = device
        self.temp = nn.Parameter(torch.tensor(temp, device=device))
        self.scale = nn.Parameter(torch.tensor(scale, device=device))
        self.dweight = dweight  # diffusion loss weight

    def get_mask(self, shape: Tuple[int, int]) -> torch.Tensor:
        """Create positive sample mask (diagonal)"""
        mask = torch.zeros(shape, device=self.device)
        n_b, _ = shape
        index = torch.arange(n_b, device=self.device)
        mask[index, index] = 1
        return mask

    def forward(self, errors: torch.Tensor) -> torch.Tensor:
        """
        Compute contrastive loss from prediction errors.

        Args:
            errors: (B, B) error matrix where errors[i,j] is the error
                   between action_i and condition_j

        Returns:
            Scalar contrastive loss
        """
        batch_size = errors.shape[0]
        # print(f'shape of errors: {errors.shape}')
        # pdb.set_trace()
        

        # Convert errors to similarity scores (lower error = higher similarity)
        # Following SoftREPA: logits = scale * exp(-errors/temp)
        logits = self.scale * torch.exp(-errors / self.temp)  # (B, B)

        # Create target labels (diagonal elements are positive pairs)
        #targets = torch.arange(batch_size, device=errors.device)  # [0, 1, 2, ..., B-1]

        masks = self.get_mask(shape=errors.shape)
        targets = masks.clone()

        # logsumexp = torch.logsumexp(logits, dim=1)

        # # Cross-entropy loss for contrastive learning
        # loss = logsumexp.mean()
        loss = F.cross_entropy(logits, targets)

        # Optional: add diffusion loss weight for diagonal elements
        if self.dweight > 0:
            diagonal_errors = torch.diag(errors)
            loss += self.dweight * diagonal_errors.mean()

        return loss


class DiffusionAgent_DC(BaseAgent):
    """
    DC-enhanced DiffusionAgent for contrastive learning.
    Extends the standard DiffusionAgent with SoftREPA-style DC tokens functionality.
    """

    def __init__(
            self,
            model: DictConfig,
            trainset: DictConfig,
            valset: DictConfig,
            train_batch_size: int,
            val_batch_size: int,
            num_workers: int,
            device: str,
            epoch: int,
            scale_data: bool,
            eval_every_n_epochs: int,
            # Additional parameters not in BaseAgent
            optimization: DictConfig,
            discount: float = 0.99,
            use_ema: bool = True,
            decay: float = 0.995,
            update_ema_every_n_steps: int = 1,
            goal_window_size: int = 1,
            window_size: int = 10,
            diffusion_kde: bool = False,
            diffusion_kde_samples: int = 100,
            goal_conditioned: bool = False,
            # Sequence length parameters (from original DiffusionAgent)
            obs_seq_len: int = 5,
            action_seq_size: int = 5,
            pred_last_action_only: bool = False,
            # DC-specific parameters from config
            contrastive_temp: float = 0.07,
            contrastive_scale: float = 4.0,
            contrastive_dweight: float = 0.0,
            dc_training_steps: int = 1000,
            dc_eval_freq: int = 100,
            dc_save_freq: int = 500,
            dc_init_std: float = 0.02,
            freeze_base_model: bool = True,
            dc_only_training: bool = True,
            pretrained_model_path: Optional[str] = None,
            load_pretrained_weights: bool = False,
            use_mixed_precision: bool = False,
            gradient_clip_norm: float = 1.0,
            warmup_steps: int = 100,
            log_dc_stats: bool = True,
            log_contrastive_matrix: bool = False,
            save_dc_visualizations: bool = False,
            # Batch selection parameters
            use_batch_selection: bool = False,
            batch_selection_n_samples: Optional[int] = None,
            batch_selection_method: str = 'greedy',
            batch_selection_distance_metric: str = 'l2',
            window_before: int = 0,
            window_after: int = 0,
            hard_negative_weight: float = 2.0,
            # DTW contrastive learning parameters
            use_dtw_contrastive: bool = False,
            dtw_alignment_weight: float = 1.0,
            dtw_contrastive_weight: float = 0.5,
            dtw_positive_threshold: float = 0.1,
            **kwargs  # Catch any additional parameters
    ):
        # Store additional parameters not handled by BaseAgent
        self.optimization = optimization

        # Initialize base agent (only pass parameters that BaseAgent accepts)
        super().__init__(
            model=model,
            trainset=trainset,
            valset=valset,
            train_batch_size=train_batch_size,
            val_batch_size=val_batch_size,
            num_workers=num_workers,
            device=device,
            epoch=epoch,
            scale_data=scale_data,
            eval_every_n_epochs=eval_every_n_epochs
        )

        # Store DC-specific parameters
        self.discount = discount
        self.use_ema = use_ema
        self.decay = decay
        self.update_ema_every_n_steps = update_ema_every_n_steps
        self.goal_window_size = goal_window_size
        self.window_size = window_size
        self.diffusion_kde = diffusion_kde
        self.diffusion_kde_samples = diffusion_kde_samples
        self.goal_conditioned = goal_conditioned

        # Sequence length parameters (from original DiffusionAgent)
        self.obs_seq_len = obs_seq_len
        self.action_seq_size = action_seq_size
        self.pred_last_action_only = pred_last_action_only

        # DC training parameters
        self.dc_training_steps = dc_training_steps
        self.dc_eval_freq = dc_eval_freq
        self.dc_save_freq = dc_save_freq
        self.dc_init_std = dc_init_std
        self.freeze_base_model_flag = freeze_base_model
        self.dc_only_training = dc_only_training
        self.pretrained_model_path = pretrained_model_path
        self.load_pretrained_weights = load_pretrained_weights
        self.use_mixed_precision = use_mixed_precision
        self.gradient_clip_norm = gradient_clip_norm
        self.warmup_steps = warmup_steps
        self.log_dc_stats = log_dc_stats
        self.log_contrastive_matrix = log_contrastive_matrix
        self.save_dc_visualizations = save_dc_visualizations

        # Batch selection parameters
        self.use_batch_selection = use_batch_selection
        self.batch_selection_n_samples = batch_selection_n_samples
        self.batch_selection_method = batch_selection_method
        self.batch_selection_distance_metric = batch_selection_distance_metric

        # Store contrastive loss parameters for later reference
        self.contrastive_temp = contrastive_temp
        self.contrastive_scale = contrastive_scale
        self.contrastive_dweight = contrastive_dweight
        self.window_before = window_before
        self.window_after = window_after
        self.hard_negative_weight = hard_negative_weight

        # DTW contrastive learning parameters
        self.use_dtw_contrastive = use_dtw_contrastive
        self.dtw_alignment_weight = dtw_alignment_weight
        self.dtw_contrastive_weight = dtw_contrastive_weight
        self.dtw_positive_threshold = dtw_positive_threshold

        # Initialize contrastive loss based on DTW availability
        if self.use_dtw_contrastive:
            log.info("🔥 Using DTW-guided contrastive learning")
            self.contrastive_loss = DTWGuidedContrastiveLoss(
                temperature=contrastive_temp,
                scale=contrastive_scale,
                alignment_weight=dtw_alignment_weight,
                contrastive_weight=dtw_contrastive_weight,
                diagonal_weight=contrastive_dweight,
                hard_negative_weight=hard_negative_weight,
                positive_threshold=dtw_positive_threshold,
                device=device
            )
        else:
            log.info("🔥 Using standard episode-aware contrastive learning")
            # Fallback to original contrastive loss
            self.contrastive_loss = IntegratedEpisodeContrastiveLoss(
                temp=contrastive_temp,
                scale=contrastive_scale,
                dweight=contrastive_dweight,
                device=device,
                window_before=window_before,
                window_after=window_after,
                hard_negative_weight=hard_negative_weight
            )

        # Define the bounds for the sampler class (from original implementation)
        self.model.model.min_action = torch.from_numpy(self.scaler.y_bounds[0, :]).to(self.device)
        self.model.model.max_action = torch.from_numpy(self.scaler.y_bounds[1, :]).to(self.device)

        # Model save names (from original implementation)
        self.eval_model_name = "eval_best_ddpm.pth"
        self.last_model_name = "last_ddpm.pth"

        # Initialize optimizer (from original implementation)
        self.optimizer = hydra.utils.instantiate(
            self.optimization, params=self.model.parameters()
        )

        # Initialize step counter (from original implementation)
        self.steps = 0

        # Initialize EMA helper only if EMA is enabled
        if self.use_ema:
            self.ema_helper = ExponentialMovingAverage(self.model.parameters(), decay, self.device)
            self.ema = ExponentialMovingAverage(self.model.parameters(), decay=self.decay)

        # 🔥 Initialize Dispersive Loss configuration (will be set from config)
        self.use_dispersive_loss = False
        self.dispersive_weight = 0.25
        self.dispersive_in_diffusion = True
        self.dispersive_feature_layer = "last"

    def configure_dispersive_loss(self, cfg):
        """Configure Dispersive Loss from config"""
        contrastive_cfg = cfg.dc_training.contrastive
        
        # 设置 Dispersive Loss 配置
        self.use_dispersive_loss = contrastive_cfg.get('use_disperse_loss', False)
        self.dispersive_weight = contrastive_cfg.get('disperse_weight', 0.25)
        self.dispersive_in_diffusion = contrastive_cfg.get('disperse_in_diffusion', True)
        self.dispersive_feature_layer = contrastive_cfg.get('disperse_feature_layer', 'last')
        # 同步用于前向分支控制的标志（agent级）
        self.use_dispersive_in_diffusion = bool(self.dispersive_in_diffusion and self.use_dispersive_loss)
        
        # 将配置传递给模型
        if hasattr(self.model, 'use_dispersive_in_diffusion'):
            self.model.use_dispersive_in_diffusion = self.use_dispersive_in_diffusion
            self.model.dispersive_weight = self.dispersive_weight
            self.model.dispersive_feature_layer = self.dispersive_feature_layer
            
            # NEW: 配置模型的特征提取为 encoder late
            if hasattr(self.model.model, 'feature_extraction_mode'):
                # 仅当需要在扩散中集成disperse时，启用特征提取
                if self.use_dispersive_in_diffusion:
                    try:
                        self.model.model.feature_extraction_mode = 'encoder'
                        # late layer: -1
                        if hasattr(self.model.model, 'encoder_feature_layers'):
                            self.model.model.encoder_feature_layers = [-1]
                    except Exception as e:
                        log.warning(f"⚠️ 配置encoder特征提取失败: {e}")
            
            log.info(f"✅ 配置 Dispersive Loss:")
            log.info(f"  - 启用状态: {self.use_dispersive_loss}")
            log.info(f"  - 集成到扩散损失: {self.dispersive_in_diffusion}")
            log.info(f"  - 权重: {self.dispersive_weight}")
            log.info(f"  - 特征层: {self.dispersive_feature_layer}")
        else:
            log.warning("⚠️ 模型不支持 Dispersive Loss 配置")

    def _display_dispersive_loss_status(self):
        """Display Dispersive Loss configuration status"""
        log.info("=" * 60)
        log.info("🎨 Dispersive Loss 配置状态")
        log.info("=" * 60)
        log.info(f"  启用状态: {self.use_dispersive_loss}")
        log.info(f"  权重系数: {self.dispersive_weight}")
        log.info(f"  集成位置: {'扩散损失中' if self.dispersive_in_diffusion else '对比损失中'}")
        log.info(f"  特征层级: {self.dispersive_feature_layer}")
        log.info("=" * 60)

        # Initialize mixed precision scaler if enabled
        if self.use_mixed_precision:
            from torch.cuda.amp import GradScaler
            self.scaler = GradScaler()

        log.info(f"✅ Created DiffusionAgent_DC with:")
        log.info(f"  - DC training steps: {self.dc_training_steps}")
        log.info(f"  - Contrastive temp: {self.contrastive_temp}")
        log.info(f"  - Contrastive scale: {self.contrastive_scale}")
        log.info(f"  - Use EMA: {self.use_ema}")
        log.info(f"  - Mixed precision: {self.use_mixed_precision}")
        log.info(f"  - Freeze base model: {self.freeze_base_model_flag}")
        log.info(f"  - DC only training: {self.dc_only_training}")
        log.info(f"  - Pretrained model: {self.pretrained_model_path}")
        log.info(f"  - Load pretrained: {self.load_pretrained_weights}")

    def setup_model(self):
        """Setup the DC-enhanced diffusion model"""
        # The model should already be a DiffusionPolicy_DC instance
        if not isinstance(self.model, DiffusionPolicy_DC):
            log.warning("Model is not DiffusionPolicy_DC, DC functionality may not work properly")

        # Load pretrained weights if specified
        if self.load_pretrained_weights and self.pretrained_model_path:
            self._load_pretrained_weights(self.pretrained_model_path)

        # Initialize DC tokens with specified std
        self._initialize_dc_tokens_with_config()

        # Freeze base model if specified
        if self.freeze_base_model_flag:
            self.model.freeze_base_model()

        # Print parameter statistics
        if self.log_dc_stats:
            self.model.print_parameter_stats()

        return self.model

    def _initialize_dc_tokens_with_config(self):
        """Initialize DC tokens using configuration parameters"""
        if hasattr(self.model.model.model, 'dc_tokens') and self.model.model.model.dc_tokens is not None:
            nn.init.normal_(self.model.model.model.dc_tokens, mean=0, std=self.dc_init_std)
            log.info(f"✅ Initialized encoder DC tokens with std={self.dc_init_std}")

        if hasattr(self.model.model.model, 'dc_t_tokens') and self.model.model.model.dc_t_tokens is not None:
            nn.init.normal_(self.model.model.model.dc_t_tokens.weight, mean=0, std=self.dc_init_std)
            log.info(f"✅ Initialized encoder DC_T tokens with std={self.dc_init_std}")

        if hasattr(self.model.model.model, 'decoder_dc_tokens') and self.model.model.model.decoder_dc_tokens is not None:
            nn.init.normal_(self.model.model.model.decoder_dc_tokens, mean=0, std=self.dc_init_std)
            log.info(f"✅ Initialized decoder DC tokens with std={self.dc_init_std}")

        if hasattr(self.model.model.model, 'decoder_dc_t_tokens') and self.model.model.model.decoder_dc_t_tokens is not None:
            nn.init.normal_(self.model.model.model.decoder_dc_t_tokens.weight, mean=0, std=self.dc_init_std)
            log.info(f"✅ Initialized decoder DC_T tokens with std={self.dc_init_std}")

    def train_dc_tokens(self, pretrained_model_path: Optional[str] = None):
        """
        Train DC tokens using SoftREPA-style contrastive learning.

        Args:
            pretrained_model_path: Path to pretrained model checkpoint (optional)
        """
        log.info("🚀 Starting DC tokens training...")

        # Load pretrained weights if provided
        if pretrained_model_path:
            self._load_pretrained_weights(pretrained_model_path)

        # Setup model for DC training
        self.setup_model()

        # Freeze base model and only train DC tokens
        self.model.freeze_base_model()

        # Setup optimizer for DC parameters only
        dc_params = self.model.get_dc_parameters()
        if not dc_params:
            log.error("❌ No DC parameters found for training!")
            return

        optimizer = torch.optim.AdamW(dc_params, lr=self.optimization.lr)

        # Training loop
        self.model.train()
        step = 0

        train_loader = self.get_train_dataloader()

        with tqdm(total=self.dc_training_steps, desc="DC Training") as pbar:
            while step < self.dc_training_steps:
                for batch_idx, raw_batch in enumerate(train_loader):
                    if step >= self.dc_training_steps:
                        break


                    batch = self._convert_batch_format(raw_batch)

                    # Move batch to device
                    batch = self._move_batch_to_device(batch)

                    # Create extended batch info for episode-aware contrastive learning
                    extended_batch_info = self._create_extended_batch_info(batch)
                    
                    # Compute contrastive error matrix
                    error_matrix = self.model.compute_contrastive_error(batch, use_dc=True)

                    # Compute contrastive loss (DTW-guided or standard)
                    if self.use_dtw_contrastive:
                        loss, loss_components = self._compute_dtw_guided_loss(
                            error_matrix, batch, extended_batch_info
                        )
                        mask = None  # DTW loss doesn't use mask
                    else:
                        # Standard episode-aware contrastive loss
                        loss, mask = self.contrastive_loss(error_matrix, extended_batch_info)

                    # Backward pass
                    optimizer.zero_grad()
                    loss.backward()
                    optimizer.step()

                    # Update EMA if enabled
                    if self.use_ema and step % self.update_ema_every_n_steps == 0:
                        self.ema.update()

                    # Logging
                    if step % 10 == 0:
                        if self.use_dtw_contrastive:
                            # DTW-guided loss logging
                            positive_pairs = loss_components.get('dtw_positive_pairs', 0).item()
                            total_pairs = error_matrix.numel()

                            pbar.set_postfix({
                                'loss': f'{loss.item():.4f}',
                                'align': f'{loss_components.get("alignment_loss", 0):.4f}',
                                'contr': f'{loss_components.get("contrastive_loss", 0):.4f}',
                                'dtw_pos': f'{positive_pairs}',
                                'step': step
                            })

                            if wandb.run is not None:
                                log_dict = {
                                    'dc_train/total_loss': loss.item(),
                                    'dc_train/dtw_positive_pairs': positive_pairs,
                                    'dc_train/step': step
                                }
                                # Add all loss components
                                for key, value in loss_components.items():
                                    if isinstance(value, torch.Tensor):
                                        log_dict[f'dc_train/{key}'] = value.item()
                                wandb.log(log_dict)
                        else:
                            # Standard loss logging
                            positive_pairs = mask.sum().item() if mask is not None else 0
                            total_pairs = mask.numel() if mask is not None else 0

                            pbar.set_postfix({
                                'loss': f'{loss.item():.4f}',
                                'pos_pairs': f'{positive_pairs}/{total_pairs}',
                                'step': step
                            })

                            if wandb.run is not None:
                                wandb.log({
                                    'dc_train/loss': loss.item(),
                                    'dc_train/positive_pairs': positive_pairs,
                                    'dc_train/total_pairs': total_pairs,
                                    'dc_train/positive_ratio': positive_pairs / total_pairs if total_pairs > 0 else 0,
                                    'dc_train/step': step
                                })

                    # Evaluation
                    if step % self.dc_eval_freq == 0 and step > 0:
                        self._evaluate_dc_model(step)

                    # Save checkpoint
                    if step % self.dc_save_freq == 0 and step > 0:
                        self._save_dc_checkpoint(step)

                    step += 1
                    pbar.update(1)

        log.info("✅ DC tokens training completed!")
    
    def _create_extended_batch_info(self, batch):
        """
        为episode-aware contrastive learning创建简化的batch信息

        Args:
            batch: 包含episode_indices的batch

        Returns:
            extended_batch_info: 简化的episode信息字典
        """
        episode_indices = batch.get('episode_indices', None)
        if episode_indices is None:
            return None

        extended_batch_info = {
            'original_episode_indices': episode_indices
        }

        return extended_batch_info

    def _compute_dtw_guided_loss(self, error_matrix, batch, extended_batch_info):
        """
        Compute DTW-guided contrastive loss

        Args:
            error_matrix: (B, B) diffusion error matrix
            batch: Batch containing slice indices
            extended_batch_info: Episode information

        Returns:
            Tuple of (loss, loss_components)
        """
        # Get DTW similarity matrix for current batch
        slice_indices = batch.get('slice_indices', None)
        if slice_indices is None:
            log.warning("⚠️ No slice indices found for DTW-guided loss, falling back to standard loss")
            return self.contrastive_loss(error_matrix, extended_batch_info)

        # Get DTW similarity matrix from dataset
        dtw_similarity = self.trainset.get_dtw_similarity_batch(slice_indices)
        if dtw_similarity is None:
            log.warning("⚠️ Failed to get DTW similarity matrix, falling back to standard loss")
            return self.contrastive_loss(error_matrix, extended_batch_info)

        # Move DTW similarity to device
        dtw_similarity = dtw_similarity.to(self.device)

        # Compute DTW-guided contrastive loss
        loss, loss_components = self.contrastive_loss(
            error_matrix, dtw_similarity, extended_batch_info
        )

        return loss, loss_components

    def _load_pretrained_weights(self, pretrained_model_path: str):
        """Load pretrained weights from checkpoint"""
        log.info(f"🔄 Loading pretrained weights from: {pretrained_model_path}")

        try:
            # Implementation depends on your checkpoint format
            # This is a placeholder - adapt based on your actual checkpoint structure
            checkpoint = torch.load(pretrained_model_path, map_location=self.device)

            if 'model_state_dict' in checkpoint:
                state_dict = checkpoint['model_state_dict']
            else:
                state_dict = checkpoint

            # Load non-DC parameters
            model_state_dict = self.model.state_dict()
            loaded_count = 0

            for name, param in model_state_dict.items():
                if 'dc' not in name.lower() and name in state_dict:
                    if param.shape == state_dict[name].shape:
                        param.data.copy_(state_dict[name].data)
                        loaded_count += 1

            log.info(f"✅ Loaded {loaded_count} pretrained parameters")

        except Exception as e:
            log.warning(f"⚠️ Could not load pretrained weights: {e}")
            log.info("Using randomly initialized weights")

    def _move_batch_to_device(self, batch: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """Move batch tensors to the specified device"""
        device_batch = {}
        for key, value in batch.items():
            if isinstance(value, torch.Tensor):
                device_batch[key] = value.to(self.device)
            else:
                device_batch[key] = value
        return device_batch

    def _evaluate_dc_model(self, step: int):
        """Evaluate DC model performance"""
        log.info(f"📊 Evaluating DC model at step {step}")

        self.model.eval()
        val_loader = self.get_val_dataloader()

        total_loss = 0.0
        num_batches = 0

        with torch.no_grad():
            for raw_batch in val_loader:

                batch = self._convert_batch_format(raw_batch)
                batch = self._move_batch_to_device(batch)

                # Compute contrastive error matrix
                error_matrix = self.model.compute_contrastive_error(batch, use_dc=True)

                # Compute contrastive loss
                loss = self.contrastive_loss(error_matrix)

                total_loss += loss.item()
                num_batches += 1

                # Limit evaluation batches
                if num_batches >= 10:
                    break

        avg_loss = total_loss / num_batches if num_batches > 0 else 0.0

        log.info(f"📈 Validation loss: {avg_loss:.4f}")

        if wandb.run is not None:
            wandb.log({
                'dc_val/loss': avg_loss,
                'dc_val/step': step
            })

        self.model.train()

    def _save_dc_checkpoint(self, step: int):
        """Save DC model checkpoint"""
        checkpoint_dir = f"checkpoints/dc_step_{step}"
        os.makedirs(checkpoint_dir, exist_ok=True)

        checkpoint = {
            'step': step,
            'model_state_dict': self.model.state_dict(),
            'contrastive_loss_state_dict': self.contrastive_loss.state_dict(),
        }

        if self.use_ema:
            checkpoint['ema_state_dict'] = self.ema.state_dict()

        checkpoint_path = os.path.join(checkpoint_dir, 'checkpoint.pth')
        torch.save(checkpoint, checkpoint_path)

        log.info(f"💾 Saved checkpoint to: {checkpoint_path}")

    def get_train_dataloader(self):
        """
        Get training dataloader that returns data in the same format as the original implementation.
        Returns batches with format: (bp_imgs, inhand_imgs, action, goal_imgs)
        where goal_imgs is actually task_embedding.
        """
        # Use the existing train_dataloader from BaseAgent
        # This ensures we get the same data format as the original implementation
        return self.train_dataloader

    def get_val_dataloader(self):
        """
        Get validation dataloader that returns data in the same format as the original implementation.
        Returns batches with format: (bp_imgs, inhand_imgs, action, goal_imgs)
        where goal_imgs is actually task_embedding.
        """
        # Use the existing test_dataloader from BaseAgent
        # This ensures we get the same data format as the original implementation
        return self.test_dataloader

    def _convert_batch_format(self, batch):
        """
        Convert batch from dataloader format to the format expected by DC training.
        Optionally applies batch selection to maximize diversity between samples.

        Input batch format (from MultiTaskDataset):
            - bp_imgs: agentview_rgb (B, T, C, H, W)
            - inhand_imgs: eye_in_hand_rgb (B, T, C, H, W)
            - action: action sequence (B, T, action_dim)
            - goal_imgs: task_embedding (B, 1, 512)
            - episode_indices: episode index for each sample (B,)

        Output format for DC training:
            - obs: concatenated observations with goal embedded (n_samples, obs_seq_len+1, obs_dim)
            - action: action sequence (n_samples, action_seq_len, action_dim)

        Args:
            batch: Input batch from dataloader
            n_samples: Number of samples to select for diversity (if None, uses full batch)
            batch_selection_method: Method for batch selection ('greedy', 'kmeans', 'uniform')
            distance_metric: Distance metric for greedy method ('l1', 'l2', 'cosine')

        Note: goal is already included in obs through _encode_observations, so no separate goal field needed.
        """
        from utils.frame_selection import apply_batch_selection

        # Handle both DTW-enabled and standard batch formats
        if self.use_dtw_contrastive and len(batch) == 6:
            # DTW-enabled format: (bp_imgs, inhand_imgs, action, goal_imgs, episode_indices, slice_indices)
            bp_imgs, inhand_imgs, action, goal_imgs, episode_indices, slice_indices = batch
        else:
            # Standard format: (bp_imgs, inhand_imgs, action, goal_imgs, episode_indices)
            bp_imgs, inhand_imgs, action, goal_imgs, episode_indices = batch
            slice_indices = None

        # Move to device and apply data scaling (following original implementation)
        bp_imgs = bp_imgs.to(self.device)
        inhand_imgs = inhand_imgs.to(self.device)
        goal_imgs = goal_imgs.to(self.device)

        # Scale action data
        action = self.scaler.scale_output(action)

        # # Apply batch selection for diversity if n_samples is specified
        # if n_samples is not None and n_samples < bp_imgs.shape[0]:
        #     log.info(f"🎯 Applying batch selection: {batch_selection_method} method, selecting {n_samples} samples from {bp_imgs.shape[0]}")
            
        #     # Create batch dictionary for selection
        #     batch_dict = {
        #         'bp_imgs': bp_imgs,
        #         'inhand_imgs': inhand_imgs,
        #         'action': action,
        #         'goal_imgs': goal_imgs
        #     }
            
        #     # Apply batch selection to reduce batch size while maintaining diversity
        #     selected_batch = apply_batch_selection(
        #         batch_dict, n_samples, 
        #         method=batch_selection_method, 
        #         distance_metric=distance_metric,
        #         reference_key='bp_imgs'  # Use visual data as reference for diversity
        #     )
            
        #     bp_imgs = selected_batch['bp_imgs']
        #     inhand_imgs = selected_batch['inhand_imgs']
        #     action = selected_batch['action']
        #     goal_imgs = selected_batch['goal_imgs']

        # Slice sequences according to configured lengths (following original implementation)
        # Action: start from obs_seq_len-1 to get the prediction targets
        action = action[:, self.obs_seq_len - 1:, :].contiguous()

        # Observations: take first obs_seq_len frames
        bp_imgs = bp_imgs[:, :self.obs_seq_len].contiguous()
        inhand_imgs = inhand_imgs[:, :self.obs_seq_len].contiguous()

        # Process observations through the model's encoder
        # This already includes goal embedding concatenated with visual observations
        inputs = (bp_imgs, inhand_imgs, goal_imgs)
        obs = self.model._encode_observations(inputs, goal_imgs)

        # Return obs, action, episode indices, and slice indices (for DTW)
        result = {
            "obs": obs,
            "action": action,
            "episode_indices": episode_indices
        }

        # Add slice indices for DTW contrastive learning
        if self.use_dtw_contrastive and slice_indices is not None:
            result["slice_indices"] = slice_indices

        return result

    def reset(self):
        """Reset the context of the model (from original DiffusionAgent)"""
        # Reset action counter for inference
        self.action_counter = self.action_seq_size

        # Clear any context queues if they exist
        if hasattr(self, 'obs_context'):
            self.obs_context.clear()
        if hasattr(self, 'bp_image_context'):
            self.bp_image_context.clear()
        if hasattr(self, 'inhand_image_context'):
            self.inhand_image_context.clear()
        if hasattr(self, 'des_robot_pos_context'):
            self.des_robot_pos_context.clear()
        if hasattr(self, 'goal_context'):
            self.goal_context.clear()

    @torch.no_grad()
    def load_pretrained_model(self, weights_path: str, sv_name=None, **kwargs) -> None:
        """
        Method to load a pretrained model weights inside self.model
        (from original DiffusionAgent)
        """
        model_path = os.path.join(weights_path, sv_name) if sv_name else weights_path
        # Fix for PyTorch 2.7+ weights_only security feature
        try:
            # Try loading with weights_only=True first (secure)
            checkpoint = torch.load(model_path, map_location=self.device, weights_only=True)
        except Exception as e:
            if "numpy.core.multiarray.scalar" in str(e) or "Weights only load failed" in str(e):
                # Fall back to weights_only=False for older checkpoints that contain numpy objects
                log.warning(f"⚠️  Loading checkpoint with weights_only=False due to numpy objects: {e}")
                checkpoint = torch.load(model_path, map_location=self.device, weights_only=False)
            else:
                raise e
        
        # Handle different checkpoint formats
        if isinstance(checkpoint, dict) and 'model_state_dict' in checkpoint:
            # This is a training checkpoint with metadata
            self.model.load_state_dict(checkpoint['model_state_dict'], strict=False)
            log.info(f"✅ Loaded model from training checkpoint (epoch: {checkpoint.get('epoch', 'unknown')})")
        else:
            # This is a direct model state dict
            self.model.load_state_dict(checkpoint, strict=False)

        # Reinitialize EMA helper with loaded parameters (only if EMA is enabled)
        if self.use_ema:
            self.ema_helper = ExponentialMovingAverage(self.model.parameters(), self.decay, self.device)
        log.info('✅ Loaded pre-trained model parameters')

    @torch.no_grad()
    def store_model_weights(self, store_path: str, sv_name=None) -> None:
        """
        Store the model weights inside the store path
        (from original DiffusionAgent)
        """
        os.makedirs(store_path, exist_ok=True)

        # Store EMA version if enabled
        if self.use_ema:
            self.ema_helper.store(self.model.parameters())
            self.ema_helper.copy_to(self.model.parameters())

        # Save model state
        model_path = os.path.join(store_path, sv_name) if sv_name else os.path.join(store_path, "model_weights.pth")
        torch.save(self.model.state_dict(), model_path)

        # Restore original parameters if EMA was used
        if self.use_ema:
            self.ema_helper.restore(self.model.parameters())

        # Also save non-EMA version
        torch.save(self.model.state_dict(), os.path.join(store_path, "non_ema_model_state_dict.pth"))

        log.info(f'💾 Stored model weights to: {model_path}')

    def train_step(self, state: torch.Tensor, action: torch.Tensor, goal: Optional[torch.Tensor] = None) -> float:
        """
        Standard training step (from original DiffusionAgent)
        Compatible with original training loop
        """
        self.model.train()

        # Scale goal if provided
        if goal is not None:
            goal = self.scaler.scale_input(goal)

        # Compute the loss
        loss = self.model(state, goal, action=action, if_train=True)

        # Backward pass
        self.optimizer.zero_grad()
        loss.backward()
        self.optimizer.step()

        self.steps += 1

        # Update EMA model
        if self.steps % self.update_ema_every_n_steps == 0:
            self.ema_helper.update(self.model.parameters())

        return loss

    @torch.no_grad()
    def evaluate(self, state: torch.tensor, action: torch.tensor, goal: Optional[torch.Tensor] = None) -> float:
        """
        Evaluation method (from original DiffusionAgent)
        """
        # Scale data
        state = self.scaler.scale_input(state)
        action = self.scaler.scale_output(action)

        # Slice sequences according to configured lengths
        action = action[:, self.obs_seq_len - 1:, :]
        state = state[:, :self.obs_seq_len, :]

        if goal is not None:
            goal = self.scaler.scale_input(goal)

        # Use EMA model for evaluation
        if self.use_ema:
            self.ema_helper.store(self.model.parameters())
            self.ema_helper.copy_to(self.model.parameters())

        self.model.eval()

        # Compute the loss
        loss = self.model.model.loss(action, state, goal)
        total_mse = loss.mean().item()

        # Restore original parameters
        if self.use_ema:
            self.ema_helper.restore(self.model.parameters())

        return total_mse

    @torch.no_grad()
    def predict(self, state: torch.Tensor, goal: Optional[torch.Tensor] = None, extra_args=None, if_vision=True) -> torch.Tensor:
        """
        Prediction method for inference (from original DiffusionAgent)
        """
        if if_vision:
            bp_image, inhand_image, goal_image = state

            # Convert to tensors and normalize
            bp_image = torch.from_numpy(bp_image).to(self.device).float().permute(2, 0, 1).unsqueeze(0) / 255.
            inhand_image = torch.from_numpy(inhand_image).to(self.device).float().permute(2, 0, 1).unsqueeze(0) / 255.
            goal_image = goal_image.to(self.device).unsqueeze(0)

            # Initialize context queues if not exist
            if not hasattr(self, 'bp_image_context'):
                from collections import deque
                self.bp_image_context = deque(maxlen=self.obs_seq_len)
                self.inhand_image_context = deque(maxlen=self.obs_seq_len)
                self.action_counter = self.action_seq_size

            # Add to context
            self.bp_image_context.append(bp_image)
            self.inhand_image_context.append(inhand_image)

            # Create sequences
            bp_image_seq = torch.stack(tuple(self.bp_image_context), dim=1)
            inhand_image_seq = torch.stack(tuple(self.inhand_image_context), dim=1)

            input_state = (bp_image_seq, inhand_image_seq, goal_image)
        else:
            obs = torch.from_numpy(state).float().to(self.device).unsqueeze(0)
            obs = self.scaler.scale_input(obs)

            if not hasattr(self, 'obs_context'):
                from collections import deque
                self.obs_context = deque(maxlen=self.obs_seq_len)
                self.action_counter = self.action_seq_size

            self.obs_context.append(obs)
            input_state = torch.stack(tuple(self.obs_context), dim=1)

        # Generate new action sequence if needed
        if not hasattr(self, 'action_counter') or self.action_counter == self.action_seq_size:
            self.action_counter = 0

            # Use EMA model for inference
            if self.use_ema:
                self.ema_helper.store(self.model.parameters())
                self.ema_helper.copy_to(self.model.parameters())

            self.model.eval()
            model_pred = self.model(input_state, goal)

            # Restore original parameters
            if self.use_ema:
                self.ema_helper.restore(self.model.parameters())

            model_pred = self.scaler.inverse_scale_output(model_pred)
            self.curr_action_seq = model_pred

        # Return next action
        next_action = self.curr_action_seq[:, self.action_counter, :]
        self.action_counter += 1
        return next_action.detach().cpu().numpy()

    def random_sample(self, state: torch.Tensor, goal: Optional[torch.Tensor] = None, extra_args=None, if_vision=True):
        """
        Random sampling method (from original DiffusionAgent)
        """
        if if_vision:
            bp_imgs, inhand_imgs = state

            bp_imgs = bp_imgs[:, :self.obs_seq_len].contiguous()
            inhand_imgs = inhand_imgs[:, :self.obs_seq_len].contiguous()

            input_state = (bp_imgs, inhand_imgs)
        else:
            obs = torch.from_numpy(state).float().to(self.device).unsqueeze(0)
            obs = self.scaler.scale_input(obs)

            if not hasattr(self, 'obs_context'):
                from collections import deque
                self.obs_context = deque(maxlen=self.obs_seq_len)

            self.obs_context.append(obs)
            input_state = torch.stack(tuple(self.obs_context), dim=1)

        # Generate samples with observation embedding
        model_pred, obs_embedding = self.model(input_state, goal, if_return_obs=True)

        return model_pred, obs_embedding

    def train_agent(self):
        """
        Main method to train the agent (required by BaseAgent).
        For DC training, this delegates to train_dc_tokens.
        """
        self.main_logger.info("🚀 Starting DC agent training...")
        return self.train_dc_tokens()

    def train_vision_agent(self):
        """
        Main method to train the vision agent (required by BaseAgent).
        For DC training, this delegates to train_dc_tokens.
        """
        self.main_logger.info("🚀 Starting DC vision agent training...")
        return self.train_dc_tokens()


import torch
import torch.nn as nn
from typing import Dict

class IntegratedEpisodeContrastiveLoss(nn.Module):
    """Episode感知的对比学习损失 - 困难负样本加权版本
    分别计算positive loss和negative loss，然后相加得到total loss
    """
    
    def __init__(self, temp=0.07, scale=4.0, device='cuda', dweight=0, 
                 window_before=2, window_after=1, hard_negative_weight=2.0):
        super().__init__()
        self.device = device
        self.temp = torch.nn.Parameter(torch.tensor(temp).to(self.device))
        self.scale = torch.nn.Parameter(torch.tensor(scale).to(self.device))
        self.dweight = dweight
        self.window_before = window_before
        self.window_after = window_after
        self.hard_negative_weight = hard_negative_weight  # 困难负样本权重


    
    def create_simple_episode_mask(self, episode_indices):
        """
        为(B,B)的error matrix创建episode感知的掩码
        
        Args:
            episode_indices: episode索引 (B,)
            
        Returns:
            mask: 正样本掩码 (B, B)
        """
        batch_size = len(episode_indices)
        mask = torch.zeros((batch_size, batch_size), device=self.device)
        
        # 计算episode边界
        boundaries = self._compute_episode_boundaries(episode_indices)
        
        for i in range(batch_size):
            # 找到样本i所在的episode范围
            episode_start, episode_end = self._find_episode_range(i, boundaries)
            
            # 计算时序窗口
            window_start = max(episode_start, i - self.window_before)
            window_end = min(episode_end, i + self.window_after + 1)
            
            # 在时序窗口内标记正样本
            for j in range(window_start, window_end):
                if j < batch_size and episode_indices[i] == episode_indices[j]:
                    mask[i, j] = 1.0
        
        return mask
    
    def create_hard_negative_weights(self, episode_indices):
        """
        创建困难负样本权重矩阵
        
        Args:
            episode_indices: episode索引 (B,)
            
        Returns:
            weights: 权重矩阵 (B, B)
                    - 正样本区域: 权重=0 (不参与负样本计算)
                    - 同episode负样本: 权重=hard_negative_weight (困难)
                    - 跨episode负样本: 权重=1.0 (简单)
        """
        batch_size = len(episode_indices)
        weights = torch.ones((batch_size, batch_size), device=self.device)
        
        # 创建正样本掩码
        positive_mask = self.create_simple_episode_mask(episode_indices)
        
        # 创建同episode掩码（包括正样本区域）
        same_episode_mask = torch.zeros((batch_size, batch_size), device=self.device)
        for i in range(batch_size):
            for j in range(batch_size):
                if episode_indices[i] == episode_indices[j]:
                    same_episode_mask[i, j] = 1.0
        
        # 同episode内的负样本 = 同episode - 正样本
        hard_negative_mask = same_episode_mask - positive_mask
        
        # 设置权重
        weights = torch.where(
            positive_mask == 1, 
            torch.zeros_like(weights),  # 正样本区域权重为0
            torch.where(
                hard_negative_mask == 1,
                torch.full_like(weights, self.hard_negative_weight),  # 困难负样本
                torch.ones_like(weights)  # 简单负样本(跨episode)
            )
        )
        
        return weights, positive_mask
    
    def _compute_episode_boundaries(self, episode_indices):
        """计算episode边界"""
        boundaries = [0]
        current_episode = episode_indices[0].item()
        
        for i, ep_idx in enumerate(episode_indices):
            if ep_idx.item() != current_episode:
                boundaries.append(i)
                current_episode = ep_idx.item()
        
        boundaries.append(len(episode_indices))
        return torch.tensor(boundaries, device=episode_indices.device)
    
    def _find_episode_range(self, sample_idx, boundaries):
        """找到样本所属episode的范围"""
        for i in range(len(boundaries) - 1):
            if boundaries[i] <= sample_idx < boundaries[i + 1]:
                return boundaries[i].item(), boundaries[i + 1].item()
        return 0, boundaries[-1].item()

    def forward(self, errors, extended_batch_info=None):
        """
        计算损失 - 困难负样本加权版本
        
        Args:
            errors: 距离矩阵 (B, B)
            extended_batch_info: episode信息字典
            
        Returns:
            loss: 损失值
            mask: 使用的掩码矩阵
        """
        batch_size = errors.shape[0]
        
        if extended_batch_info is not None:
            # 使用episode感知的困难负样本加权
            episode_indices = extended_batch_info['original_episode_indices']
            negative_weights, positive_mask = self.create_hard_negative_weights(episode_indices)
        else:
            # 回退到简单模式
            positive_mask = torch.eye(batch_size, device=self.device)
            negative_weights = torch.ones_like(positive_mask)
        
        # 计算logits（数值稳定版本）：用线性形式避免指数溢出
        logits = -errors / self.temp
        logits = self.scale * logits

        # 计算困难负样本加权的对比损失
        loss = self._compute_weighted_contrastive_loss(logits, positive_mask, negative_weights)
        
        # 添加正则化项
        if self.dweight > 0:
            diagonal_errors = errors[torch.arange(batch_size), torch.arange(batch_size)]
            loss += self.dweight * diagonal_errors.mean()
        
        return loss, positive_mask

    def forward_with_components(self, errors, extended_batch_info=None):
        """
        计算损失并返回分离的组件 - 用于详细监控
        
        Args:
            errors: 距离矩阵 (B, B)
            extended_batch_info: episode信息字典
            
        Returns:
            Dict containing:
            - total_loss: 总损失值
            - contrastive_loss: 对比学习损失
            - diffusion_regularization: deweight加权的扩散损失
            - mask: 使用的掩码矩阵
        """
        batch_size = errors.shape[0]
        
        if extended_batch_info is not None:
            # 使用episode感知的困难负样本加权
            episode_indices = extended_batch_info['original_episode_indices']
            negative_weights, positive_mask = self.create_hard_negative_weights(episode_indices)
        else:
            # 回退到简单模式
            positive_mask = torch.eye(batch_size, device=self.device)
            negative_weights = torch.ones_like(positive_mask)
        
        # 计算logits（数值稳定版本）：用线性形式避免指数溢出
        logits = -errors / self.temp
        logits = self.scale * logits

        # 计算困难负样本加权的对比损失
        contrastive_loss = self._compute_weighted_contrastive_loss(logits, positive_mask, negative_weights)
        
        # 计算deweight加权的扩散损失 (正则化项)
        diffusion_regularization = torch.tensor(0.0, device=self.device)
        if self.dweight > 0:
            diagonal_errors = errors[torch.arange(batch_size), torch.arange(batch_size)]
            diffusion_regularization = self.dweight * diagonal_errors.mean()
        
        # 总损失
        total_loss = contrastive_loss + diffusion_regularization
        
        return {
            'total_loss': total_loss,
            'contrastive_loss': contrastive_loss,
            'diffusion_regularization': diffusion_regularization,
            'mask': positive_mask
        }
    
    def _compute_weighted_contrastive_loss(self, logits, positive_mask, negative_weights):
        """
        计算困难负样本加权的对比损失
        
        Args:
            logits: 相似度矩阵 (B, B)
            positive_mask: 正样本掩码 (B, B) 
            negative_weights: 负样本权重矩阵 (B, B)
        """
        batch_size = logits.shape[0]
        
        # 1. 正样本损失（标准对比学习）
        positive_loss = self._compute_positive_loss(logits, positive_mask)
        
        # 2. 加权负样本损失
        negative_loss = self._compute_weighted_negative_loss(logits, positive_mask, negative_weights)
        
        # 3. 总损失
        total_loss = positive_loss + negative_loss
        
        return total_loss
    
    def _compute_positive_loss(self, logits, positive_mask):
        """计算正样本损失（稳定版）"""
        # 使用 log_softmax 计算 log 概率，避免溢出
        log_probs = torch.log_softmax(logits, dim=1)

        # 正样本权重归一化
        mask_sums = positive_mask.sum(dim=1, keepdim=True)
        mask_sums = torch.clamp(mask_sums, min=1e-8)
        normalized_positive_mask = positive_mask / mask_sums

        # 正样本损失：最大化正样本对的概率
        positive_loss = -(normalized_positive_mask * log_probs).sum() / positive_mask.shape[0]

        return positive_loss
    
    def _compute_weighted_negative_loss(self, logits, positive_mask, negative_weights):
        """计算加权负样本损失（稳定版，log-sum-exp）"""
        # 负样本掩码
        negative_mask = 1.0 - positive_mask

        # 加权掩码（正样本权重维持为1，负样本为指定权重）
        weighted_mask = positive_mask + negative_mask * negative_weights

        # 对数域计算：
        # log(sum_i w_i * exp(logits_i)) = logsumexp(logits + log(w_i))
        eps = 1e-8
        log_weights = torch.log(torch.clamp(weighted_mask, min=eps))

        # 分母：加权所有样本的 log-sum-exp
        denom_logsumexp = torch.logsumexp(logits + log_weights, dim=1, keepdim=True)

        # 分子：正样本的 log-sum-exp（若每行只有1个正样本，则等价于取该位置的logits）
        # 使用一个极小的掩码避开 -inf：将非正样本位置置为 -1e9 以近似屏蔽
        very_neg = -1e9
        positive_only_logits = torch.where(positive_mask > 0, logits, very_neg)
        numer_logsumexp = torch.logsumexp(positive_only_logits, dim=1, keepdim=True)

        # 负样本损失：- (log(sum_pos) - log(sum_all)) = logsumexp(all) - logsumexp(pos)
        negative_loss = (denom_logsumexp - numer_logsumexp).mean()

        return negative_loss
        
    def _compute_masked_contrastive_loss(self, logits, mask):
        """计算基于掩码的对比损失（保留作为后备）"""
        log_probs = torch.log_softmax(logits, dim=1)
        
        # 归一化掩码（每行的正样本权重归一化）
        mask_sums = mask.sum(dim=1, keepdim=True)
        mask_sums = torch.clamp(mask_sums, min=1e-8)  # 避免除零
        normalized_mask = mask / mask_sums
        
        # 计算加权交叉熵损失
        loss = -(normalized_mask * log_probs).sum() / mask.shape[0]
        
        return loss

# 简化版本完成，无需复杂的演示代码