#!/usr/bin/env python3
"""
分析完整MaIL模型的参数分布
包括视觉编码器、扩散模型等所有组件
"""

import os
import sys
import torch
import hydra
from omegaconf import DictConfig, OmegaConf
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

def count_parameters(model):
    """计算模型参数数量"""
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    return total_params, trainable_params

def analyze_component_params(model, component_name):
    """分析单个组件的参数"""
    total_params, trainable_params = count_parameters(model)
    print(f"  {component_name}:")
    print(f"    总参数: {total_params:,}")
    print(f"    可训练: {trainable_params:,}")
    return total_params

def create_resnet_encoder():
    """创建ResNet视觉编码器"""
    from agents.module.vision.get_model import get_resnet
    
    # 创建两个ResNet (agentview_rgb 和 eye_in_hand_rgb)
    resnet1 = get_resnet(input_shape=[3, 128, 128], output_size=128)
    resnet2 = get_resnet(input_shape=[3, 128, 128], output_size=128)
    
    return resnet1, resnet2

def main():
    print("🔍 完整MaIL模型参数分析")
    print("=" * 80)
    
    try:
        # 1. 分析视觉编码器
        print("\n📷 视觉编码器分析:")
        print("-" * 40)
        
        resnet1, resnet2 = create_resnet_encoder()
        resnet1_params = analyze_component_params(resnet1, "AgentView ResNet18")
        resnet2_params = analyze_component_params(resnet2, "Eye-in-Hand ResNet18")
        
        total_vision_params = resnet1_params + resnet2_params
        print(f"  视觉编码器总参数: {total_vision_params:,}")
        
        # 2. 分析扩散模型 (不同层数配置)
        print("\n🤖 扩散模型分析:")
        print("-" * 40)
        
        from agents.models.goal_ddpm.oc_ddpm import DiffusionEncDec
        from omegaconf import DictConfig
        
        configs = [(6, 6), (12, 12)]
        diffusion_results = []
        
        for enc_layers, dec_layers in configs:
            print(f"\n  配置: {enc_layers}+{dec_layers} 层")
            
            encoder_config = DictConfig({
                '_target_': 'agents.models.goal_ddpm.oc_ddpm.TransformerEncoder',
                'embed_dim': 128,
                'n_heads': 4,
                'n_layers': enc_layers,
                'attn_pdrop': 0.1,
                'resid_pdrop': 0.1,
                'bias': False,
                'block_size': 14
            })
            
            decoder_config = DictConfig({
                '_target_': 'agents.models.goal_ddpm.oc_ddpm.TransformerDecoder',
                'embed_dim': 128,
                'cross_embed': 128,
                'n_heads': 4,
                'n_layers': dec_layers,
                'attn_pdrop': 0.1,
                'resid_pdrop': 0.1,
                'bias': False,
                'block_size': 14
            })
            
            diffusion_model = DiffusionEncDec(
                encoder=encoder_config,
                decoder=decoder_config,
                state_dim=256,
                action_dim=7,
                device='cpu',
                goal_conditioned=False,
                embed_dim=128,
                embed_pdrob=0,
                goal_seq_len=0,
                obs_seq_len=5,
                action_seq_len=5,
                linear_output=True
            )
            
            diffusion_params = analyze_component_params(diffusion_model, f"扩散模型({enc_layers}+{dec_layers})")
            diffusion_results.append((f"{enc_layers}+{dec_layers}", diffusion_params))
        
        # 3. 总体对比分析
        print("\n📊 总体参数对比:")
        print("=" * 80)
        
        print(f"{'组件':<20} {'参数数量':<15} {'占比'}")
        print("-" * 50)
        
        for config, diffusion_params in diffusion_results:
            total_model_params = total_vision_params + diffusion_params
            
            vision_ratio = (total_vision_params / total_model_params) * 100
            diffusion_ratio = (diffusion_params / total_model_params) * 100
            
            print(f"\n配置: {config} 层")
            print(f"{'视觉编码器':<20} {total_vision_params:<15,} {vision_ratio:.1f}%")
            print(f"{'扩散模型':<20} {diffusion_params:<15,} {diffusion_ratio:.1f}%")
            print(f"{'总计':<20} {total_model_params:<15,} 100.0%")
            
            # 计算显存估算 (FP32)
            memory_mb = (total_model_params * 4) / (1024 * 1024)  # 4 bytes per parameter
            print(f"{'模型显存(FP32)':<20} {memory_mb:<15.1f} MB")
        
        # 4. 分析为什么感觉参数没变化
        print("\n🤔 为什么感觉参数数量没变化？")
        print("=" * 80)
        
        vision_params = total_vision_params
        diff_6_6 = diffusion_results[0][1]
        diff_12_12 = diffusion_results[1][1]
        
        total_6_6 = vision_params + diff_6_6
        total_12_12 = vision_params + diff_12_12
        
        print(f"6+6层配置总参数:  {total_6_6:,}")
        print(f"12+12层配置总参数: {total_12_12:,}")
        print(f"参数增加量:        {total_12_12 - total_6_6:+,}")
        print(f"增加比例:          {((total_12_12 - total_6_6) / total_6_6) * 100:.1f}%")
        
        print(f"\n💡 可能的原因:")
        print(f"1. 视觉编码器占主导地位: {vision_params:,} 参数 ({(vision_params/total_6_6)*100:.1f}%)")
        print(f"2. 扩散模型参数相对较少: {diff_6_6:,} → {diff_12_12:,}")
        print(f"3. 总体增加比例相对较小: {((total_12_12 - total_6_6) / total_6_6) * 100:.1f}%")
        print(f"4. 显存占用主要来源可能是:")
        print(f"   - 激活值 (前向传播中间结果)")
        print(f"   - 梯度 (反向传播)")
        print(f"   - 优化器状态 (Adam的动量等)")
        print(f"   - 批次数据")
        
    except Exception as e:
        print(f"❌ 分析过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
