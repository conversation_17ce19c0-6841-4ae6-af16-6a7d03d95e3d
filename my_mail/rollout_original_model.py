#!/usr/bin/env python3
"""
Original Model Rollout Script for MaIL Project
Evaluate the original pretrained model without DC tokens.
"""

import os
import sys
import time
import logging
import random
import argparse
from datetime import datetime
from pathlib import Path
import multiprocessing as mp

import hydra
import numpy as np
import torch
import wandb
from omegaconf import DictConfig, OmegaConf

# Add MaIL project root to path
sys.path.insert(0, '/home/<USER>/work/MaIL')

from agents.utils import sim_framework_path

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
log = logging.getLogger(__name__)

# Register OmegaConf resolver
OmegaConf.register_new_resolver("add", lambda *numbers: sum(numbers))

def set_seed_everywhere(seed):
    """Set random seed for reproducibility"""
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed_all(seed)
    np.random.seed(seed)
    random.seed(seed)

def setup_logging():
    """Setup logging for original model rollout"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_dir = Path(f"logs/original_rollout/{timestamp}")
    log_dir.mkdir(parents=True, exist_ok=True)
    
    # File handler
    file_handler = logging.FileHandler(log_dir / "original_rollout.log")
    file_handler.setLevel(logging.INFO)
    
    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    
    # Formatter
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)
    
    # Add handlers to logger
    log.addHandler(file_handler)
    log.addHandler(console_handler)
    
    log.info("=" * 50)
    log.info("🚀 Original Model Rollout Logging initialized")
    log.info(f"📁 Log directory: {log_dir}")
    log.info("=" * 50)
    
    return log_dir

def log_system_info():
    """Log system information"""
    log.info("🖥️  System Information:")
    log.info(f"   CUDA Available: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        log.info(f"   CUDA Device: {torch.cuda.get_device_name()}")
        log.info(f"   CUDA Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
    
    log.info(f"   Python Version: {sys.version}")
    log.info(f"   PyTorch Version: {torch.__version__}")
    log.info(f"   Available CPUs: {mp.cpu_count()}")

def load_original_model(agent, model_path, device="cuda"):
    """Load original pretrained model"""
    log.info(f"🔄 Loading original model from: {model_path}")

    if not os.path.exists(model_path):
        raise FileNotFoundError(f"Model not found: {model_path}")

    # Load model state dict directly (since model_path is the full file path)
    checkpoint = torch.load(model_path, map_location=device)
    agent.model.load_state_dict(checkpoint, strict=False)

    # Set to evaluation mode
    agent.model.eval()

    # Log model info
    total_params = sum(p.numel() for p in agent.model.parameters())
    log.info(f"📊 Total model parameters: {total_params:,}")
    log.info("✅ Original model loaded successfully!")

    return agent

def load_original_config():
    """Load original model configuration from its training directory"""
    config_path = "/home/<USER>/work/MaIL/MaIL/logs/libero_goal/multi_task_diffusion_transformer/sweeps/goal_ddpm_transformer_encdec/2025-07-22/21-53-31/.hydra/config.yaml"

    log.info(f"📖 Loading original model config from: {config_path}")

    with open(config_path, 'r') as f:
        import yaml
        config_dict = yaml.safe_load(f)

    # Convert to OmegaConf
    cfg = OmegaConf.create(config_dict)

    log.info("✅ Original model config loaded successfully!")
    return cfg

def main():
    """Main function for original model rollout"""
    
    # Setup logging
    log_dir = setup_logging()
    
    log.info("🚀 Starting Original Model Rollout Evaluation")
    log.info("📊 Evaluating original pretrained model (no DC tokens)")
    
    # Set seed
    set_seed_everywhere(42)
    log.info("🎲 Random seed set to: 42")
    
    # Disable wandb
    os.environ["WANDB_MODE"] = "disabled"
    wandb.init(mode="disabled")
    log.info("📊 Wandb initialized in disabled mode")
    
    # Log system info
    log_system_info()
    
    try:
        # Load original model configuration
        cfg = load_original_config()

        # Instantiate original agent using the loaded configuration
        log.info("🔧 Instantiating original agent...")
        agent = hydra.utils.instantiate(cfg.agents)
        log.info(f"✅ Agent created: {type(agent).__name__}")
        
        # Load pretrained model
        model_path = "/home/<USER>/work/MaIL/MaIL/logs/libero_goal/multi_task_diffusion_transformer/sweeps/goal_ddpm_transformer_encdec/2025-07-22/21-53-31/eval_best_ddpm.pth"
        agent = load_original_model(agent, model_path, cfg.device)
        
        # Test model inference (without environment)
        log.info("🧪 Testing model inference...")

        # Create dummy input for testing
        batch_size = 2
        obs_seq_len = cfg.obs_seq
        action_seq_len = cfg.train_action_seq

        # Create dummy observations
        dummy_obs = torch.randn(batch_size, obs_seq_len, cfg.obs_dim, device=cfg.device)
        dummy_actions = torch.randn(batch_size, action_seq_len, cfg.action_dim, device=cfg.device)
        dummy_timesteps = torch.randint(0, 16, (batch_size,), device=cfg.device)

        # Test forward pass
        with torch.no_grad():
            try:
                # Test model prediction
                output = agent.model(dummy_actions, dummy_timesteps, dummy_obs, dummy_obs)
                log.info(f"✅ Model inference test successful!")
                log.info(f"   Input shape: {dummy_actions.shape}")
                log.info(f"   Output shape: {output.shape}")

                # Test agent prediction method
                dummy_state = {
                    'agentview_rgb': torch.randn(1, 3, 128, 128, device=cfg.device),
                    'eye_in_hand_rgb': torch.randn(1, 3, 128, 128, device=cfg.device),
                    'task_emb': torch.randn(1, 256, device=cfg.device)
                }

                pred_action = agent.predict(dummy_state)
                log.info(f"✅ Agent prediction test successful!")
                log.info(f"   Predicted action shape: {pred_action.shape}")

            except Exception as e:
                log.error(f"❌ Model inference test failed: {e}")
                import traceback
                traceback.print_exc()

        log.info("🎉 Original model loading and inference test completed successfully!")
        log.info("📊 Model Summary:")
        log.info(f"   Total parameters: {sum(p.numel() for p in agent.model.parameters()):,}")
        log.info(f"   Model type: {type(agent.model).__name__}")
        log.info(f"   Device: {next(agent.model.parameters()).device}")
        log.info("=" * 50)
        log.info("✅ Original model validation completed!")
        log.info("🔄 To run full rollout evaluation, fix the gym environment issue first.")
        log.info("=" * 50)
        
    except Exception as e:
        log.error(f"❌ Error during rollout evaluation: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    print("🚀 Starting Original Model Rollout Evaluation")
    print("🎮 Episodes per task: 10")
    print("🖥️  Device: cuda")
    print("🎲 Seed: 42")
    print("💻 CPU cores: 2")
    print("📋 Task suite: libero_goal")
    print("=" * 50)
    print("✅ Configuration loaded successfully")
    print("🔄 Running rollout evaluation...")

    # Run main function
    exit_code = main()

    if exit_code == 0:
        print("✅ Rollout evaluation completed successfully!")
    else:
        print("❌ Rollout evaluation failed!")

    sys.exit(exit_code)
