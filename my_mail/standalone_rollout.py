#!/usr/bin/env python3
"""
Standalone LIBERO Rollout Script
This script loads a trained model and performs rollouts in LIBERO environment with video recording.
"""

import os
import sys
import logging
import random
import argparse
from pathlib import Path
from datetime import datetime

import numpy as np
import torch
import hydra
from omegaconf import DictConfig, OmegaConf
import wandb

# Add MaIL to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Initialize wandb in disabled mode to avoid logging errors
wandb.init(mode="disabled")

from libero.libero.envs import *
from libero.libero import benchmark
from libero.libero.envs import OffScreenRenderEnv
from utils.video_utils import VideoRecorder
import imgaug.parameters as iap
from imgaug import augmenters as iaa


def set_seed_everywhere(seed):
    """Set random seeds for reproducibility."""
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed_all(seed)
    np.random.seed(seed)
    random.seed(seed)


def process_image_input(img_tensor):
    """Process image input for the model."""
    return img_tensor / 255.


class LiberoRolloutEvaluator:
    """
    Standalone evaluator for LIBERO rollouts with video recording.
    """
    
    def __init__(self, 
                 task_suite="libero_goal",
                 num_episodes=20,
                 max_steps_per_episode=600,
                 seed=42,
                 device="cuda",
                 save_video=True,
                 data_aug=False,
                 aug_factor=0.02):
        
        self.task_suite = task_suite
        self.num_episodes = num_episodes
        self.max_steps_per_episode = max_steps_per_episode
        self.seed = seed
        self.device = device
        self.save_video = save_video
        self.data_aug = data_aug
        self.aug_factor = aug_factor
        
        # Setup data augmentation if enabled
        if self.data_aug:
            self.aug = iaa.arithmetic.ReplaceElementwise(
                iap.FromLowerResolution(iap.Binomial(self.aug_factor), size_px=8),
                [255]
            )
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        # Results storage
        self.results = {}
        
    def load_agent(self, config_path, model_weights_path):
        """
        Load trained agent from config and weights.
        
        Args:
            config_path: Path to the hydra config file
            model_weights_path: Path to the model weights file
        """
        self.logger.info(f"Loading agent configuration from: {config_path}")
        
        # Load configuration
        with hydra.initialize_config_dir(config_dir=str(Path(config_path).parent.absolute())):
            cfg = hydra.compose(config_name=Path(config_path).stem)
        
        # Instantiate agent
        self.logger.info("Instantiating agent...")
        agent = hydra.utils.instantiate(cfg.agents)
        
        # Load model weights
        self.logger.info(f"Loading model weights from: {model_weights_path}")
        if not os.path.exists(model_weights_path):
            raise FileNotFoundError(f"Model weights not found: {model_weights_path}")
        
        agent.load_pretrained_model(
            weights_path=str(Path(model_weights_path).parent),
            sv_name=Path(model_weights_path).name
        )
        
        # Set to evaluation mode
        agent.model.eval()
        
        self.agent = agent
        self.cfg = cfg
        
        self.logger.info("✅ Agent loaded successfully!")
        return agent
    
    def run_single_episode(self, task_id, episode_idx, init_state_idx=0):
        """
        Run a single episode for a specific task.
        
        Args:
            task_id: Task ID to evaluate
            episode_idx: Episode index for naming
            init_state_idx: Initial state index to use
            
        Returns:
            dict: Episode results including success, steps, video_path
        """
        # Get task suite and setup environment
        task_suite = benchmark.get_benchmark_dict()[self.task_suite]()
        task_bddl_file = task_suite.get_task_bddl_file_path(task_id)
        
        # Get task information
        file_name = os.path.basename(task_bddl_file).split('.')[0]
        
        # Load task embedding (if agent uses task embeddings)
        task_emb = None
        if hasattr(self.agent, 'trainset') and hasattr(self.agent.trainset, 'tasks'):
            task_emb = self.agent.trainset.tasks.get(file_name, None)
            if task_emb is not None:
                self.logger.info(f"🎯 Task embedding found for {file_name}: shape={task_emb.shape if hasattr(task_emb, 'shape') else type(task_emb)}")
            else:
                self.logger.info(f"⚠️  No task embedding found for {file_name}")
        else:
            self.logger.info("⚠️  Agent does not have trainset.tasks attribute")
        
        # Get task description
        task_description = f"task_{task_id}"
        try:
            task_obj = task_suite.get_task(task_id)
            if hasattr(task_obj, 'language'):
                task_description = task_obj.language
            elif hasattr(task_obj, 'name'):
                task_description = task_obj.name
        except:
            pass
        
        # Setup environment
        init_states = task_suite.get_task_init_states(task_id)
        env_args = {
            "bddl_file_name": task_bddl_file,
            "camera_heights": 128,
            "camera_widths": 128
        }
        
        env = OffScreenRenderEnv(**env_args)
        
        # Reset agent and environment
        self.agent.reset()
        env.seed(self.seed)
        env.reset()
        obs = env.set_init_state(init_state=init_states[init_state_idx])
        
        # Initialize video recorder
        video_recorder = VideoRecorder(save_video=self.save_video)
        
        # Initial physics simulation with dummy actions
        dummy = np.zeros(7)
        dummy[-1] = -1.0  # Open gripper
        for _ in range(5):
            obs, _, _, _ = env.step(dummy)
            if self.save_video:
                video_recorder.record_frame(obs, "agentview_image")
        
        # Run episode
        episode_success = False
        total_reward = 0
        step_count = 0
        
        self.logger.info(f"🎮 Running episode {episode_idx} for task {task_id}: {task_description}")
        
        for step in range(self.max_steps_per_episode):
            # Get observation
            agentview_rgb = obs["agentview_image"]
            
            # Apply data augmentation if enabled
            if self.data_aug:
                agentview_rgb = self.aug(image=agentview_rgb)
            
            # Prepare state input for agent
            self.logger.info(f"Agent has use_eye_in_hand: {hasattr(self.agent, 'use_eye_in_hand')}")
            if hasattr(self.agent, 'use_eye_in_hand'):
                self.logger.info(f"use_eye_in_hand value: {self.agent.use_eye_in_hand}")
            
            # For goal-conditioned agent, we always need 3 components: (bp_image, inhand_image, goal_image)
            eye_in_hand_rgb = obs["robot0_eye_in_hand_image"]
            
            if task_emb is not None:
                state = (agentview_rgb, eye_in_hand_rgb, task_emb)
            else:
                # If no task embedding, we need to create a dummy goal image
                dummy_goal = torch.zeros((1, 512)).to(self.agent.device)  # Match expected shape and device
                state = (agentview_rgb, eye_in_hand_rgb, dummy_goal)
            
            # Get action from agent
            try:
                # Debug: print state information
                self.logger.info(f"State type: {type(state)}, length: {len(state) if isinstance(state, tuple) else 'not tuple'}")
                if isinstance(state, tuple):
                    for i, s in enumerate(state):
                        self.logger.info(f"State[{i}]: type={type(s)}, shape={s.shape if hasattr(s, 'shape') else 'no shape'}")
                
                if task_emb is not None:
                    self.logger.debug(f"Task embedding: type={type(task_emb)}, shape={task_emb.shape}")
                    action = self.agent.predict(state, goal=task_emb)
                else:
                    action = self.agent.predict(state)
                
                # Convert to numpy array if it's a tensor
                if hasattr(action, 'detach'):
                    action = action.detach().cpu().numpy()
                
                # Flatten if needed (predict returns shape (1, action_dim))
                if action.ndim > 1:
                    action = action.flatten()
                    
            except Exception as e:
                self.logger.error(f"Error getting action from agent: {e}")
                import traceback
                self.logger.error(f"Full traceback: {traceback.format_exc()}")
                break
            
            # Execute action
            obs, reward, done, info = env.step(action)
            total_reward += reward
            step_count += 1
            
            # Record frame
            if self.save_video:
                video_recorder.record_frame(obs, "agentview_image")
            
            # Check for success
            if reward == 1:
                episode_success = True
                self.logger.info(f"✅ Episode {episode_idx} succeeded at step {step_count}")
                break
        
        # Save video
        video_path = None
        if self.save_video and video_recorder.get_num_frames() > 0:
            try:
                current_time = datetime.now().strftime("%Y%m%d_%H%M%S")
                episode_id = f"task_{task_id}_ep_{episode_idx}_{current_time}"
                video_path = video_recorder.save_recorded_video(
                    task_suite_name=self.task_suite,
                    epoch="rollout",
                    episode_idx=episode_id,
                    success=episode_success,
                    task_description=task_description
                )
            except Exception as e:
                self.logger.error(f"Failed to save video: {e}")
        
        # Clean up
        env.close()
        
        # Log results
        if episode_success:
            self.logger.info(f"🎉 Task {task_id} Episode {episode_idx}: SUCCESS in {step_count} steps")
        else:
            self.logger.info(f"❌ Task {task_id} Episode {episode_idx}: FAILED after {step_count} steps")
        
        return {
            'task_id': task_id,
            'episode_idx': episode_idx,
            'success': episode_success,
            'steps': step_count,
            'total_reward': total_reward,
            'video_path': video_path,
            'task_description': task_description
        }
    
    def evaluate_tasks(self, task_ids=None, episodes_per_task=None):
        """
        Evaluate agent on specified tasks.
        
        Args:
            task_ids: List of task IDs to evaluate. If None, evaluates all tasks.
            episodes_per_task: Number of episodes per task. If None, uses self.num_episodes.
        
        Returns:
            dict: Complete evaluation results
        """
        if episodes_per_task is None:
            episodes_per_task = self.num_episodes
        
        # Determine tasks to evaluate
        if task_ids is None:
            if self.task_suite == "libero_90":
                task_ids = list(range(90))
            else:
                task_ids = list(range(10))
        
        self.logger.info(f"🚀 Starting evaluation on {len(task_ids)} tasks, {episodes_per_task} episodes each")
        self.logger.info(f"📊 Task suite: {self.task_suite}")
        self.logger.info(f"🎥 Video recording: {'Enabled' if self.save_video else 'Disabled'}")
        
        all_results = []
        task_success_rates = {}
        
        for task_id in task_ids:
            self.logger.info(f"\n📋 Evaluating Task {task_id}")
            
            task_results = []
            successes = 0
            
            for episode_idx in range(episodes_per_task):
                result = self.run_single_episode(task_id, episode_idx)
                task_results.append(result)
                all_results.append(result)
                
                if result['success']:
                    successes += 1
            
            # Calculate task success rate
            success_rate = successes / episodes_per_task
            task_success_rates[task_id] = success_rate
            
            self.logger.info(f"📊 Task {task_id} Success Rate: {success_rate:.2%} ({successes}/{episodes_per_task})")
        
        # Calculate overall statistics
        total_episodes = len(all_results)
        total_successes = sum(1 for r in all_results if r['success'])
        overall_success_rate = total_successes / total_episodes if total_episodes > 0 else 0
        
        # Compile final results
        final_results = {
            'overall_success_rate': overall_success_rate,
            'total_episodes': total_episodes,
            'total_successes': total_successes,
            'task_success_rates': task_success_rates,
            'all_episodes': all_results,
            'config': {
                'task_suite': self.task_suite,
                'num_episodes_per_task': episodes_per_task,
                'max_steps_per_episode': self.max_steps_per_episode,
                'seed': self.seed,
                'save_video': self.save_video
            }
        }
        
        # Log final results
        self.logger.info(f"\n🎯 FINAL RESULTS:")
        self.logger.info(f"   Overall Success Rate: {overall_success_rate:.2%}")
        self.logger.info(f"   Total Episodes: {total_episodes}")
        self.logger.info(f"   Total Successes: {total_successes}")
        self.logger.info(f"   Tasks Evaluated: {len(task_ids)}")
        
        # Save results to file
        results_dir = Path("rollout_results")
        results_dir.mkdir(exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = results_dir / f"rollout_results_{self.task_suite}_{timestamp}.json"
        
        import json
        with open(results_file, 'w') as f:
            # Convert numpy types to native Python types for JSON serialization
            json_results = json.loads(json.dumps(final_results, default=str))
            json.dump(json_results, f, indent=2)
        
        self.logger.info(f"💾 Results saved to: {results_file}")
        
        return final_results


def main():
    parser = argparse.ArgumentParser(description="Standalone LIBERO Rollout Evaluation")
    parser.add_argument("--config", type=str, required=True,
                        help="Path to the hydra config file used for training")
    parser.add_argument("--weights", type=str, required=True,
                        help="Path to the trained model weights file")
    parser.add_argument("--task-suite", type=str, default="libero_goal",
                        choices=["libero_spatial", "libero_object", "libero_goal", "libero_90"],
                        help="LIBERO task suite to evaluate on")
    parser.add_argument("--episodes", type=int, default=5,
                        help="Number of episodes per task")
    parser.add_argument("--max-steps", type=int, default=600,
                        help="Maximum steps per episode")
    parser.add_argument("--seed", type=int, default=42,
                        help="Random seed for reproducibility")
    parser.add_argument("--device", type=str, default="cuda",
                        help="Device to run on (cuda/cpu)")
    parser.add_argument("--no-video", action="store_true",
                        help="Disable video recording")
    parser.add_argument("--tasks", type=str, default=None,
                        help="Comma-separated list of task IDs to evaluate (e.g., '0,1,2'). If not specified, evaluates all tasks.")
    parser.add_argument("--data-aug", action="store_true",
                        help="Enable data augmentation during evaluation")
    parser.add_argument("--aug-factor", type=float, default=0.02,
                        help="Data augmentation factor")
    
    args = parser.parse_args()
    
    # Set seed
    set_seed_everywhere(args.seed)
    
    # Parse task IDs if specified
    task_ids = None
    if args.tasks:
        task_ids = [int(x.strip()) for x in args.tasks.split(',')]
    
    # Create evaluator
    evaluator = LiberoRolloutEvaluator(
        task_suite=args.task_suite,
        num_episodes=args.episodes,
        max_steps_per_episode=args.max_steps,
        seed=args.seed,
        device=args.device,
        save_video=not args.no_video,
        data_aug=args.data_aug,
        aug_factor=args.aug_factor
    )
    
    # Load agent
    try:
        evaluator.load_agent(args.config, args.weights)
    except Exception as e:
        print(f"❌ Failed to load agent: {e}")
        return 1
    
    # Run evaluation
    try:
        results = evaluator.evaluate_tasks(task_ids=task_ids)
        print(f"\n🎉 Evaluation completed successfully!")
        print(f"📊 Overall Success Rate: {results['overall_success_rate']:.2%}")
        return 0
    except Exception as e:
        print(f"❌ Evaluation failed: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit(main())