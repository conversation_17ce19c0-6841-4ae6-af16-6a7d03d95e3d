#!/usr/bin/env python3
"""
DC tokens training script for MaIL project.
Implements SoftREPA-style contrastive learning based on lerobot-add_transformer's approach.
"""

import argparse
import logging
import sys
import os
import json
from pathlib import Path
from datetime import datetime
import time

import torch
import torch.nn as nn
from torch.utils.data import DataLoader
from torch.cuda.amp import GradScaler, autocast
from tqdm import tqdm

# Add MaIL project root to path
sys.path.insert(0, '/home/<USER>/work/MaIL')

from omegaconf import DictConfig, OmegaConf
import hydra
import wandb

from agents.dc_ddpm_agent import DiffusionAgent_DC, DiffusionPolicy_DC, ContrastiveLoss
from agents.models.goal_ddpm.oc_ddpm_dc import DiffusionEncDec


def setup_logging(output_dir: str):
    """Setup logging configuration"""
    log_file = Path(output_dir) / "dc_training.log"
    Path(output_dir).mkdir(parents=True, exist_ok=True)
    
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    logging.info(f"Log file: {log_file}")


def create_dummy_batch(batch_size: int, device: str) -> dict:
    """Create dummy batch for testing"""
    return {
        "action": torch.randn(batch_size, 5, 7, device=device),  # (B, horizon, action_dim)
        "obs": torch.randn(batch_size, 10, 256, device=device),  # (B, obs_seq, obs_dim)
        "goal": torch.randn(batch_size, 256, device=device),     # (B, goal_dim)
    }


def test_dc_functionality(device='cpu'):
    """Test DC tokens functionality"""
    logging.info("🧪 Testing DC functionality...")
    logging.info(f"Using device: {device}")

    try:
        # Import the DC model directly
        from agents.models.goal_ddpm.oc_ddpm_dc import DiffusionEncDec, TransformerEncoder, TransformerDecoder
        from omegaconf import DictConfig

        # Create model components directly
        embed_dim = 128

        # Create encoder config
        encoder_config = DictConfig({
            '_target_': 'agents.models.goal_ddpm.oc_ddpm_dc.TransformerEncoder',
            'embed_dim': embed_dim,
            'n_heads': 4,
            'n_layers': 4,
            'attn_pdrop': 0.1,
            'resid_pdrop': 0.1,
            'bias': False,
            'block_size': 32,
            'n_dc_layers': 3
        })

        # Create decoder config
        decoder_config = DictConfig({
            '_target_': 'agents.models.goal_ddpm.oc_ddpm_dc.TransformerDecoder',
            'embed_dim': embed_dim,
            'cross_embed': embed_dim,
            'n_heads': 4,
            'n_layers': 3,
            'attn_pdrop': 0.1,
            'resid_pdrop': 0.1,
            'bias': False,
            'block_size': 32,
            'decoder_n_dc_layers': 2
        })

        # Create DC-enhanced diffusion model
        diffusion_model = DiffusionEncDec(
            encoder=encoder_config,
            decoder=decoder_config,
            state_dim=256,
            action_dim=7,
            goal_conditioned=False,
            goal_seq_len=10,
            obs_seq_len=5,
            action_seq_len=5,
            embed_pdrob=0.1,
            embed_dim=embed_dim,
            device=device,
            linear_output=True,
            # DC configuration
            n_dc_layers=3,
            n_dc_tokens=4,
            use_dc_t=True,
            use_dc=True,
            n_decoder_dc_layers=2,
            n_decoder_dc_tokens=3,
            use_decoder_dc_t=True,
            use_decoder_dc=True,
        ).to(device)

        logging.info("✅ DiffusionEncDec created successfully")

        # Test basic functionality
        batch_size = 2
        actions = torch.randn(batch_size, 5, 7, device=device)
        time = torch.randint(0, 100, (batch_size,), device=device)
        states = torch.randn(batch_size, 5, 256, device=device)
        goals = torch.randn(batch_size, 10, 256, device=device)

        # Test forward pass
        with torch.no_grad():
            output = diffusion_model(actions, time, states, goals)
            logging.info(f"✅ Forward pass successful, output shape: {output.shape}")

        # Test DC tokens initialization
        if diffusion_model.dc_tokens is not None:
            nn.init.normal_(diffusion_model.dc_tokens, mean=0, std=0.02)
            logging.info("✅ Initialized encoder DC tokens")

        if diffusion_model.decoder_dc_tokens is not None:
            nn.init.normal_(diffusion_model.decoder_dc_tokens, mean=0, std=0.02)
            logging.info("✅ Initialized decoder DC tokens")

        # Test parameter counting
        total_params = sum(p.numel() for p in diffusion_model.parameters())
        dc_params = sum(p.numel() for name, p in diffusion_model.named_parameters() if 'dc' in name)

        logging.info(f"✅ Total parameters: {total_params:,}")
        logging.info(f"✅ DC parameters: {dc_params:,}")
        logging.info(f"✅ DC parameter ratio: {dc_params/total_params*100:.2f}%")

        # Test gradient computation
        logging.info("🔄 Testing gradient computation...")

        # Freeze base model
        for name, param in diffusion_model.named_parameters():
            if 'dc' in name:
                param.requires_grad = True
            else:
                param.requires_grad = False

        optimizer = torch.optim.AdamW([p for p in diffusion_model.parameters() if p.requires_grad], lr=1e-4)

        # Simple loss computation
        output = diffusion_model(actions, time, states, goals)
        loss = output.mean()

        optimizer.zero_grad()
        loss.backward()
        optimizer.step()

        logging.info("✅ Gradient computation successful")

        return True
        
    except Exception as e:
        logging.error(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    parser = argparse.ArgumentParser(description="Train DC tokens for MaIL")
    parser.add_argument("--output_dir", type=str, default="./dc_training_output", 
                       help="Output directory for logs and checkpoints")
    parser.add_argument("--pretrained_model", type=str, default=None,
                       help="Path to pretrained model checkpoint")
    parser.add_argument("--batch_size", type=int, default=4,
                       help="Training batch size")
    parser.add_argument("--steps", type=int, default=1000,
                       help="Number of training steps")
    parser.add_argument("--lr", type=float, default=1e-4,
                       help="Learning rate")
    parser.add_argument("--device", type=str, default="auto",
                       help="Device to use (auto, cpu, cuda)")
    parser.add_argument("--test_only", action="store_true",
                       help="Only run functionality test")
    
    args = parser.parse_args()
    
    # Setup device
    if args.device == "auto":
        device = 'cuda' if torch.cuda.is_available() else 'cpu'
    else:
        device = args.device
    
    # Setup logging
    setup_logging(args.output_dir)
    
    logging.info("🚀 Starting DC tokens training for MaIL")
    logging.info(f"Arguments: {args}")
    logging.info(f"Device: {device}")
    
    # Test functionality
    if not test_dc_functionality(device):
        logging.error("❌ Functionality test failed!")
        return 1
    
    if args.test_only:
        logging.info("✅ Test completed successfully!")
        return 0
    
    # TODO: Implement full training loop with real data
    logging.info("🔄 Full training implementation coming soon...")
    logging.info("For now, the functionality test demonstrates DC tokens integration")
    
    return 0


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
