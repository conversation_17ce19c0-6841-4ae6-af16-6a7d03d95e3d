#!/bin/bash

# Debug DC Training Script
set -e

echo "🔍 DC Training Debug Script"
echo "=========================="

# Set environment variables
export PYTHONPATH="/home/<USER>/work/my_mail:$PYTHONPATH"
export PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True
export HF_ENDPOINT=https://hf-mirror.com
export http_proxy=http://localhost:7890
export https_proxy=http://localhost:7890

echo "📝 Environment Setup:"
echo "   PYTHONPATH: $PYTHONPATH"
echo "   Working Directory: $(pwd)"
echo "   CUDA Device: $CUDA_VISIBLE_DEVICES"

# Check Python imports
echo ""
echo "🧪 Testing Python imports..."
python -c "
import sys
print('Python path:')
for i, p in enumerate(sys.path[:5]):
    print(f'  {i}: {p}')

try:
    from agents.dc_ddpm_agent import DiffusionAgent_DC
    print('✅ agents.dc_ddpm_agent imported successfully')
except ImportError as e:
    print(f'❌ Failed to import agents.dc_ddpm_agent: {e}')

try:
    from dataset.multi_task_dataset_goal import MultiTaskDataset
    print('✅ dataset.multi_task_dataset_goal imported successfully')
except ImportError as e:
    print(f'❌ Failed to import dataset.multi_task_dataset_goal: {e}')
"

# Check GPU memory
echo ""
echo "🖥️  GPU Memory Status:"
nvidia-smi --query-gpu=index,name,memory.used,memory.total --format=csv,noheader,nounits | grep "^1,"

# Check model file
MODEL_PATH="/home/<USER>/work/MaIL/logs/libero_goal/multi_task_diffusion_transformer/sweeps/disperse/2025-08-02/11-32-58/last_ddpm.pth"
echo ""
echo "📁 Checking model file:"
if [ -f "$MODEL_PATH" ]; then
    echo "   ✅ Model file exists: $MODEL_PATH"
    echo "   📊 File size: $(du -h "$MODEL_PATH" | cut -f1)"
else
    echo "   ❌ Model file not found: $MODEL_PATH"
    exit 1
fi

# Test configuration loading
echo ""
echo "⚙️  Testing configuration loading..."
python -c "
from omegaconf import OmegaConf
OmegaConf.register_new_resolver('add', lambda *numbers: sum(numbers))

try:
    cfg = OmegaConf.load('config/dc_training.yaml')
    print('✅ Configuration loaded successfully')
    print(f'   Task suite: {cfg.task_suite}')
    print(f'   Batch size: {cfg.train_batch_size}')
    print(f'   DC tokens: {cfg.n_dc_tokens}')
except Exception as e:
    print(f'❌ Configuration loading failed: {e}')
"

echo ""
echo "🎯 Ready to run training with these parameters:"
echo "   --pretrained $MODEL_PATH"
echo "   --dweight 1"
echo "   --debug (batch_size=2, steps=100)"
echo ""
echo "💡 Suggested command:"
echo "   CUDA_VISIBLE_DEVICES=1 ./run_dc_training.sh --pretrained $MODEL_PATH --dweight 1 --debug"