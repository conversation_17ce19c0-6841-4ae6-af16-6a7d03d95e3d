#!/usr/bin/env python3
"""
调试脚本：计算数据集的确切slice数量
"""

import os
import sys
import torch
from omegaconf import OmegaConf

# 添加MaIL到Python路径
sys.path.append('/home/<USER>/work/MaIL')

from dataset.multi_task_dataset_goal import MultiTaskDataset

def analyze_dataset_slices():
    """分析数据集的slice分布"""
    
    # 注册自定义解析器
    from omegaconf import OmegaConf
    OmegaConf.register_new_resolver("add", lambda x, y: int(x) + int(y))
    
    # 加载配置
    config_path = "config/dc_training.yaml"
    cfg = OmegaConf.load(config_path)
    
    print("🔍 数据集Slice分析")
    print("=" * 50)
    
    # 创建数据集
    print("📂 加载数据集...")
    dataset = MultiTaskDataset(
        data_directory=cfg.dataset_path,
        task_suite=cfg.task_suite,
        obs_keys=cfg.obs_keys,
        obs_modalities=cfg.observation.modalities,
        dataset_keys=cfg.dataset_keys,
        filter_by_attribute=None,
        padding=False,
        device="cpu",  # 使用CPU避免GPU内存问题
        obs_dim=cfg.obs_dim,
        action_dim=cfg.action_dim,
        state_dim=cfg.state_dim,
        max_len_data=cfg.max_len_data,
        window_size=cfg.window_size,
        num_data=cfg.trainset.num_data,
        data_aug=False,
        aug_factor=0.02
    )
    
    # 统计信息
    print(f"📊 数据集统计:")
    print(f"   任务套件: {cfg.task_suite}")
    print(f"   数据路径: {cfg.dataset_path}")
    print(f"   窗口大小: {cfg.window_size}")
    print(f"   每任务Demo数: {cfg.trainset.num_data}")
    print(f"   总Demo数: {dataset.num_data}")
    print(f"   总Slice数: {len(dataset)}")
    print(f"   最大Demo长度: {cfg.max_len_data}")
    
    # 分析每个demo的长度分布
    print(f"\n📈 Demo长度分布:")
    demo_lengths = []
    slice_counts = []
    
    for i in range(dataset.num_data):
        length = dataset.get_seq_length(i)
        demo_lengths.append(length)
        
        # 计算该demo贡献的slice数
        slice_count = max(0, length - cfg.window_size + 1)
        slice_counts.append(slice_count)
        
        if i < 10:  # 显示前10个demo的详细信息
            print(f"   Demo {i:2d}: 长度={length:3d}, Slice数={slice_count:3d}")
    
    print(f"   ...")
    print(f"   平均Demo长度: {sum(demo_lengths)/len(demo_lengths):.1f}")
    print(f"   最短Demo长度: {min(demo_lengths)}")
    print(f"   最长Demo长度: {max(demo_lengths)}")
    print(f"   平均每Demo Slice数: {sum(slice_counts)/len(slice_counts):.1f}")
    
    # 验证slice总数
    calculated_total = sum(slice_counts)
    actual_total = len(dataset)
    
    print(f"\n✅ 验证结果:")
    print(f"   计算的总Slice数: {calculated_total}")
    print(f"   实际总Slice数: {actual_total}")
    print(f"   是否匹配: {'✅' if calculated_total == actual_total else '❌'}")
    
    # 训练相关计算
    batch_size = cfg.dc_training.batch_size
    total_steps = cfg.dc_training.total_steps
    
    print(f"\n🚀 训练相关计算:")
    print(f"   批次大小: {batch_size}")
    print(f"   总训练步数: {total_steps}")
    print(f"   每轮epoch的batch数: {len(dataset) // batch_size}")
    print(f"   总样本数 (steps × batch_size): {total_steps * batch_size:,}")
    print(f"   数据集可支持的epoch数: {(total_steps * batch_size) // len(dataset):.1f}")
    
    return {
        'total_slices': len(dataset),
        'total_demos': dataset.num_data,
        'window_size': cfg.window_size,
        'avg_demo_length': sum(demo_lengths)/len(demo_lengths),
        'avg_slices_per_demo': sum(slice_counts)/len(slice_counts)
    }

if __name__ == "__main__":
    try:
        results = analyze_dataset_slices()
        print(f"\n🎯 关键结果:")
        for key, value in results.items():
            print(f"   {key}: {value}")
    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc()