# Diffusion Transformer完整评估指南

## 📋 概述

本指南介绍如何使用训练时的完全相同配置对你的Diffusion Transformer模型进行完整的libero_goal评估。

## 🎯 评估配置

### 模型信息
- **模型路径**: `MaIL/logs/libero_goal/multi_task_diffusion_transformer/sweeps/goal_ddpm_transformer_encdec/2025-07-22/21-53-31/eval_best_ddpm.pth`
- **配置文件**: `config/benchmark_libero_goal_diffusion_transformer.yaml`
- **模型参数**: 27,982,919 (约2800万参数)

### 评估设置 (严格按照训练配置)
- **任务套件**: libero_goal (10个目标导向任务)
- **每任务episodes**: 20轮 (vs 之前快速测试的3轮)
- **总episodes**: 200轮 (10任务 × 20轮)
- **最大步数**: 600步/轮
- **随机种子**: 4 (与训练时一致)
- **数据增强**: False
- **增强强度**: 0.1 (与训练时一致)
- **视频记录**: 启用，每任务最多3个视频

## 🚀 运行方式

### 方式1: 使用便捷脚本 (推荐)
```bash
cd /home/<USER>/work/MaIL
./start_full_evaluation.sh
```

### 方式2: 直接运行Python脚本
```bash
cd /home/<USER>/work/MaIL
python run_training_config_full_eval.py
```

## ⏱️ 预期时间

- **预计总时间**: 30-60分钟
- **每个任务**: 3-6分钟 (20轮 × 600步最大)
- **成功任务**: 更快完成 (平均86-400步)
- **失败任务**: 需要完整600步

## 📊 输出文件

### 1. 结果文件
- **文件名**: `training_config_full_evaluation.json`
- **内容**: 详细的成功率统计、每个episode结果
- **格式**: JSON，包含所有任务的详细数据

### 2. 日志文件
- **文件名**: `full_evaluation_YYYYMMDD_HHMMSS.log`
- **内容**: 完整的运行日志，包括实时进度

### 3. 视频文件
- **位置**: `rollouts/libero_goal/epoch_simulation/`
- **数量**: 最多30个视频 (10任务 × 3个/任务)
- **格式**: MP4，包含成功和失败案例

## 📈 预期结果分析

### 与快速测试对比
- **之前快速测试**: 11.1% 成功率 (3任务×3轮, seed=42)
- **完整评估**: ? % 成功率 (10任务×20轮, seed=4)

### 可能的性能差异原因
1. **Episodes数量**: 20轮 vs 3轮 (更可靠的统计)
2. **随机种子**: 4 vs 42 (与训练时一致)
3. **任务覆盖**: 10个任务 vs 3个任务 (更全面)
4. **数据增强强度**: 0.1 vs 0.02 (与训练时一致)

## 🎯 Libero Goal任务列表

你的模型将评估以下10个任务：

0. **open_the_middle_drawer_of_the_cabinet** - 打开柜子中间抽屉
1. **put_the_bowl_on_the_stove** - 把碗放到炉子上
2. **put_the_wine_bottle_on_top_of_the_cabinet** - 把酒瓶放到柜子顶部
3. **任务3** - (运行时显示具体描述)
4. **任务4** - (运行时显示具体描述)
5. **任务5** - (运行时显示具体描述)
6. **任务6** - (运行时显示具体描述)
7. **任务7** - (运行时显示具体描述)
8. **任务8** - (运行时显示具体描述)
9. **任务9** - (运行时显示具体描述)

## 📊 结果解读

### 成功率指标
- **整体成功率**: 所有200个episodes的总成功率
- **平均任务成功率**: 10个任务成功率的平均值
- **任务成功率标准差**: 任务间性能差异程度

### 性能分析
- **最佳任务**: 成功率最高的3个任务
- **最差任务**: 成功率最低的3个任务
- **平均步数**: 完成任务所需的平均步数

## 🛠️ 故障排除

### 常见问题
1. **环境问题**: 确保在mail conda环境中
2. **GPU内存**: 如果CUDA OOM，脚本会自动处理
3. **文件权限**: 确保有写入权限

### 监控进度
```bash
# 实时查看日志
tail -f full_evaluation_*.log

# 检查GPU使用情况
nvidia-smi

# 检查进程状态
ps aux | grep python
```

## 💡 优化建议

### 如果运行时间过长
1. 可以先运行几个任务测试
2. 检查GPU利用率
3. 确保没有其他程序占用GPU

### 如果成功率较低
1. 查看视频分析失败原因
2. 检查模型是否正确加载
3. 对比不同任务的表现差异

## 📞 运行状态检查

运行过程中你会看到：
```
🎯 开始评估任务 0
  📝 Episode 1/20
     ✅ 成功 - 86 步  或  ❌ 失败 - 600 步
📊 任务 0 完成: 成功率 X.X% (成功数/20)
```

完成后会显示：
```
🎉 训练配置完整评估完成!
📊 整体成功率: X.X%
🏆 表现最佳的3个任务: ...
📉 表现最差的3个任务: ...
```
