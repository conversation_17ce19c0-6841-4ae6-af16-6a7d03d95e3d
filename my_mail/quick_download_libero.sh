#!/bin/bash

# LIBERO数据集快速下载脚本
# 使用方法: ./quick_download_libero.sh [dataset_name]

set -e  # 遇到错误立即退出

echo "🚀 LIBERO数据集快速下载工具"
echo "================================"

# 检查参数
DATASET=${1:-"libero_goal"}  # 默认下载libero_goal
OUTPUT_DIR=${2:-"./datasets"}

echo "📦 数据集: $DATASET"
echo "📁 输出目录: $OUTPUT_DIR"
echo ""

# 检查Python环境
if ! command -v python &> /dev/null; then
    echo "❌ 错误: 未找到Python"
    exit 1
fi

# 检查依赖
echo "🔍 检查依赖..."
python -c "import requests, tqdm" 2>/dev/null || {
    echo "📦 安装依赖: requests, tqdm"
    pip install requests tqdm
}

# 推荐使用方式1: HuggingFace CLI (最稳定)
echo "💡 方式1: 使用HuggingFace CLI (推荐)"
if python download_libero_dataset_fixed.py -d "$DATASET" -o "$OUTPUT_DIR" --use-hf-cli; then
    echo "✅ 使用HuggingFace CLI下载成功!"
    exit 0
fi

echo "⚠️  HuggingFace CLI失败，尝试自定义下载器..."

# 方式2: 自定义下载器
echo "💡 方式2: 使用自定义下载器"
python download_libero_dataset_fixed.py -d "$DATASET" -o "$OUTPUT_DIR" -m hf-mirror -w 8

echo ""
echo "🎉 下载完成!"
echo "📁 数据文件位置: $OUTPUT_DIR/$DATASET/"
echo ""
echo "💡 使用提示:"
echo "   - 检查文件: ls -lh $OUTPUT_DIR/$DATASET/"
echo "   - 验证完整性: 确保demo.hdf5和init_states.hdf5都存在" 