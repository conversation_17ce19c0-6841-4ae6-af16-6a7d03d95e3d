#!/usr/bin/env python3
"""
加载预训练的Diffusion Transformer模型并在LIBERO环境中进行simulation
支持单任务和多任务评估，包含完整的视频记录和结果分析功能
"""

import os
import sys
import logging
import random
import argparse
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional, Tuple

import numpy as np
import torch
import hydra
from omegaconf import DictConfig, OmegaConf
import wandb
import yaml

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 禁用wandb避免日志错误
wandb.init(mode="disabled")

from libero.libero.envs import *
from libero.libero import benchmark
from libero.libero.envs import OffScreenRenderEnv
from utils.video_utils import VideoRecorder
import imgaug.parameters as iaa
import imgaug.parameters as iap


def set_seed_everywhere(seed: int):
    """设置随机种子以确保可重复性"""
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed_all(seed)
    np.random.seed(seed)
    random.seed(seed)


def process_image_input(img_tensor):
    """处理图像输入"""
    return img_tensor / 255.


class PretrainedModelSimulator:
    """
    预训练模型simulation评估器
    用于加载训练好的模型并在LIBERO环境中进行evaluation
    """
    
    def __init__(self, 
                 config_path: str,
                 model_weights_path: str,
                 task_suite: str = "libero_goal",
                 num_episodes: int = 20,
                 max_steps_per_episode: int = 600,
                 seed: int = 42,
                 device: str = "cuda",
                 save_video: bool = True,
                 max_videos_per_task: int = 3,
                 data_aug: bool = False,
                 aug_factor: float = 0.02,
                 verbose: bool = True):
        """
        初始化Simulator
        
        Args:
            config_path: 配置文件路径 (.yaml)
            model_weights_path: 模型权重文件路径 (.pth)
            task_suite: 任务套件名称 ("libero_goal", "libero_90"等)
            num_episodes: 每个任务的评估轮数
            max_steps_per_episode: 每轮最大步数
            seed: 随机种子
            device: 计算设备
            save_video: 是否保存视频
            max_videos_per_task: 每个任务最大视频数
            data_aug: 是否启用数据增强
            aug_factor: 数据增强强度
            verbose: 是否详细输出
        """
        self.config_path = Path(config_path)
        self.model_weights_path = Path(model_weights_path)
        self.task_suite = task_suite
        self.num_episodes = num_episodes
        self.max_steps_per_episode = max_steps_per_episode
        self.seed = seed
        self.device = device
        self.save_video = save_video
        self.max_videos_per_task = max_videos_per_task
        self.data_aug = data_aug
        self.aug_factor = aug_factor
        self.verbose = verbose
        
        # 设置随机种子
        set_seed_everywhere(seed)
        
        # 设置数据增强
        if self.data_aug:
            self.aug = iaa.arithmetic.ReplaceElementwise(
                iap.FromLowerResolution(iap.Binomial(self.aug_factor), size_px=8),
                [255]
            )
        
        # 设置日志
        logging.basicConfig(
            level=logging.INFO if verbose else logging.WARNING,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
        
        # 结果存储
        self.results = {}
        self.video_counter = {}
        
        # Agent和配置
        self.agent = None
        self.cfg = None
        
        self.logger.info("🚀 PretrainedModelSimulator初始化完成")
        
    def load_agent(self) -> None:
        """加载agent和配置"""
        self.logger.info(f"📂 从配置文件加载agent: {self.config_path}")
        
        # 验证文件存在
        if not self.config_path.exists():
            raise FileNotFoundError(f"配置文件不存在: {self.config_path}")
        if not self.model_weights_path.exists():
            raise FileNotFoundError(f"模型权重文件不存在: {self.model_weights_path}")
        
        # 加载配置
        config_dir = str(self.config_path.parent.absolute())
        config_name = self.config_path.stem
        
        with hydra.initialize_config_dir(config_dir=config_dir, version_base="1.1"):
            self.cfg = hydra.compose(config_name=config_name)
            
        self.logger.info(f"✅ 配置加载成功: {config_name}")
        
        # 实例化agent
        self.logger.info("🔧 实例化agent...")
        self.agent = hydra.utils.instantiate(self.cfg.agents)
        
        # 加载模型权重
        self.logger.info(f"📥 加载模型权重: {self.model_weights_path}")
        weights_dir = str(self.model_weights_path.parent)
        weights_name = self.model_weights_path.name
        
        self.agent.load_pretrained_model(
            weights_path=weights_dir,
            sv_name=weights_name
        )
        
        # 设置为评估模式
        self.agent.model.eval()
        
        self.logger.info("✅ Agent加载成功!")

        # 打印模型信息
        if self.verbose:
            total_params = sum(p.numel() for p in self.agent.model.parameters())
            trainable_params = sum(p.numel() for p in self.agent.model.parameters() if p.requires_grad)
            self.logger.info(f"📊 模型参数统计:")
            self.logger.info(f"   总参数: {total_params:,}")
            self.logger.info(f"   可训练参数: {trainable_params:,}")

    def load_complete_dc_model(self, dc_model_path: str) -> None:
        """
        直接从预训练的DC模型加载完整模型
        这是一个简化的接口，假设DC模型包含了完整的模型权重（包括原始权重和DC tokens）

        Args:
            dc_model_path: 完整的DC模型权重文件路径 (如 best_dc_model.pth)
        """
        self.logger.info(f"📂 从完整DC模型文件加载: {dc_model_path}")

        # 验证DC模型文件存在
        dc_model_path = Path(dc_model_path)
        if not dc_model_path.exists():
            raise FileNotFoundError(f"DC模型文件不存在: {dc_model_path}")

        # 从DC模型路径推断DC训练目录；找不到则回退到从checkpoint内置config加载
        dc_cfg = None
        found_hydra = False
        dc_training_dir = dc_model_path.parent
        while dc_training_dir != dc_training_dir.parent:
            hydra_path = dc_training_dir / ".hydra"
            if hydra_path.exists():
                found_hydra = True
                break
            dc_training_dir = dc_training_dir.parent

        if found_hydra:
            dc_config_path = dc_training_dir / ".hydra" / "config.yaml"
            if not dc_config_path.exists():
                raise FileNotFoundError(f"DC训练配置文件不存在: {dc_config_path}")

            # 加载DC训练配置
            self.logger.info(f"📖 读取DC训练配置: {dc_config_path}")
            with open(dc_config_path, 'r') as f:
                dc_config_raw = f.read()

            # 手动处理不支持的插值
            dc_config_raw = dc_config_raw.replace('${add:${obs_seq},4}', '9')  # obs_seq=5, 所以5+4=9
            dc_config_raw = dc_config_raw.replace('${add:${window_size}, 5}', '14')  # window_size=9, 所以9+5=14

            # 解析为字典
            dc_config = yaml.safe_load(dc_config_raw)

            # 转换为OmegaConf配置
            dc_cfg = OmegaConf.create(dc_config)
        else:
            # 回退：直接从 checkpoint 读取保存的 config（训练时已保存到state_dict中）
            self.logger.warning(
                f"⚠️  无法从路径向上找到.hydra目录，尝试从checkpoint中读取config: {dc_model_path}"
            )
            try:
                meta_checkpoint = torch.load(str(dc_model_path), map_location=self.device)
                if isinstance(meta_checkpoint, dict) and 'config' in meta_checkpoint:
                    dc_cfg = OmegaConf.create(meta_checkpoint['config'])
                    self.logger.info("✅ 已从checkpoint内置的config恢复DC配置")
                else:
                    raise FileNotFoundError("checkpoint中未包含'config'字段")
            except Exception as e:
                raise FileNotFoundError(
                    f"无法找到包含.hydra目录的DC训练目录，且从checkpoint读取config失败: {e}"
                )

        self.logger.info(f"✅ DC配置加载成功")

        # 实例化DC agent
        self.logger.info("🔧 实例化DC agent...")
        self.agent = hydra.utils.instantiate(dc_cfg.agents)
        self.logger.info(f"✅ DC Agent创建成功: {type(self.agent).__name__}")

        # 保存配置供后续使用
        self.cfg = dc_cfg

        # 加载完整的DC模型权重
        self.logger.info(f"📥 加载完整DC模型权重: {dc_model_path}")
        dc_checkpoint = torch.load(str(dc_model_path), map_location=self.device)

        # 获取模型状态字典
        if 'model_state_dict' in dc_checkpoint:
            dc_state_dict = dc_checkpoint['model_state_dict']
        elif 'state_dict' in dc_checkpoint:
            dc_state_dict = dc_checkpoint['state_dict']
        else:
            dc_state_dict = dc_checkpoint

        # 直接加载完整模型状态
        try:
            self.agent.model.load_state_dict(dc_state_dict, strict=False)
            self.logger.info("✅ 完整DC模型权重加载成功!")
        except Exception as e:
            self.logger.error(f"❌ 加载完整DC模型失败: {e}")
            self.logger.info("🔄 尝试仅加载匹配的权重...")
            
            # 如果直接加载失败，则只加载匹配的权重
            model_dict = self.agent.model.state_dict()
            filtered_dict = {}
            loaded_keys = []
            missing_keys = []
            
            for k, v in dc_state_dict.items():
                if k in model_dict:
                    if model_dict[k].shape == v.shape:
                        filtered_dict[k] = v
                        loaded_keys.append(k)
                    else:
                        self.logger.warning(f"⚠️  形状不匹配，跳过 {k}: 模型{model_dict[k].shape} vs 检查点{v.shape}")
                else:
                    missing_keys.append(k)
            
            # 检查模型中缺失的权重
            for k in model_dict.keys():
                if k not in dc_state_dict:
                    self.logger.warning(f"⚠️  模型中的权重在检查点中缺失: {k}")
            
            model_dict.update(filtered_dict)
            self.agent.model.load_state_dict(model_dict)
            
            self.logger.info(f"✅ 成功加载 {len(loaded_keys)} 个权重")
            if self.verbose:
                self.logger.info(f"📝 加载的权重数量统计:")
                dc_count = len([k for k in loaded_keys if 'dc_tokens' in k])
                other_count = len(loaded_keys) - dc_count
                self.logger.info(f"   DC相关权重: {dc_count}")
                self.logger.info(f"   其他权重: {other_count}")

        # 设置为评估模式
        self.agent.model.eval()

        self.logger.info("✅ 完整DC Agent加载成功!")

        # 打印模型信息
        if self.verbose:
            total_params = sum(p.numel() for p in self.agent.model.parameters())
            trainable_params = sum(p.numel() for p in self.agent.model.parameters() if p.requires_grad)
            self.logger.info(f"📊 完整DC模型参数统计:")
            self.logger.info(f"   总参数: {total_params:,}")
            self.logger.info(f"   可训练参数: {trainable_params:,}")

            # 统计DC相关参数
            dc_params = 0
            dc_param_names = []
            for name, param in self.agent.model.named_parameters():
                if 'dc_tokens' in name:
                    dc_params += param.numel()
                    dc_param_names.append(name)
                    self.logger.info(f"   DC参数 {name}: {param.shape}")
            self.logger.info(f"   DC相关参数总数: {dc_params:,}")

            # 打印checkpoint的额外信息
            if isinstance(dc_checkpoint, dict):
                if 'step' in dc_checkpoint:
                    self.logger.info(f"📊 模型训练步数: {dc_checkpoint['step']}")
                if 'eval_success_rate' in dc_checkpoint:
                    self.logger.info(f"📊 模型评估成功率: {dc_checkpoint['eval_success_rate']:.2%}")
                if 'eval_avg_reward' in dc_checkpoint:
                    self.logger.info(f"📊 模型平均奖励: {dc_checkpoint['eval_avg_reward']:.3f}")

        self.logger.info("🎉 完整DC模型加载完成!")

    def load_dc_agent(self, dc_model_path: str, original_model_path: str = None) -> None:
        """
        加载预训练的DC模型

        Args:
            dc_model_path: DC模型权重文件路径 (如 best_dc_model.pth)
            original_model_path: 原始模型路径，如果为None则从DC训练配置中获取
        """
        self.logger.info(f"📂 从DC模型文件加载agent: {dc_model_path}")

        # 验证DC模型文件存在
        dc_model_path = Path(dc_model_path)
        if not dc_model_path.exists():
            raise FileNotFoundError(f"DC模型文件不存在: {dc_model_path}")

        # 从DC模型路径推断DC训练目录
        # DC模型路径格式: .../logs/dc_training/2025-07-25/00-31-44/checkpoints/dc_training/libero_goal_xxx/best_dc_model.pth
        # 需要找到包含.hydra目录的上级目录
        dc_training_dir = dc_model_path.parent
        while dc_training_dir != dc_training_dir.parent:
            hydra_path = dc_training_dir / ".hydra"
            if hydra_path.exists():
                break
            dc_training_dir = dc_training_dir.parent
        else:
            raise FileNotFoundError(f"无法找到包含.hydra目录的DC训练目录，从路径: {dc_model_path}")

        dc_config_path = dc_training_dir / ".hydra" / "config.yaml"

        if not dc_config_path.exists():
            raise FileNotFoundError(f"DC训练配置文件不存在: {dc_config_path}")

        # 加载DC训练配置
        self.logger.info(f"📖 读取DC训练配置: {dc_config_path}")
        with open(dc_config_path, 'r') as f:
            dc_config_raw = f.read()

        # 手动处理不支持的插值
        dc_config_raw = dc_config_raw.replace('${add:${obs_seq},4}', '9')  # obs_seq=5, 所以5+4=9
        dc_config_raw = dc_config_raw.replace('${add:${window_size}, 5}', '14')  # window_size=9, 所以9+5=14

        # 解析为字典
        dc_config = yaml.safe_load(dc_config_raw)

        # 转换为OmegaConf配置
        dc_cfg = OmegaConf.create(dc_config)

        self.logger.info(f"✅ DC配置加载成功")

        # 实例化DC agent
        self.logger.info("🔧 实例化DC agent...")
        self.agent = hydra.utils.instantiate(dc_cfg.agents)
        self.logger.info(f"✅ DC Agent创建成功: {type(self.agent).__name__}")

        # 确定原始模型路径
        if original_model_path is None:
            original_model_path = dc_config.get('dc_training', {}).get('pretrained_model_path')
            if original_model_path is None:
                raise ValueError("无法从DC配置中获取原始模型路径，请手动指定original_model_path参数")

        # 保存配置供后续使用
        self.cfg = dc_cfg

        # 使用两步加载策略：先加载原始模型，再加载DC tokens
        self.logger.info("🔄 使用两步加载策略加载模型...")
        self._load_original_model_then_dc_tokens(original_model_path, str(dc_model_path))

        # 设置为评估模式
        self.agent.model.eval()

        self.logger.info("✅ DC Agent加载成功!")

        # 打印模型信息
        if self.verbose:
            total_params = sum(p.numel() for p in self.agent.model.parameters())
            trainable_params = sum(p.numel() for p in self.agent.model.parameters() if p.requires_grad)
            self.logger.info(f"📊 DC模型参数统计:")
            self.logger.info(f"   总参数: {total_params:,}")
            self.logger.info(f"   可训练参数: {trainable_params:,}")

            # 统计DC相关参数
            dc_params = 0
            for name, param in self.agent.model.named_parameters():
                if 'dc_tokens' in name:
                    dc_params += param.numel()
                    self.logger.info(f"   DC参数 {name}: {param.shape}")
            self.logger.info(f"   DC相关参数总数: {dc_params:,}")

    def _load_original_model_then_dc_tokens(self, original_model_path: str, dc_model_path: str) -> None:
        """
        两步加载策略：先加载原始预训练模型，再加载DC tokens

        Args:
            original_model_path: 原始预训练模型路径
            dc_model_path: DC模型checkpoint路径
        """
        self.logger.info("🔄 执行两步加载策略")

        # Step 1: 加载原始预训练模型
        self.logger.info(f"📥 步骤1: 加载原始模型 - {original_model_path}")
        if not Path(original_model_path).exists():
            raise FileNotFoundError(f"原始模型文件不存在: {original_model_path}")

        original_checkpoint = torch.load(original_model_path, map_location=self.device)

        # 获取原始模型状态字典
        if 'model_state_dict' in original_checkpoint:
            original_state_dict = original_checkpoint['model_state_dict']
        elif 'state_dict' in original_checkpoint:
            original_state_dict = original_checkpoint['state_dict']
        else:
            original_state_dict = original_checkpoint

        # 加载原始权重（排除DC相关参数）
        model_dict = self.agent.model.state_dict()
        filtered_dict = {}

        for k, v in original_state_dict.items():
            if k in model_dict and 'dc_tokens' not in k:
                filtered_dict[k] = v

        model_dict.update(filtered_dict)
        self.agent.model.load_state_dict(model_dict)
        self.logger.info("✅ 原始模型权重加载成功!")

        # Step 2: 加载DC tokens
        self.logger.info(f"📥 步骤2: 加载DC tokens - {dc_model_path}")
        if not Path(dc_model_path).exists():
            raise FileNotFoundError(f"DC模型文件不存在: {dc_model_path}")

        dc_checkpoint = torch.load(dc_model_path, map_location=self.device)

        # 获取DC模型状态字典
        if 'model_state_dict' in dc_checkpoint:
            dc_state_dict = dc_checkpoint['model_state_dict']
        elif 'state_dict' in dc_checkpoint:
            dc_state_dict = dc_checkpoint['state_dict']
        else:
            dc_state_dict = dc_checkpoint

        # 根据配置条件加载DC相关的tokens
        dc_keys_to_load = [
            'model.model.dc_tokens',                    # 编码器DC tokens
            'model.model.dc_t_tokens.weight',           # 编码器时间相关DC tokens
        ]
        
        # 根据配置决定是否加载解码器DC tokens
        if self.cfg.agents.model.model.get('use_decoder_dc', False):
            dc_keys_to_load.append('model.model.decoder_dc_tokens')
            self.logger.info("📝 配置启用解码器DC tokens，将尝试加载")
        else:
            self.logger.info("📝 配置未启用解码器DC tokens，跳过加载")
            
        if self.cfg.agents.model.model.get('use_decoder_dc_t', False):
            dc_keys_to_load.append('model.model.decoder_dc_t_tokens.weight')
            self.logger.info("📝 配置启用解码器DC时间tokens，将尝试加载")
        else:
            self.logger.info("📝 配置未启用解码器DC时间tokens，跳过加载")

        dc_loaded_count = 0
        for key in dc_keys_to_load:
            if key in dc_state_dict and key in model_dict:
                self.agent.model.state_dict()[key].copy_(dc_state_dict[key])
                self.logger.info(f"✅ 加载 {key}，形状: {dc_state_dict[key].shape}")
                dc_loaded_count += 1
            else:
                if key in dc_state_dict:
                    self.logger.warning(f"⚠️  键 {key} 在DC checkpoint中找到但模型中不存在")
                else:
                    self.logger.warning(f"⚠️  键 {key} 在DC checkpoint中未找到")

        self.logger.info(f"✅ 成功加载 {dc_loaded_count}/{len(dc_keys_to_load)} 种DC token类型")

        # 打印DC checkpoint的额外信息
        if self.verbose and isinstance(dc_checkpoint, dict):
            if 'step' in dc_checkpoint:
                self.logger.info(f"📊 DC模型训练步数: {dc_checkpoint['step']}")
            if 'eval_success_rate' in dc_checkpoint:
                self.logger.info(f"📊 DC模型评估成功率: {dc_checkpoint['eval_success_rate']:.2%}")
            if 'eval_avg_reward' in dc_checkpoint:
                self.logger.info(f"📊 DC模型平均奖励: {dc_checkpoint['eval_avg_reward']:.3f}")

        self.logger.info("🎉 原始模型 + DC tokens 加载完成!")
            
    def get_task_embedding(self, task_file_name: str):
        """获取任务嵌入"""
        task_emb = None
        if hasattr(self.agent, 'trainset') and hasattr(self.agent.trainset, 'tasks'):
            task_emb = self.agent.trainset.tasks.get(task_file_name, None)
            if task_emb is not None:
                self.logger.debug(f"🎯 找到任务嵌入: {task_file_name}")
            else:
                self.logger.warning(f"⚠️  未找到任务嵌入: {task_file_name}")
        return task_emb
        
    def run_single_episode(self, 
                          task_id: int, 
                          episode_idx: int, 
                          init_state_idx: int = 0) -> Dict:
        """
        执行单个任务的一个episode
        
        Args:
            task_id: 任务ID
            episode_idx: episode索引
            init_state_idx: 初始状态索引
            
        Returns:
            Dict: episode结果，包含success, steps, video_path等
        """
        # 获取任务套件和环境设置
        task_suite = benchmark.get_benchmark_dict()[self.task_suite]()
        task_bddl_file = task_suite.get_task_bddl_file_path(task_id)
        
        # 获取任务信息
        task_file_name = os.path.basename(task_bddl_file).split('.')[0]
        task_emb = self.get_task_embedding(task_file_name)
        
        # 获取初始状态
        init_states = task_suite.get_task_init_states(task_id)
        
        # 设置环境参数
        env_args = {
            "bddl_file_name": task_bddl_file,
            "camera_heights": 128,
            "camera_widths": 128
        }
        
        # 创建环境
        env = OffScreenRenderEnv(**env_args)
        
        # 重置agent和环境
        self.agent.reset()
        env.seed(self.seed)
        env.reset()
        obs = env.set_init_state(init_state=init_states[init_state_idx])
        
        # 初始化视频记录器
        should_record_video = (
            self.save_video and
            len([k for k in self.video_counter.keys() if str(task_id) in str(k)]) < self.max_videos_per_task
        )
        video_recorder = VideoRecorder(save_video=should_record_video)
        
        # 获取任务描述
        task_description = f"task_{task_id}"
        try:
            task_obj = task_suite.get_task(task_id)
            if hasattr(task_obj, 'language'):
                task_description = task_obj.language
            elif hasattr(task_obj, 'name'):
                task_description = task_obj.name
        except:
            pass
            
        # 执行初始物理模拟
        dummy_action = np.zeros(7)
        dummy_action[-1] = -1.0  # 打开夹爪
        for _ in range(5):
            obs, _, _, _ = env.step(dummy_action)
            if should_record_video:
                video_recorder.record_frame(obs, "agentview_image")
        
        # 执行episode
        episode_success = False
        total_steps = 0
        
        for step in range(self.max_steps_per_episode):
            total_steps = step + 1
            
            # 获取观察
            agentview_rgb = obs["agentview_image"]
            
            # 数据增强
            if self.data_aug:
                agentview_rgb = self.aug(image=agentview_rgb)
            
            # 构建状态
            if hasattr(self.cfg.agents, 'model') and hasattr(self.cfg.agents.model, 'obs_encoder'):
                # 使用eye_in_hand相机
                eye_in_hand_rgb = obs["robot0_eye_in_hand_image"]
                state = (agentview_rgb, eye_in_hand_rgb, task_emb)
            else:
                state = agentview_rgb
            
            # 预测动作
            try:
                action = self.agent.predict(state)[0]
            except Exception as e:
                self.logger.error(f"❌ 动作预测失败: {e}")
                break
                
            # 执行动作
            obs, reward, done, info = env.step(action)
            
            # 记录视频帧
            if should_record_video:
                video_recorder.record_frame(obs, "agentview_image")
            
            # 检查成功
            if reward == 1:
                episode_success = True
                break
        
        # 保存视频
        video_path = None
        if should_record_video and video_recorder.get_num_frames() > 0:
            try:
                episode_key = f"{task_id}_{episode_idx}"
                video_path = video_recorder.save_recorded_video(
                    task_suite_name=self.task_suite,
                    epoch="simulation",
                    episode_idx=episode_key,
                    success=episode_success,
                    task_description=task_description
                )
                if video_path:
                    self.video_counter[episode_key] = True
                    self.logger.info(f"📹 视频已保存: {task_id} - {'成功' if episode_success else '失败'}")
            except Exception as e:
                self.logger.warning(f"⚠️  视频保存失败: {e}")
        
        # 清理环境
        env.close()
        
        # 返回结果
        result = {
            'task_id': task_id,
            'episode_idx': episode_idx,
            'init_state_idx': init_state_idx,
            'success': episode_success,
            'steps': total_steps,
            'video_path': video_path,
            'task_description': task_description
        }
        
        return result
    
    def evaluate_task(self, task_id: int) -> Dict:
        """评估单个任务的所有episodes"""
        self.logger.info(f"🎯 开始评估任务 {task_id}")
        
        task_results = []
        success_count = 0
        
        for episode_idx in range(self.num_episodes):
            self.logger.info(f"  📝 Episode {episode_idx + 1}/{self.num_episodes}")
            
            result = self.run_single_episode(task_id, episode_idx, episode_idx % 10)
            task_results.append(result)
            
            if result['success']:
                success_count += 1
                
            self.logger.info(f"     {'✅ 成功' if result['success'] else '❌ 失败'} - {result['steps']} 步")
        
        success_rate = success_count / self.num_episodes
        
        task_summary = {
            'task_id': task_id,
            'episodes': task_results,
            'success_count': success_count,
            'total_episodes': self.num_episodes,
            'success_rate': success_rate,
            'avg_steps': np.mean([r['steps'] for r in task_results])
        }
        
        self.logger.info(f"📊 任务 {task_id} 完成: 成功率 {success_rate:.2%} ({success_count}/{self.num_episodes})")
        
        return task_summary
    
    def evaluate_all_tasks(self) -> Dict:
        """评估所有任务"""
        self.logger.info(f"🚀 开始完整评估 - 任务套件: {self.task_suite}")
        
        # 确定任务数量
        if self.task_suite == "libero_90":
            num_tasks = 90
        else:
            num_tasks = 10
            
        self.logger.info(f"📋 总任务数: {num_tasks}, 每任务episodes: {self.num_episodes}")
        
        all_results = {}
        total_success_count = 0
        total_episodes = 0
        
        for task_id in range(num_tasks):
            task_summary = self.evaluate_task(task_id)
            all_results[task_id] = task_summary
            
            total_success_count += task_summary['success_count']
            total_episodes += task_summary['total_episodes']
        
        # 计算整体统计
        overall_success_rate = total_success_count / total_episodes
        task_success_rates = [all_results[tid]['success_rate'] for tid in range(num_tasks)]
        
        final_summary = {
            'task_suite': self.task_suite,
            'num_tasks': num_tasks,
            'episodes_per_task': self.num_episodes,
            'total_episodes': total_episodes,
            'total_success_count': total_success_count,
            'overall_success_rate': overall_success_rate,
            'per_task_success_rates': task_success_rates,
            'avg_task_success_rate': np.mean(task_success_rates),
            'std_task_success_rate': np.std(task_success_rates),
            'task_results': all_results
        }
        
        self.results = final_summary
        
        # 打印最终结果
        self.logger.info("=" * 60)
        self.logger.info("🎉 评估完成!")
        self.logger.info(f"📊 整体成功率: {overall_success_rate:.2%} ({total_success_count}/{total_episodes})")
        self.logger.info(f"📈 平均任务成功率: {np.mean(task_success_rates):.2%} ± {np.std(task_success_rates):.2%}")
        self.logger.info(f"📹 保存的视频数量: {len(self.video_counter)}")
        self.logger.info("=" * 60)
        
        return final_summary
    
    def evaluate_specific_tasks(self, task_ids: List[int]) -> Dict:
        """评估指定的任务列表"""
        self.logger.info(f"🎯 评估指定任务: {task_ids}")
        
        specific_results = {}
        total_success_count = 0
        total_episodes = 0
        
        for task_id in task_ids:
            task_summary = self.evaluate_task(task_id)
            specific_results[task_id] = task_summary
            
            total_success_count += task_summary['success_count']
            total_episodes += task_summary['total_episodes']
        
        # 计算统计
        overall_success_rate = total_success_count / total_episodes
        task_success_rates = [specific_results[tid]['success_rate'] for tid in task_ids]
        
        summary = {
            'evaluated_tasks': task_ids,
            'num_tasks': len(task_ids),
            'episodes_per_task': self.num_episodes,
            'total_episodes': total_episodes,
            'total_success_count': total_success_count,
            'overall_success_rate': overall_success_rate,
            'per_task_success_rates': task_success_rates,
            'avg_task_success_rate': np.mean(task_success_rates),
            'task_results': specific_results
        }
        
        self.logger.info(f"📊 指定任务成功率: {overall_success_rate:.2%} ({total_success_count}/{total_episodes})")
        
        return summary
    
    def save_results(self, output_path: Optional[str] = None) -> str:
        """保存评估结果到文件"""
        if not self.results:
            self.logger.warning("⚠️  没有结果可保存，请先运行评估")
            return ""
            
        if output_path is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = f"simulation_results_{self.task_suite}_{timestamp}.json"
        
        import json
        
        # 确保所有numpy类型都能序列化
        def convert_numpy(obj):
            if isinstance(obj, np.ndarray):
                return obj.tolist()
            elif isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            return obj
            
        # 递归转换结果中的numpy类型
        def deep_convert(data):
            if isinstance(data, dict):
                return {k: deep_convert(v) for k, v in data.items()}
            elif isinstance(data, list):
                return [deep_convert(v) for v in data]
            else:
                return convert_numpy(data)
        
        converted_results = deep_convert(self.results)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(converted_results, f, indent=2, ensure_ascii=False)
            
        self.logger.info(f"💾 结果已保存到: {output_path}")
        return output_path


def main():
    """主函数 - 命令行接口"""
    parser = argparse.ArgumentParser(description="加载预训练模型进行LIBERO环境simulation")

    # 模型加载相关参数
    parser.add_argument("--config", type=str,
                       help="配置文件路径 (.yaml) - 普通模型必需")
    parser.add_argument("--weights", type=str,
                       help="模型权重文件路径 (.pth) - 普通模型必需")
    parser.add_argument("--dc_model", type=str,
                       help="DC模型权重文件路径 (如 best_dc_model.pth) - DC模型必需")
    parser.add_argument("--original_model", type=str,
                       help="原始模型路径 - DC模型可选，不指定则从DC配置中获取")
    parser.add_argument("--complete_dc_model", type=str,
                       help="完整DC模型权重文件路径 - 包含完整模型权重的DC模型文件")

    # 评估参数
    parser.add_argument("--task_suite", type=str, default="libero_goal",
                       choices=["libero_goal", "libero_90", "libero_10", "libero_spatial"],
                       help="任务套件")
    parser.add_argument("--num_episodes", type=int, default=20,
                       help="每个任务的评估轮数")
    parser.add_argument("--max_steps", type=int, default=600,
                       help="每轮最大步数")
    parser.add_argument("--seed", type=int, default=42,
                       help="随机种子")
    parser.add_argument("--device", type=str, default="cuda",
                       help="计算设备")
    parser.add_argument("--save_video", action="store_true", default=True,
                       help="保存视频")
    parser.add_argument("--max_videos_per_task", type=int, default=3,
                       help="每个任务最大视频数")
    parser.add_argument("--data_aug", action="store_true",
                       help="启用数据增强")
    parser.add_argument("--aug_factor", type=float, default=0.02,
                       help="数据增强强度")
    parser.add_argument("--tasks", type=str, default=None,
                       help="指定要评估的任务ID (逗号分隔，如 '0,1,2')")
    parser.add_argument("--output", type=str, default=None,
                       help="结果输出文件路径")
    parser.add_argument("--verbose", action="store_true", default=True,
                       help="详细输出")
    
    args = parser.parse_args()

    # 检查参数组合
    is_dc_model = args.dc_model is not None
    is_normal_model = args.config is not None and args.weights is not None
    is_complete_dc_model = args.complete_dc_model is not None

    if not (is_dc_model or is_normal_model or is_complete_dc_model):
        parser.error("必须指定以下参数组合之一:\n"
                    "  普通模型: --config 和 --weights\n"
                    "  DC模型: --dc_model (可选 --original_model)\n"
                    "  完整DC模型: --complete_dc_model")

    # 检查参数冲突
    model_types = [is_dc_model, is_normal_model, is_complete_dc_model]
    if sum(model_types) > 1:
        parser.error("只能指定一种模型类型的参数")

    # 创建simulator
    if is_complete_dc_model:
        # 完整DC模型模式 - 使用临时参数创建simulator
        simulator = PretrainedModelSimulator(
            config_path="dummy",  # 临时值，将被DC配置覆盖
            model_weights_path="dummy",  # 临时值，将被DC配置覆盖
            task_suite=args.task_suite,
            num_episodes=args.num_episodes,
            max_steps_per_episode=args.max_steps,
            seed=args.seed,
            device=args.device,
            save_video=args.save_video,
            max_videos_per_task=args.max_videos_per_task,
            data_aug=args.data_aug,
            aug_factor=args.aug_factor,
            verbose=args.verbose
        )

        # 加载完整DC模型
        print("🔧 使用完整DC模型模式")
        simulator.load_complete_dc_model(args.complete_dc_model)
    elif is_dc_model:
        # DC模型模式 - 使用临时参数创建simulator
        simulator = PretrainedModelSimulator(
            config_path="dummy",  # 临时值，将被DC配置覆盖
            model_weights_path="dummy",  # 临时值，将被DC配置覆盖
            task_suite=args.task_suite,
            num_episodes=args.num_episodes,
            max_steps_per_episode=args.max_steps,
            seed=args.seed,
            device=args.device,
            save_video=args.save_video,
            max_videos_per_task=args.max_videos_per_task,
            data_aug=args.data_aug,
            aug_factor=args.aug_factor,
            verbose=args.verbose
        )

        # 加载DC agent
        print("🔧 使用DC模型模式")
        simulator.load_dc_agent(args.dc_model, args.original_model)
    else:
        # 普通模型模式
        simulator = PretrainedModelSimulator(
            config_path=args.config,
            model_weights_path=args.weights,
            task_suite=args.task_suite,
            num_episodes=args.num_episodes,
            max_steps_per_episode=args.max_steps,
            seed=args.seed,
            device=args.device,
            save_video=args.save_video,
            max_videos_per_task=args.max_videos_per_task,
            data_aug=args.data_aug,
            aug_factor=args.aug_factor,
            verbose=args.verbose
        )

        # 加载普通agent
        print("🔧 使用普通模型模式")
        simulator.load_agent()
    
    # 运行评估
    if args.tasks:
        # 评估指定任务
        task_ids = [int(x.strip()) for x in args.tasks.split(',')]
        results = simulator.evaluate_specific_tasks(task_ids)
    else:
        # 评估所有任务
        results = simulator.evaluate_all_tasks()
    
    # 保存结果
    output_file = simulator.save_results(args.output)
    
    print(f"\n🎉 评估完成! 结果保存在: {output_file}")


if __name__ == "__main__":
    main()


def example_usage():
    """
    使用示例 - 展示三种不同的模型加载方式
    """
    
    print("=" * 60)
    print("🚀 PretrainedModelSimulator 使用示例")
    print("=" * 60)
    
    # 方式1: 加载普通预训练模型
    print("\n📝 方式1: 普通预训练模型")
    print("python load_and_simulate.py \\")
    print("  --config config/diffusion_transformer.yaml \\")
    print("  --weights checkpoints/best_model.pth \\")
    print("  --task_suite libero_goal \\")
    print("  --num_episodes 20")
    
    # 方式2: 加载DC模型 (需要原始模型)
    print("\n📝 方式2: DC模型 (分离加载)")
    print("python load_and_simulate.py \\")
    print("  --dc_model logs/dc_training/2025-07-28/17-55-17/checkpoints/best_dc_model.pth \\")
    print("  --original_model checkpoints/pretrained_model.pth \\")
    print("  --task_suite libero_goal \\")
    print("  --num_episodes 20")
    
    # 方式3: 加载完整DC模型 (新增的简化方式)
    print("\n📝 方式3: 完整DC模型 (一键加载) ✨ 新功能")
    print("python load_and_simulate.py \\")
    print("  --complete_dc_model logs/dc_training/2025-07-28/17-55-17/checkpoints/best_complete_dc_model.pth \\")
    print("  --task_suite libero_goal \\")
    print("  --num_episodes 20")
    
    # 代码示例
    print("\n💻 代码使用示例:")
    print("""
# 方式1: 普通模型
simulator = PretrainedModelSimulator(
    config_path="config/diffusion_transformer.yaml",
    model_weights_path="checkpoints/best_model.pth"
)
simulator.load_agent()

# 方式2: DC模型 (分离加载)
simulator = PretrainedModelSimulator(config_path="dummy", model_weights_path="dummy")
simulator.load_dc_agent(
    dc_model_path="logs/dc_training/.../best_dc_model.pth",
    original_model_path="checkpoints/pretrained_model.pth"
)

# 方式3: 完整DC模型 (一键加载) ✨ 推荐
simulator = PretrainedModelSimulator(config_path="dummy", model_weights_path="dummy")
simulator.load_complete_dc_model("logs/dc_training/.../best_complete_dc_model.pth")

# 运行评估
results = simulator.evaluate_all_tasks()
simulator.save_results()
""")
    
    print("\n🔧 三种方式的区别:")
    print("方式1 - 普通模型: 适用于标准的预训练diffusion模型")
    print("方式2 - DC分离加载: 需要分别指定原始模型和DC tokens，适用于DC训练过程中的中间checkpoint")
    print("方式3 - 完整DC模型: 包含完整权重的DC模型，一键加载，最简单易用 ✨")
    
    print("\n💡 推荐使用方式3 (load_complete_dc_model)，它:")
    print("  ✅ 只需要一个模型文件")
    print("  ✅ 自动读取训练配置")
    print("  ✅ 包含完整的模型权重")
    print("  ✅ 支持错误恢复和兼容性检查")
    print("=" * 60) 