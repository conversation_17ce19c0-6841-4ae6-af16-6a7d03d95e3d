#!/usr/bin/env python3
"""
LIBERO数据集下载脚本 (修复版)
支持国内镜像源、断点续传、多线程下载
基于官方LIBERO项目和HuggingFace数据集
"""

import os
import sys
import time
import json
import hashlib
import argparse
import requests
from pathlib import Path
from tqdm import tqdm
from concurrent.futures import ThreadPoolExecutor, as_completed
from urllib.parse import urljoin
import subprocess

# 国内镜像源配置
MIRROR_SOURCES = {
    'hf-mirror': 'https://hf-mirror.com/',
    'modelscope': 'https://www.modelscope.cn/models/',  
    'official': 'https://huggingface.co/',
}

# LIBERO数据集配置 (基于官方HuggingFace仓库)
LIBERO_DATASETS = {
    'libero_spatial': {
        'hf_repo': 'LIBERO/LIBERO_spatial',
        'hf_repo_type': 'dataset',  # 指定为数据集
        'files': [
            'LIBERO_spatial_demo.hdf5',
            'LIBERO_spatial_init_states.hdf5'
        ]
    },
    'libero_object': {
        'hf_repo': 'LIBERO/LIBERO_object', 
        'hf_repo_type': 'dataset',
        'files': [
            'LIBERO_object_demo.hdf5',
            'LIBERO_object_init_states.hdf5'
        ]
    },
    'libero_goal': {
        'hf_repo': 'LIBERO/LIBERO_goal',
        'hf_repo_type': 'dataset',
        'files': [
            'LIBERO_goal_demo.hdf5', 
            'LIBERO_goal_init_states.hdf5'
        ]
    },
    'libero_10': {
        'hf_repo': 'LIBERO/LIBERO_10',
        'hf_repo_type': 'dataset',
        'files': [
            'LIBERO_10_demo.hdf5',
            'LIBERO_10_init_states.hdf5'
        ]
    },
    'libero_90': {
        'hf_repo': 'LIBERO/LIBERO_90',
        'hf_repo_type': 'dataset',
        'files': [
            'LIBERO_90_demo.hdf5',
            'LIBERO_90_init_states.hdf5'
        ]
    }
}

class LiberoDownloader:
    def __init__(self, mirror='hf-mirror', output_dir='./datasets', max_workers=4):
        self.mirror = mirror
        self.base_url = MIRROR_SOURCES[mirror]
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        self.max_workers = max_workers
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        
    def get_file_url(self, repo, filename):
        """构建文件下载URL"""
        if self.mirror == 'modelscope':
            # ModelScope的URL格式不同
            return f"https://www.modelscope.cn/api/v1/datasets/{repo}/repo?Revision=master&FilePath={filename}"
        else:
            # HuggingFace格式
            return f"{self.base_url}{repo}/resolve/main/{filename}"
    
    def get_file_info(self, url):
        """获取文件信息（大小等）"""
        try:
            response = self.session.head(url, timeout=30, allow_redirects=True)
            if response.status_code == 200:
                return {
                    'size': int(response.headers.get('content-length', 0)),
                    'accept_ranges': response.headers.get('accept-ranges', '') == 'bytes'
                }
            else:
                print(f"HTTP状态码: {response.status_code}, URL: {url}")
        except Exception as e:
            print(f"获取文件信息失败: {e}")
        return {'size': 0, 'accept_ranges': False}
    
    def download_chunk(self, url, start, end, output_file, chunk_id):
        """下载文件片段"""
        headers = {'Range': f'bytes={start}-{end}'}
        try:
            with self.session.get(url, headers=headers, stream=True, timeout=60) as response:
                if response.status_code in [206, 200]:  # 206 Partial Content or 200 OK
                    with open(f"{output_file}.part{chunk_id}", 'wb') as f:
                        for chunk in response.iter_content(chunk_size=8192):
                            if chunk:
                                f.write(chunk)
                    return True
        except Exception as e:
            print(f"下载片段 {chunk_id} 失败: {e}")
        return False
    
    def merge_chunks(self, output_file, num_chunks):
        """合并文件片段"""
        with open(output_file, 'wb') as outfile:
            for i in range(num_chunks):
                chunk_file = f"{output_file}.part{i}"
                if os.path.exists(chunk_file):
                    with open(chunk_file, 'rb') as infile:
                        outfile.write(infile.read())
                    os.remove(chunk_file)
    
    def download_file_multithread(self, url, output_file, file_size):
        """多线程下载文件"""
        if file_size == 0:
            return self.download_file_simple(url, output_file)
        
        # 计算每个线程下载的字节数
        chunk_size = max(file_size // self.max_workers, 1024 * 1024)  # 至少1MB
        chunks = []
        
        for i in range(0, file_size, chunk_size):
            start = i
            end = min(i + chunk_size - 1, file_size - 1)
            chunks.append((start, end, i // chunk_size))
        
        print(f"使用 {len(chunks)} 个线程下载文件...")
        
        # 下载所有片段
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            futures = {
                executor.submit(self.download_chunk, url, start, end, output_file, chunk_id): chunk_id
                for start, end, chunk_id in chunks
            }
            
            completed = 0
            with tqdm(total=len(chunks), desc="下载进度", unit="chunk") as pbar:
                for future in as_completed(futures):
                    if future.result():
                        completed += 1
                    pbar.update(1)
        
        if completed == len(chunks):
            self.merge_chunks(output_file, len(chunks))
            return True
        return False
    
    def download_file_simple(self, url, output_file):
        """简单单线程下载"""
        try:
            with self.session.get(url, stream=True, timeout=60, allow_redirects=True) as response:
                if response.status_code == 200:
                    total_size = int(response.headers.get('content-length', 0))
                    
                    with open(output_file, 'wb') as f, tqdm(
                        desc=f"下载 {output_file.name}",
                        total=total_size,
                        unit='B',
                        unit_scale=True,
                        unit_divisor=1024,
                    ) as pbar:
                        for chunk in response.iter_content(chunk_size=8192):
                            if chunk:
                                f.write(chunk)
                                pbar.update(len(chunk))
                    return True
                else:
                    print(f"下载失败: HTTP {response.status_code}")
        except Exception as e:
            print(f"下载失败: {e}")
        return False
    
    def download_with_resume(self, url, output_file):
        """支持断点续传的下载"""
        # 检查是否已存在部分下载的文件
        if output_file.exists():
            existing_size = output_file.stat().st_size
            print(f"发现已存在文件 {output_file.name}，大小: {existing_size} bytes")
            
            # 获取远程文件信息
            file_info = self.get_file_info(url)
            remote_size = file_info['size']
            
            if remote_size == 0:
                print(f"无法获取远程文件大小，尝试直接下载")
                return self.download_file_simple(url, output_file)
                
            if existing_size == remote_size:
                print(f"文件 {output_file.name} 已完整下载，跳过")
                return True
            elif existing_size > remote_size:
                print(f"本地文件大于远程文件，重新下载")
                output_file.unlink()
            elif file_info['accept_ranges']:
                print(f"支持断点续传，从 {existing_size} 字节开始下载")
                return self.resume_download(url, output_file, existing_size, remote_size)
            else:
                print(f"不支持断点续传，重新下载")
                output_file.unlink()
        
        # 获取文件信息
        file_info = self.get_file_info(url)
        file_size = file_info['size']
        
        if file_size > 100 * 1024 * 1024 and file_info['accept_ranges']:  # 100MB以上使用多线程
            return self.download_file_multithread(url, output_file, file_size)
        else:
            return self.download_file_simple(url, output_file)
    
    def resume_download(self, url, output_file, start_byte, total_size):
        """断点续传下载"""
        headers = {'Range': f'bytes={start_byte}-'}
        
        try:
            with self.session.get(url, headers=headers, stream=True, timeout=60) as response:
                if response.status_code in [206, 200]:
                    with open(output_file, 'ab') as f, tqdm(
                        desc=f"续传 {output_file.name}",
                        initial=start_byte,
                        total=total_size,
                        unit='B',
                        unit_scale=True,
                        unit_divisor=1024,
                    ) as pbar:
                        for chunk in response.iter_content(chunk_size=8192):
                            if chunk:
                                f.write(chunk)
                                pbar.update(len(chunk))
                    return True
        except Exception as e:
            print(f"断点续传失败: {e}")
        return False
    
    def download_dataset(self, dataset_name, retries=3):
        """下载指定数据集"""
        if dataset_name not in LIBERO_DATASETS:
            print(f"未知数据集: {dataset_name}")
            print(f"可用数据集: {list(LIBERO_DATASETS.keys())}")
            return False
        
        dataset_info = LIBERO_DATASETS[dataset_name]
        dataset_dir = self.output_dir / dataset_name
        dataset_dir.mkdir(parents=True, exist_ok=True)
        
        print(f"\n开始下载数据集: {dataset_name}")
        print(f"使用镜像源: {self.mirror} ({self.base_url})")
        print(f"输出目录: {dataset_dir}")
        
        success_count = 0
        total_files = len(dataset_info['files'])
        
        for filename in dataset_info['files']:
            print(f"\n下载文件: {filename}")
            url = self.get_file_url(dataset_info['hf_repo'], filename)
            print(f"URL: {url}")
            output_file = dataset_dir / filename
            
            # 重试机制
            for attempt in range(retries):
                try:
                    if self.download_with_resume(url, output_file):
                        print(f"✓ {filename} 下载成功")
                        success_count += 1
                        break
                    else:
                        print(f"✗ {filename} 下载失败 (尝试 {attempt + 1}/{retries})")
                except Exception as e:
                    print(f"✗ {filename} 下载出错: {e} (尝试 {attempt + 1}/{retries})")
                
                if attempt < retries - 1:
                    print(f"等待 {2 ** attempt} 秒后重试...")
                    time.sleep(2 ** attempt)
        
        print(f"\n数据集 {dataset_name} 下载完成: {success_count}/{total_files} 文件成功")
        return success_count == total_files
    
    def download_all_datasets(self):
        """下载所有数据集"""
        print("开始下载所有LIBERO数据集...")
        
        success_datasets = []
        failed_datasets = []
        
        for dataset_name in LIBERO_DATASETS.keys():
            if self.download_dataset(dataset_name):
                success_datasets.append(dataset_name)
            else:
                failed_datasets.append(dataset_name)
        
        print(f"\n下载总结:")
        print(f"成功: {success_datasets}")
        print(f"失败: {failed_datasets}")
        
        return len(failed_datasets) == 0

def test_mirror_speed():
    """测试不同镜像源的速度"""
    print("测试镜像源速度...")
    
    # 使用一个较小的测试文件
    test_files = {
        'hf-mirror': 'LIBERO/LIBERO_goal/raw/main/README.md',
        'official': 'LIBERO/LIBERO_goal/raw/main/README.md'
    }
    
    speeds = {}
    for mirror, base_url in MIRROR_SOURCES.items():
        if mirror == 'modelscope':
            # ModelScope需要特殊处理，暂时跳过测试
            print(f"{mirror}: 跳过测试 (需要特殊API)")
            continue
            
        try:
            test_file = test_files.get(mirror, test_files['official'])
            url = urljoin(base_url, test_file)
            
            start_time = time.time()
            response = requests.head(url, timeout=10)
            end_time = time.time()
            
            if response.status_code == 200:
                speed = 1 / (end_time - start_time)
                speeds[mirror] = speed
                print(f"{mirror}: {speed:.2f} 请求/秒")
            else:
                print(f"{mirror}: 连接失败 (状态码: {response.status_code})")
        except Exception as e:
            print(f"{mirror}: 连接失败 ({e})")
    
    if speeds:
        fastest = max(speeds, key=speeds.get)
        print(f"\n推荐使用: {fastest}")
        return fastest
    return 'hf-mirror'

def download_with_hf_cli(dataset_name, output_dir, mirror_url=None):
    """使用huggingface-hub CLI下载（推荐方式）"""
    if dataset_name not in LIBERO_DATASETS:
        print(f"未知数据集: {dataset_name}")
        return False
    
    dataset_info = LIBERO_DATASETS[dataset_name]
    repo = dataset_info['hf_repo']
    repo_type = dataset_info['hf_repo_type']
    
    # 设置环境变量使用镜像
    env = os.environ.copy()
    if mirror_url and 'hf-mirror' in mirror_url:
        env['HF_ENDPOINT'] = mirror_url
        print(f"使用镜像源: {mirror_url}")
    
    try:
        cmd = [
            'huggingface-cli', 'download',
            repo,
            '--repo-type', repo_type,  # 指定仓库类型为dataset
            '--local-dir', str(Path(output_dir) / dataset_name),
            '--local-dir-use-symlinks', 'False'
        ]
        
        print(f"使用huggingface-hub CLI下载: {' '.join(cmd)}")
        subprocess.check_call(cmd, env=env)
        print(f"数据集 {dataset_name} 下载成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"huggingface-hub CLI下载失败: {e}")
        print("尝试不使用镜像源...")
        
        # 回退方案：不使用镜像源
        try:
            env_fallback = os.environ.copy()
            if 'HF_ENDPOINT' in env_fallback:
                del env_fallback['HF_ENDPOINT']
            
            print("使用官方源重试...")
            subprocess.check_call(cmd, env=env_fallback)
            print(f"数据集 {dataset_name} 下载成功 (使用官方源)")
            return True
        except subprocess.CalledProcessError as e2:
            print(f"官方源也失败: {e2}")
            return False
    except FileNotFoundError:
        print("huggingface-cli 未安装，请先安装: pip install huggingface_hub[cli]")
        return False

def install_hf_cli():
    """安装huggingface-hub CLI工具"""
    try:
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'huggingface_hub[cli]'])
        print("huggingface-hub CLI 安装成功")
        return True
    except subprocess.CalledProcessError:
        print("huggingface-hub CLI 安装失败")
        return False

def main():
    parser = argparse.ArgumentParser(description='LIBERO数据集下载工具')
    parser.add_argument('--dataset', '-d', 
                       choices=list(LIBERO_DATASETS.keys()) + ['all'],
                       default='all',
                       help='要下载的数据集 (默认: all)')
    parser.add_argument('--mirror', '-m',
                       choices=list(MIRROR_SOURCES.keys()),
                       default='hf-mirror',
                       help='镜像源 (默认: hf-mirror)')
    parser.add_argument('--output', '-o',
                       default='./datasets',
                       help='输出目录 (默认: ./datasets)')
    parser.add_argument('--workers', '-w',
                       type=int,
                       default=4,
                       help='下载线程数 (默认: 4)')
    parser.add_argument('--test-speed', '-t',
                       action='store_true',
                       help='测试镜像源速度')
    parser.add_argument('--use-hf-cli',
                       action='store_true',
                       help='使用huggingface-hub CLI下载 (推荐)')
    
    args = parser.parse_args()
    
    if args.test_speed:
        recommended_mirror = test_mirror_speed()
        if not hasattr(args, 'mirror') or not args.mirror:
            args.mirror = recommended_mirror
    
    # 推荐使用HF CLI
    print("💡 推荐使用 --use-hf-cli 参数获得更好的下载体验")
    
    if args.use_hf_cli:
        print("使用huggingface-hub CLI模式...")
        try:
            # 检查CLI是否可用
            subprocess.check_output(['huggingface-cli', '--version'])
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("huggingface-cli 未安装，正在安装...")
            if not install_hf_cli():
                print("CLI工具安装失败，使用自定义下载器")
                args.use_hf_cli = False
        
        if args.use_hf_cli:
            mirror_url = MIRROR_SOURCES.get(args.mirror)
            if args.dataset == 'all':
                for dataset in LIBERO_DATASETS.keys():
                    download_with_hf_cli(dataset, args.output, mirror_url)
            else:
                download_with_hf_cli(args.dataset, args.output, mirror_url)
            return
    
    # 使用自定义下载器
    downloader = LiberoDownloader(
        mirror=args.mirror,
        output_dir=args.output,
        max_workers=args.workers
    )
    
    if args.dataset == 'all':
        downloader.download_all_datasets()
    else:
        downloader.download_dataset(args.dataset)

if __name__ == '__main__':
    main() 