# 缓存文件
.cache/
__pycache__/
*.pyc
*.pyo
*.pyd

# 系统配置文件
.profile
.bashrc
.bash_history
.bash_logout
.netrc
.gitconfig
.wget-hsts

# 开发工具配置
.vscode/
.vscode-server/
.cursor-server/

# Python环境
.conda/
.condarc
.pip/
.local/

# AI工具配置
.claude/
.claude.json
.gemini/

# 云服务配置
.gsutil/
.nv/

# SSH配置
.ssh/

# 其他工具配置
.config/
.dotnet/
.keras/
.libero/
.mujoco/
.npm/
.specstory/
.swanlab/

# snap包
snap/

# 临时文件和备份
*~
*.tmp
*.bak

# 日志文件
*.log
logs/
wandb/

# 操作系统文件
.DS_Store
Thumbs.db

# IDE文件
.idea/
*.swp
*.swo

# 项目特定的日志和实验文件
MaIL/logs/
**/wandb/
wandb-metadata.json
wandb-summary.json
*.wandb

# 模型和检查点文件
*.pth
*.ckpt
*.pt

# 奇怪的版本号文件
=*

# 运行时文件
run_*.sh
test_*.py


# 文档草稿
*_README.md
env_*.txt
常见问题.txt

# 数据集文件（通常很大）
*.hdf5
*.h5
*.pkl
datasets/

# 实验配置的副本
config_backup/

# 外部依赖项目（避免嵌套Git仓库）
LIBERO/


# rollout得到的视频
*.mp4

#环境配置
*.yaml
*.json
