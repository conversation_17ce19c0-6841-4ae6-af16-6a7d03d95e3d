#!/usr/bin/env python3
"""
Train Diffusion Policy with Transformer on LIBERO Goal tasks with periodic environment evaluation.
"""
import os
import logging
import random

import hydra
import numpy as np
import multiprocessing as mp
import wandb
from omegaconf import DictConfig, OmegaConf
import torch
import torch.nn as nn
from tqdm import tqdm

from agents.utils import sim_framework_path


log = logging.getLogger(__name__)

print(f"CUDA Available: {torch.cuda.is_available()}")
if torch.cuda.is_available():
    print(f"CUDA Device: {torch.cuda.get_device_name()}")

OmegaConf.register_new_resolver(
     "add", lambda *numbers: sum(numbers)
)
torch.cuda.empty_cache()


def set_seed_everywhere(seed):
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed_all(seed)
    np.random.seed(seed)
    random.seed(seed)


def run_environment_evaluation(agent, cfg, epoch):
    """
    Run environment evaluation for the current agent at the given epoch.

    Args:
        agent: The trained agent
        cfg: Configuration object
        epoch: Current training epoch

    Returns:
        dict: Evaluation results including success rates
    """
    print(f"🧪 Starting environment evaluation at epoch {epoch}...")

    try:
        # Setup CPU assignment
        num_cpu = mp.cpu_count()
        cpu_set = list(range(num_cpu))
        assign_cpus = cpu_set[cfg.seed * cfg.n_cores:cfg.seed * cfg.n_cores + cfg.n_cores]

        # Create environment simulation
        env_sim = hydra.utils.instantiate(cfg.simulation)

        # Run evaluation and capture results
        print(f"🎮 Running evaluation on {cfg.task_suite} with {cfg.simulation.num_episode} episodes per task...")

        # 临时保存agent的配置，避免pickle问题
        original_cfg = getattr(agent, 'cfg', None)
        if hasattr(agent, 'cfg'):
            delattr(agent, 'cfg')

        try:
            success_results = env_sim.test_agent(agent, assign_cpus, epoch=epoch)
        finally:
            # 恢复agent的配置
            if original_cfg is not None:
                agent.cfg = original_cfg

        # Extract success rates from the simulation results
        eval_results = {
            "epoch": epoch,
            "evaluation_completed": True,
            "task_suite": cfg.task_suite,
            "average_success_rate": success_results.get("average_success_rate", 0.0) if success_results else 0.0,
            "task_success_rates": success_results.get("task_success_rates", []) if success_results else [],
            "total_episodes": success_results.get("total_episodes", 0) if success_results else 0,
            "successful_episodes": success_results.get("successful_episodes", 0) if success_results else 0
        }

        print(f"✅ Environment evaluation completed for epoch {epoch}")
        print(f"📊 Average success rate: {eval_results['average_success_rate']:.4f}")
        return eval_results

    except Exception as e:
        print(f"❌ Environment evaluation failed at epoch {epoch}: {e}")
        log.error(f"Environment evaluation failed at epoch {epoch}: {e}")
        return {
            "epoch": epoch,
            "evaluation_completed": False,
            "error": str(e)
        }


class TrainingWithEvaluation:
    """
    Custom training class that integrates environment evaluation during training.
    """
    
    def __init__(self, agent, cfg):
        self.agent = agent
        self.cfg = cfg
        self.env_eval_freq = getattr(cfg, 'env_eval_freq', cfg.eval_every_n_epochs)  # Default to same as validation eval
        
    def enhanced_train_vision_agent(self):
        """
        Enhanced training method that includes periodic environment evaluation.
        """
        print(f"🚀 开始训练，总共 {self.agent.epoch} 个epoch")
        print(f"📊 数据集大小: {len(self.agent.train_dataloader)} 批次")
        print(f"🌍 环境评估频率: 每 {self.env_eval_freq} 个epoch")

        best_success_rate = 0.0  # 跟踪最佳成功率

        # 添加epoch循环和进度条
        for num_epoch in tqdm(range(self.agent.epoch), desc="🎯 训练进度", unit="epoch"):

            # Training loop for this epoch
            epoch_losses = []

            # 为每个epoch内的batch添加进度条
            batch_iterator = tqdm(
                self.agent.train_dataloader,
                desc=f"Epoch {num_epoch+1}/{self.agent.epoch}",
                leave=False,
                unit="batch"
            )

            for batch_idx, data in enumerate(batch_iterator):
                # MultiTaskDataset returns: agentview_rgb, eye_in_hand_rgb, action, task_emb
                # Following the same pattern as the original agent.train_vision_agent()
                bp_imgs, inhand_imgs, action, goal_imgs = data

                bp_imgs = bp_imgs.to(self.agent.device)
                inhand_imgs = inhand_imgs.to(self.agent.device)
                goal_imgs = goal_imgs.to(self.agent.device)

                action = self.agent.scaler.scale_output(action)
                action = action[:, self.agent.obs_seq_len - 1:, :].contiguous()
                bp_imgs = bp_imgs[:, :self.agent.obs_seq_len].contiguous()
                inhand_imgs = inhand_imgs[:, :self.agent.obs_seq_len].contiguous()

                state = (bp_imgs, inhand_imgs, goal_imgs)
                batch_loss = self.agent.train_step(state, action)

                epoch_losses.append(batch_loss.item())

                # 更新批次进度条显示当前loss
                batch_iterator.set_postfix({
                    'Loss': f'{batch_loss.item():.4f}',
                    'Avg_Loss': f'{np.mean(epoch_losses):.4f}'
                })

                # wandb logging
                wandb.log({
                    "train_loss": batch_loss.item(),
                    "epoch": num_epoch,
                    "batch": batch_idx
                })

            # epoch结束后的统计
            avg_epoch_loss = np.mean(epoch_losses)
            print(f"📈 Epoch {num_epoch+1}/{self.agent.epoch} 完成 - 平均损失: {avg_epoch_loss:.4f}")

            # 记录epoch级别的指标
            wandb.log({
                "epoch_avg_loss": avg_epoch_loss,
                "epoch": num_epoch
            })

            # 在epoch完成后检查是否需要进行环境评估
            # 只有当 (epoch+1) 是 env_eval_freq 的倍数时才评估，且不在第0个epoch评估
            if (num_epoch + 1) % self.env_eval_freq == 0 and num_epoch >= (self.env_eval_freq - 1):
                print(f"\n🌍 Running environment evaluation after epoch {num_epoch} (every {self.env_eval_freq} epochs)")
                eval_results = run_environment_evaluation(self.agent, self.cfg, num_epoch + 1)

                # 获取成功率并上传到wandb
                if eval_results["evaluation_completed"]:
                    # 从仿真结果中获取成功率
                    current_success_rate = eval_results.get("average_success_rate", 0.0)

                    # 上传到wandb
                    wandb.log({
                        f"env_eval_completed_epoch": num_epoch + 1,
                        f"env_eval_success": True,
                        f"current_success_rate": current_success_rate,
                        f"epoch": num_epoch + 1
                    })

                    print(f"📊 Epoch {num_epoch + 1} - Success Rate: {current_success_rate:.4f}")

                    # 检查是否是最佳成功率
                    if current_success_rate > best_success_rate:
                        best_success_rate = current_success_rate

                        # 保存最佳模型
                        best_model_path = f"{self.agent.working_dir}/best_success_rate_model.pth"
                        torch.save({
                            'epoch': num_epoch + 1,
                            'model_state_dict': self.agent.model.state_dict(),
                            'success_rate': best_success_rate,
                            'optimizer_state_dict': self.agent.optimizer.state_dict() if hasattr(self.agent, 'optimizer') else None
                        }, best_model_path)

                        # 记录到wandb
                        wandb.log({
                            f"best_success_rate": best_success_rate,
                            f"best_success_rate_epoch": num_epoch + 1
                        })

                        print(f"🏆 New best success rate: {best_success_rate:.4f} at epoch {num_epoch + 1}")
                        print(f"💾 Best model saved to: {best_model_path}")
                        log.info(f'New best success rate: {best_success_rate:.4f}. Model saved!')

                    print(f"📈 Current best success rate: {best_success_rate:.4f}")

                else:
                    wandb.log({
                        f"env_eval_completed_epoch": num_epoch + 1,
                        f"env_eval_success": False,
                        f"env_eval_error": eval_results.get("error", "Unknown error")
                    })
                    print(f"❌ Environment evaluation failed at epoch {num_epoch + 1}")

                # epoch结束后的统计
                avg_epoch_loss = np.mean(epoch_losses)
                print(f"📈 Epoch {num_epoch+1}/{self.agent.epoch} 完成 - 平均损失: {avg_epoch_loss:.4f}")

                # 记录epoch级别的指标
                wandb.log({
                    "epoch_avg_loss": avg_epoch_loss,
                    "epoch": num_epoch
                })

        print("✅ 训练完成!")

        # Store final model
        if hasattr(self.agent, 'working_dir') and hasattr(self.agent, 'last_model_name'):
            self.agent.store_model_weights(self.agent.working_dir, sv_name=self.agent.last_model_name)
            print(f"💾 模型已保存到: {self.agent.working_dir}/{self.agent.last_model_name}")

        log.info("Training done!")

    def train_with_periodic_eval(self):
        """
        Modified training loop that includes periodic environment evaluation.
        """
        print(f"🎯 Starting training with periodic environment evaluation...")
        print(f"📊 Environment evaluation frequency: every {self.env_eval_freq} epochs")

        # Run the enhanced training directly
        self.enhanced_train_vision_agent()


@hydra.main(config_path="config", config_name="benchmark_libero_goal_diffusion_transformer.yaml", version_base="1.1")
def main(cfg: DictConfig) -> None:
    print("=" * 50)
    print("🚀 Training Diffusion Policy + Transformer on LIBERO Goal with Periodic Evaluation")
    print("=" * 50)
    
    set_seed_everywhere(cfg.seed)
    
    print(f"📝 Configuration:")
    print(f"   Agent: {cfg.agent_name}")
    print(f"   Task Suite: {cfg.task_suite}")
    print(f"   Diffusion Steps: {cfg.diff_steps}")
    print(f"   Encoder Layers: {cfg.encoder_n_layer}")
    print(f"   Decoder Layers: {cfg.decoder_n_layer}")
    print(f"   Batch Size: {cfg.train_batch_size}")
    print(f"   Epochs: {cfg.epoch}")
    print(f"   Validation Eval Freq: {cfg.eval_every_n_epochs}")
    print(f"   Environment Eval Freq: {getattr(cfg, 'env_eval_freq', cfg.eval_every_n_epochs)}")
    print(f"   Device: {cfg.device}")
    print()

    # Check if wandb config is valid
    # 启用wandb
    wandb_enabled = True
    
    print(f"🔍 Wandb config check: {wandb_enabled} (已启用)")
    if hasattr(cfg, 'wandb'):
        print(f"   Entity: {getattr(cfg.wandb, 'entity', 'Not found')}")
        print(f"   Project: {getattr(cfg.wandb, 'project', 'Not found')}")
    
    # init wandb logger and config from hydra path
    if wandb_enabled:
        try:
            wandb_config = OmegaConf.to_container(cfg, resolve=True, throw_on_missing=True)

            # Generate run name
            run_name = None
            if hasattr(cfg.wandb, 'run_name') and cfg.wandb.run_name is not None:
                run_name = cfg.wandb.run_name
            else:
                # Auto-generate run name with timestamp and key parameters
                from datetime import datetime
                timestamp = datetime.now().strftime("%m%d_%H%M")
                run_name = f"{cfg.agent_name}_{cfg.task_suite}_ep{cfg.epoch}_eval{cfg.env_eval_freq}_{timestamp}"

            # 如果entity为null，则不传递entity参数
            init_kwargs = {
                "project": cfg.wandb.project,
                "group": cfg.group,
                "name": run_name,
                "mode": "online",
                "config": wandb_config,
                "tags": [cfg.agent_name, cfg.task_suite, f"epochs_{cfg.epoch}", f"eval_freq_{cfg.env_eval_freq}"]
            }
            if cfg.wandb.entity is not None:
                init_kwargs["entity"] = cfg.wandb.entity

            run = wandb.init(**init_kwargs)
            print(f"✅ Wandb initialized successfully with run name: {run_name}")
        except Exception as e:
            print(f"⚠️  Wandb initialization failed: {e}")
            print("📊 Continuing without wandb logging")
            wandb_enabled = False
            # Initialize wandb in disabled mode
            try:
                wandb.init(mode="disabled", project="dummy")
            except:
                pass  # If wandb init fails, continue without it
    else:
        print("📊 Wandb disabled - continuing without logging")
        # Initialize wandb in disabled mode to avoid errors
        try:
            wandb.init(mode="disabled", project="dummy")
        except:
            pass  # If wandb init fails, continue without it

    print("🔧 Instantiating agent...")
    agent = hydra.utils.instantiate(cfg.agents)
    print(f"✅ Agent created: {type(agent).__name__}")
    
    # Create training with evaluation wrapper
    trainer = TrainingWithEvaluation(agent, cfg)
    
    # Start enhanced training
    trainer.train_with_periodic_eval()
    print("✅ Training with periodic evaluation completed!")

    # Final evaluation
    print("🧪 Starting final evaluation...")
    run_environment_evaluation(agent, cfg, epoch="final")
    print("✅ Final evaluation completed!")

    log.info("🎉 Training and evaluation finished successfully!")
    
    if wandb_enabled:
        wandb.finish()

    print("=" * 50)
    print("🏁 All done! Check the logs and videos for results.")
    print("=" * 50)


if __name__ == "__main__":
    main()
